# Teams Feature Implementation Plan

## Assessment Summary

### Current Architecture Analysis

**Technology Stack:**
- **Frontend**: React + TypeScript, Vite, Tailwind CSS, Radix UI components
- **Backend**: Supabase (PostgreSQL + Auth + RLS)
- **State Management**: React Context + TanStack Query
- **Authentication**: Supabase Auth with email/password

**Current Data Model:**
- **Users**: Managed by Supabase Auth (`auth.users`) with profiles in `public.profiles`
- **Requests**: Each request belongs to a single user (`user_id` foreign key)
- **Index/Actes**: Linked to requests, indirectly owned by users through RLS policies
- **Row Level Security**: All data access is filtered by `auth.uid()` matching `user_id`

**Key Findings:**
1. **Single-user design**: All data is strictly isolated per user via RLS policies
2. **Direct ownership**: Requests have a single `user_id` owner
3. **No sharing mechanisms**: No existing infrastructure for multi-user access
4. **Authentication**: Simple email/password with profile management
5. **Data access**: All queries filtered by current user's ID

---

## Phase 1: Database Schema Changes

### 1.1 New Tables Required

```sql
-- Teams table
CREATE TABLE IF NOT EXISTS "public"."teams" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "name" "text" NOT NULL,
    "description" "text",
    "created_by" "uuid" NOT NULL REFERENCES "auth"."users"("id") ON DELETE CASCADE,
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "updated_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "is_active" boolean DEFAULT true NOT NULL,
    "settings" "jsonb" DEFAULT '{}'::jsonb,
    CONSTRAINT "teams_pkey" PRIMARY KEY ("id")
);

-- Team memberships with roles
CREATE TYPE "public"."team_role_enum" AS ENUM (
    'owner',
    'admin', 
    'member',
    'viewer'
);

CREATE TABLE IF NOT EXISTS "public"."team_memberships" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "team_id" "uuid" NOT NULL REFERENCES "public"."teams"("id") ON DELETE CASCADE,
    "user_id" "uuid" NOT NULL REFERENCES "auth"."users"("id") ON DELETE CASCADE,
    "role" "public"."team_role_enum" NOT NULL DEFAULT 'member',
    "invited_by" "uuid" REFERENCES "auth"."users"("id"),
    "joined_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "is_active" boolean DEFAULT true NOT NULL,
    CONSTRAINT "team_memberships_pkey" PRIMARY KEY ("id"),
    CONSTRAINT "team_memberships_unique" UNIQUE ("team_id", "user_id")
);

-- Team invitations
CREATE TYPE "public"."invitation_status_enum" AS ENUM (
    'pending',
    'accepted',
    'declined',
    'expired'
);

CREATE TABLE IF NOT EXISTS "public"."team_invitations" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "team_id" "uuid" NOT NULL REFERENCES "public"."teams"("id") ON DELETE CASCADE,
    "email" "text" NOT NULL,
    "role" "public"."team_role_enum" NOT NULL DEFAULT 'member',
    "invited_by" "uuid" NOT NULL REFERENCES "auth"."users"("id") ON DELETE CASCADE,
    "status" "public"."invitation_status_enum" NOT NULL DEFAULT 'pending',
    "token" "text" NOT NULL UNIQUE,
    "expires_at" timestamp with time zone NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "updated_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    CONSTRAINT "team_invitations_pkey" PRIMARY KEY ("id")
);
```

### 1.2 Schema Modifications

```sql
-- Add team_id to requests table
ALTER TABLE "public"."requests" 
ADD COLUMN "team_id" "uuid" REFERENCES "public"."teams"("id") ON DELETE SET NULL;

-- Add team_id to profiles for default team
ALTER TABLE "public"."profiles"
ADD COLUMN "default_team_id" "uuid" REFERENCES "public"."teams"("id") ON DELETE SET NULL;

-- Create indexes for performance
CREATE INDEX "idx_team_memberships_team_id" ON "public"."team_memberships"("team_id");
CREATE INDEX "idx_team_memberships_user_id" ON "public"."team_memberships"("user_id");
CREATE INDEX "idx_team_invitations_team_id" ON "public"."team_invitations"("team_id");
CREATE INDEX "idx_team_invitations_email" ON "public"."team_invitations"("email");
CREATE INDEX "idx_team_invitations_token" ON "public"."team_invitations"("token");
CREATE INDEX "idx_requests_team_id" ON "public"."requests"("team_id");
```

### 1.3 Row Level Security (RLS) Policies

```sql
-- Enable RLS on new tables
ALTER TABLE "public"."teams" ENABLE ROW LEVEL SECURITY;
ALTER TABLE "public"."team_memberships" ENABLE ROW LEVEL SECURITY;
ALTER TABLE "public"."team_invitations" ENABLE ROW LEVEL SECURITY;

-- Teams policies
CREATE POLICY "Users can view teams they belong to" ON "public"."teams"
FOR SELECT USING (
    EXISTS (
        SELECT 1 FROM "public"."team_memberships"
        WHERE "team_id" = "teams"."id"
        AND "user_id" = "auth"."uid"()
        AND "is_active" = true
    )
);

CREATE POLICY "Team owners and admins can update teams" ON "public"."teams"
FOR UPDATE USING (
    EXISTS (
        SELECT 1 FROM "public"."team_memberships"
        WHERE "team_id" = "teams"."id"
        AND "user_id" = "auth"."uid"()
        AND "role" IN ('owner', 'admin')
        AND "is_active" = true
    )
);

CREATE POLICY "Users can create teams" ON "public"."teams"
FOR INSERT WITH CHECK ("created_by" = "auth"."uid"());

-- Team memberships policies
CREATE POLICY "Users can view memberships for their teams" ON "public"."team_memberships"
FOR SELECT USING (
    EXISTS (
        SELECT 1 FROM "public"."team_memberships" tm2
        WHERE tm2."team_id" = "team_memberships"."team_id"
        AND tm2."user_id" = "auth"."uid"()
        AND tm2."is_active" = true
    )
);

CREATE POLICY "Team admins can manage memberships" ON "public"."team_memberships"
FOR ALL USING (
    EXISTS (
        SELECT 1 FROM "public"."team_memberships" tm2
        WHERE tm2."team_id" = "team_memberships"."team_id"
        AND tm2."user_id" = "auth"."uid"()
        AND tm2."role" IN ('owner', 'admin')
        AND tm2."is_active" = true
    )
);

-- Team invitations policies
CREATE POLICY "Team members can view invitations for their teams" ON "public"."team_invitations"
FOR SELECT USING (
    EXISTS (
        SELECT 1 FROM "public"."team_memberships"
        WHERE "team_id" = "team_invitations"."team_id"
        AND "user_id" = "auth"."uid"()
        AND "is_active" = true
    )
);

CREATE POLICY "Team admins can manage invitations" ON "public"."team_invitations"
FOR ALL USING (
    EXISTS (
        SELECT 1 FROM "public"."team_memberships"
        WHERE "team_id" = "team_invitations"."team_id"
        AND "user_id" = "auth"."uid"()
        AND "role" IN ('owner', 'admin')
        AND "is_active" = true
    )
);
```

### 1.4 Updated RLS Policies for Existing Tables

```sql
-- Update requests policies to include team access
DROP POLICY IF EXISTS "Users can view their own requests" ON "public"."requests";
CREATE POLICY "Users can view their own requests or team requests" ON "public"."requests"
FOR SELECT USING (
    "user_id" = "auth"."uid"()
    OR
    EXISTS (
        SELECT 1 FROM "public"."team_memberships"
        WHERE "team_id" = "requests"."team_id"
        AND "user_id" = "auth"."uid"()
        AND "is_active" = true
    )
);

-- Similar updates for other request policies
DROP POLICY IF EXISTS "Users can update their own requests" ON "public"."requests";
CREATE POLICY "Users can update their own requests or team requests" ON "public"."requests"
FOR UPDATE USING (
    "user_id" = "auth"."uid"()
    OR
    EXISTS (
        SELECT 1 FROM "public"."team_memberships"
        WHERE "team_id" = "requests"."team_id"
        AND "user_id" = "auth"."uid"()
        AND "role" IN ('owner', 'admin', 'member')
        AND "is_active" = true
    )
);
```

### 1.5 Database Functions for Team Management

```sql
-- Function to create a team and add creator as owner
CREATE OR REPLACE FUNCTION "public"."create_team_with_owner"(
    "team_name" "text",
    "team_description" "text" DEFAULT NULL
) RETURNS "uuid"
LANGUAGE "plpgsql" SECURITY DEFINER
AS $$
DECLARE
    new_team_id uuid;
BEGIN
    -- Create the team
    INSERT INTO "public"."teams" ("name", "description", "created_by")
    VALUES (team_name, team_description, auth.uid())
    RETURNING "id" INTO new_team_id;

    -- Add creator as owner
    INSERT INTO "public"."team_memberships" ("team_id", "user_id", "role", "invited_by")
    VALUES (new_team_id, auth.uid(), 'owner', auth.uid());

    RETURN new_team_id;
END;
$$;

-- Function to accept team invitation
CREATE OR REPLACE FUNCTION "public"."accept_team_invitation"(
    "invitation_token" "text"
) RETURNS "boolean"
LANGUAGE "plpgsql" SECURITY DEFINER
AS $$
DECLARE
    invitation_record record;
    user_email text;
BEGIN
    -- Get current user email
    SELECT email INTO user_email FROM auth.users WHERE id = auth.uid();

    -- Get invitation details
    SELECT * INTO invitation_record
    FROM "public"."team_invitations"
    WHERE "token" = invitation_token
    AND "status" = 'pending'
    AND "expires_at" > now()
    AND "email" = user_email;

    IF NOT FOUND THEN
        RETURN false;
    END IF;

    -- Create team membership
    INSERT INTO "public"."team_memberships" ("team_id", "user_id", "role", "invited_by")
    VALUES (invitation_record.team_id, auth.uid(), invitation_record.role, invitation_record.invited_by);

    -- Update invitation status
    UPDATE "public"."team_invitations"
    SET "status" = 'accepted', "updated_at" = now()
    WHERE "id" = invitation_record.id;

    RETURN true;
END;
$$;
```

---

## Phase 2: Backend API Services

### 2.1 Team Service Functions

**File: `src/lib/services/teamService.ts`**

```typescript
import { supabase } from '@/lib/supabase';
import { Database } from '@/integrations/supabase/types';

type Team = Database['public']['Tables']['teams']['Row'];
type TeamInsert = Database['public']['Tables']['teams']['Insert'];
type TeamMembership = Database['public']['Tables']['team_memberships']['Row'];
type TeamInvitation = Database['public']['Tables']['team_invitations']['Row'];

export class TeamService {
  // Create a new team
  static async createTeam(name: string, description?: string): Promise<{ data: Team | null; error: any }> {
    try {
      const { data, error } = await supabase.rpc('create_team_with_owner', {
        team_name: name,
        team_description: description
      });

      if (error) throw error;

      // Fetch the created team
      const { data: team, error: fetchError } = await supabase
        .from('teams')
        .select('*')
        .eq('id', data)
        .single();

      return { data: team, error: fetchError };
    } catch (error) {
      return { data: null, error };
    }
  }

  // Get user's teams
  static async getUserTeams(): Promise<{ data: Team[] | null; error: any }> {
    try {
      const { data, error } = await supabase
        .from('teams')
        .select(`
          *,
          team_memberships!inner(
            role,
            joined_at,
            is_active
          )
        `)
        .eq('team_memberships.is_active', true)
        .order('created_at', { ascending: false });

      return { data, error };
    } catch (error) {
      return { data: null, error };
    }
  }

  // Get team members
  static async getTeamMembers(teamId: string): Promise<{ data: any[] | null; error: any }> {
    try {
      const { data, error } = await supabase
        .from('team_memberships')
        .select(`
          *,
          profiles:user_id(
            id,
            full_name,
            avatar_url
          )
        `)
        .eq('team_id', teamId)
        .eq('is_active', true)
        .order('joined_at', { ascending: true });

      return { data, error };
    } catch (error) {
      return { data: null, error };
    }
  }
}
```

### 2.2 Invitation Service Functions

**File: `src/lib/services/invitationService.ts`**

```typescript
import { supabase } from '@/lib/supabase';
import { Database } from '@/integrations/supabase/types';

type TeamRole = Database['public']['Enums']['team_role_enum'];

export class InvitationService {
  // Send team invitation
  static async inviteToTeam(
    teamId: string,
    email: string,
    role: TeamRole = 'member'
  ): Promise<{ data: any | null; error: any }> {
    try {
      // Generate invitation token
      const token = crypto.randomUUID();
      const expiresAt = new Date();
      expiresAt.setDate(expiresAt.getDate() + 7); // 7 days expiry

      const { data, error } = await supabase
        .from('team_invitations')
        .insert({
          team_id: teamId,
          email: email,
          role: role,
          token: token,
          expires_at: expiresAt.toISOString()
        })
        .select()
        .single();

      if (error) throw error;

      // TODO: Send email notification
      // await this.sendInvitationEmail(email, token, teamId);

      return { data, error: null };
    } catch (error) {
      return { data: null, error };
    }
  }

  // Accept invitation
  static async acceptInvitation(token: string): Promise<{ success: boolean; error: any }> {
    try {
      const { data, error } = await supabase.rpc('accept_team_invitation', {
        invitation_token: token
      });

      return { success: data || false, error };
    } catch (error) {
      return { success: false, error };
    }
  }

  // Get pending invitations for user
  static async getUserInvitations(): Promise<{ data: any[] | null; error: any }> {
    try {
      const { data: user } = await supabase.auth.getUser();
      if (!user.user?.email) throw new Error('User not authenticated');

      const { data, error } = await supabase
        .from('team_invitations')
        .select(`
          *,
          teams(
            name,
            description
          ),
          invited_by_profile:invited_by(
            full_name
          )
        `)
        .eq('email', user.user.email)
        .eq('status', 'pending')
        .gt('expires_at', new Date().toISOString());

      return { data, error };
    } catch (error) {
      return { data: null, error };
    }
  }
}
```

### 2.3 Updated Request Service

**File: `src/lib/services/requestService.ts`**

```typescript
import { supabase } from '@/lib/supabase';
import { RequestEntry } from '@/lib/supabase';

export class RequestService {
  // Fetch requests with team context
  static async fetchRequests(teamId?: string): Promise<{ data: any[] | null; error: any }> {
    try {
      let query = supabase
        .from('requests')
        .select(`
          *,
          teams(
            id,
            name
          ),
          profiles:user_id(
            full_name
          )
        `)
        .order('created_at', { ascending: false });

      // Filter by team if specified
      if (teamId) {
        query = query.eq('team_id', teamId);
      }

      const { data, error } = await query;
      return { data, error };
    } catch (error) {
      return { data: null, error };
    }
  }

  // Create request with team assignment
  static async createRequest(
    requestData: RequestEntry,
    teamId?: string
  ): Promise<{ data: RequestEntry | null; error: any }> {
    try {
      const dataWithTeam = {
        ...requestData,
        team_id: teamId || null
      };

      const { data, error } = await supabase
        .from('requests')
        .insert(dataWithTeam)
        .select()
        .single();

      return { data, error };
    } catch (error) {
      return { data: null, error };
    }
  }
}
```

---

## Phase 3: Frontend Components and UI

### 3.1 Team Context Provider

**File: `src/context/TeamContext.tsx`**

```typescript
import React, { createContext, useContext, useEffect, useState } from 'react';
import { TeamService } from '@/lib/services/teamService';
import { useAuth } from './AuthContext';
import { useToast } from '@/hooks/use-toast';

type Team = {
  id: string;
  name: string;
  description?: string;
  role: string;
  created_at: string;
};

type TeamContextType = {
  teams: Team[];
  currentTeam: Team | null;
  isLoading: boolean;
  setCurrentTeam: (team: Team | null) => void;
  refreshTeams: () => Promise<void>;
  createTeam: (name: string, description?: string) => Promise<{ success: boolean; team?: Team }>;
};

const TeamContext = createContext<TeamContextType | undefined>(undefined);

export function TeamProvider({ children }: { children: React.ReactNode }) {
  const [teams, setTeams] = useState<Team[]>([]);
  const [currentTeam, setCurrentTeam] = useState<Team | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const { user } = useAuth();
  const { toast } = useToast();

  const refreshTeams = async () => {
    if (!user) return;

    try {
      setIsLoading(true);
      const { data, error } = await TeamService.getUserTeams();

      if (error) throw error;

      const formattedTeams = data?.map(team => ({
        id: team.id,
        name: team.name,
        description: team.description,
        role: team.team_memberships[0]?.role || 'member',
        created_at: team.created_at
      })) || [];

      setTeams(formattedTeams);

      // Set first team as current if none selected
      if (!currentTeam && formattedTeams.length > 0) {
        setCurrentTeam(formattedTeams[0]);
      }
    } catch (error) {
      console.error('Error fetching teams:', error);
      toast({
        title: 'Erreur',
        description: 'Impossible de charger les équipes',
        variant: 'destructive'
      });
    } finally {
      setIsLoading(false);
    }
  };

  const createTeam = async (name: string, description?: string) => {
    try {
      const { data, error } = await TeamService.createTeam(name, description);

      if (error) throw error;

      await refreshTeams();

      toast({
        title: 'Succès',
        description: 'Équipe créée avec succès'
      });

      return { success: true, team: data };
    } catch (error) {
      console.error('Error creating team:', error);
      toast({
        title: 'Erreur',
        description: 'Impossible de créer l\'équipe',
        variant: 'destructive'
      });
      return { success: false };
    }
  };

  useEffect(() => {
    if (user) {
      refreshTeams();
    } else {
      setTeams([]);
      setCurrentTeam(null);
      setIsLoading(false);
    }
  }, [user]);

  return (
    <TeamContext.Provider value={{
      teams,
      currentTeam,
      isLoading,
      setCurrentTeam,
      refreshTeams,
      createTeam
    }}>
      {children}
    </TeamContext.Provider>
  );
}

export function useTeam() {
  const context = useContext(TeamContext);
  if (context === undefined) {
    throw new Error('useTeam must be used within a TeamProvider');
  }
  return context;
}
```

### 3.2 Team Selector Component

**File: `src/components/teams/TeamSelector.tsx`**

```typescript
import React from 'react';
import { Check, ChevronsUpDown, Plus, Users } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
} from '@/components/ui/command';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { useTeam } from '@/context/TeamContext';

interface TeamSelectorProps {
  onCreateTeam?: () => void;
}

export function TeamSelector({ onCreateTeam }: TeamSelectorProps) {
  const { teams, currentTeam, setCurrentTeam, isLoading } = useTeam();
  const [open, setOpen] = React.useState(false);

  if (isLoading) {
    return (
      <Button variant="outline" disabled className="w-[200px] justify-between">
        <Users className="mr-2 h-4 w-4" />
        Chargement...
      </Button>
    );
  }

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className="w-[200px] justify-between"
        >
          <Users className="mr-2 h-4 w-4" />
          {currentTeam ? currentTeam.name : "Sélectionner une équipe"}
          <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-[200px] p-0">
        <Command>
          <CommandInput placeholder="Rechercher une équipe..." />
          <CommandEmpty>Aucune équipe trouvée.</CommandEmpty>
          <CommandGroup>
            {teams.map((team) => (
              <CommandItem
                key={team.id}
                value={team.name}
                onSelect={() => {
                  setCurrentTeam(team);
                  setOpen(false);
                }}
              >
                <Check
                  className={cn(
                    "mr-2 h-4 w-4",
                    currentTeam?.id === team.id ? "opacity-100" : "opacity-0"
                  )}
                />
                <div className="flex flex-col">
                  <span>{team.name}</span>
                  <span className="text-xs text-muted-foreground">
                    {team.role}
                  </span>
                </div>
              </CommandItem>
            ))}
          </CommandGroup>
          {onCreateTeam && (
            <>
              <CommandGroup>
                <CommandItem
                  onSelect={() => {
                    onCreateTeam();
                    setOpen(false);
                  }}
                >
                  <Plus className="mr-2 h-4 w-4" />
                  Créer une équipe
                </CommandItem>
              </CommandGroup>
            </>
          )}
        </Command>
      </PopoverContent>
    </Popover>
  );
}
```

### 3.3 Team Management Dialog

**File: `src/components/teams/TeamManagementDialog.tsx`**

```typescript
import React, { useState } from 'react';
import { Plus, Users, Mail, Settings, Trash2 } from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useTeam } from '@/context/TeamContext';
import { TeamService } from '@/lib/services/teamService';
import { InvitationService } from '@/lib/services/invitationService';
import { useToast } from '@/hooks/use-toast';

interface TeamManagementDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function TeamManagementDialog({ open, onOpenChange }: TeamManagementDialogProps) {
  const { currentTeam, refreshTeams } = useTeam();
  const { toast } = useToast();
  const [members, setMembers] = useState<any[]>([]);
  const [inviteEmail, setInviteEmail] = useState('');
  const [inviteRole, setInviteRole] = useState<'member' | 'admin'>('member');
  const [isLoading, setIsLoading] = useState(false);

  const loadMembers = async () => {
    if (!currentTeam) return;

    try {
      const { data, error } = await TeamService.getTeamMembers(currentTeam.id);
      if (error) throw error;
      setMembers(data || []);
    } catch (error) {
      console.error('Error loading members:', error);
    }
  };

  const handleInvite = async () => {
    if (!currentTeam || !inviteEmail) return;

    try {
      setIsLoading(true);
      const { error } = await InvitationService.inviteToTeam(
        currentTeam.id,
        inviteEmail,
        inviteRole
      );

      if (error) throw error;

      toast({
        title: 'Invitation envoyée',
        description: `Invitation envoyée à ${inviteEmail}`
      });

      setInviteEmail('');
      setInviteRole('member');
    } catch (error) {
      toast({
        title: 'Erreur',
        description: 'Impossible d\'envoyer l\'invitation',
        variant: 'destructive'
      });
    } finally {
      setIsLoading(false);
    }
  };

  React.useEffect(() => {
    if (open && currentTeam) {
      loadMembers();
    }
  }, [open, currentTeam]);

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            Gestion de l'équipe: {currentTeam?.name}
          </DialogTitle>
          <DialogDescription>
            Gérez les membres et les paramètres de votre équipe.
          </DialogDescription>
        </DialogHeader>

        <Tabs defaultValue="members" className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="members">Membres</TabsTrigger>
            <TabsTrigger value="settings">Paramètres</TabsTrigger>
          </TabsList>

          <TabsContent value="members" className="space-y-4">
            {/* Invite new member */}
            <Card>
              <CardHeader>
                <CardTitle className="text-sm">Inviter un membre</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="email">Email</Label>
                    <Input
                      id="email"
                      type="email"
                      value={inviteEmail}
                      onChange={(e) => setInviteEmail(e.target.value)}
                      placeholder="<EMAIL>"
                    />
                  </div>
                  <div>
                    <Label htmlFor="role">Rôle</Label>
                    <Select value={inviteRole} onValueChange={(value: any) => setInviteRole(value)}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="member">Membre</SelectItem>
                        <SelectItem value="admin">Administrateur</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                <Button onClick={handleInvite} disabled={!inviteEmail || isLoading}>
                  <Mail className="mr-2 h-4 w-4" />
                  Envoyer l'invitation
                </Button>
              </CardContent>
            </Card>

            {/* Current members */}
            <Card>
              <CardHeader>
                <CardTitle className="text-sm">Membres actuels</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {members.map((member) => (
                    <div key={member.id} className="flex items-center justify-between p-2 border rounded">
                      <div>
                        <p className="font-medium">{member.profiles?.full_name || 'Utilisateur'}</p>
                        <p className="text-sm text-muted-foreground">{member.role}</p>
                      </div>
                      {member.role !== 'owner' && (
                        <Button variant="ghost" size="sm">
                          <Settings className="h-4 w-4" />
                        </Button>
                      )}
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="settings" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-sm">Paramètres de l'équipe</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="team-name">Nom de l'équipe</Label>
                  <Input id="team-name" defaultValue={currentTeam?.name} />
                </div>
                <div>
                  <Label htmlFor="team-description">Description</Label>
                  <Textarea id="team-description" defaultValue={currentTeam?.description} />
                </div>
                <Button>Sauvegarder</Button>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  );
}
```

---

## Phase 4: UI Integration and Navigation Updates

### 4.1 Updated TopNavbar with Team Selector

**File: `src/components/TopNavbar.tsx` (modifications)**

```typescript
// Add these imports
import { TeamSelector } from '@/components/teams/TeamSelector';
import { TeamManagementDialog } from '@/components/teams/TeamManagementDialog';
import { CreateTeamDialog } from '@/components/teams/CreateTeamDialog';

// Add state for dialogs
const [showTeamManagement, setShowTeamManagement] = useState(false);
const [showCreateTeam, setShowCreateTeam] = useState(false);

// Add team selector between logo and spacer
<div className="flex-1 flex justify-center">
  <TeamSelector
    onCreateTeam={() => setShowCreateTeam(true)}
  />
</div>

// Add dialogs before closing component
<TeamManagementDialog
  open={showTeamManagement}
  onOpenChange={setShowTeamManagement}
/>
<CreateTeamDialog
  open={showCreateTeam}
  onOpenChange={setShowCreateTeam}
/>
```

### 4.2 Updated App.tsx with Team Provider

**File: `src/App.tsx` (modifications)**

```typescript
// Add import
import { TeamProvider } from '@/context/TeamContext';

// Wrap AuthProvider with TeamProvider
<QueryClientProvider client={queryClient}>
  <AuthProvider>
    <TeamProvider>
      <ThemeProvider>
        {/* ... rest of the app */}
      </ThemeProvider>
    </TeamProvider>
  </AuthProvider>
</QueryClientProvider>
```

### 4.3 Updated Dashboard with Team Context

**File: `src/pages/Index.tsx` (modifications)**

```typescript
// Add team context
import { useTeam } from '@/context/TeamContext';
import { RequestService } from '@/lib/services/requestService';

// Replace existing request fetching
const { currentTeam } = useTeam();

const loadRequests = async () => {
  if (!user) return;

  setIsLoading(true);
  try {
    const { data, error } = await RequestService.fetchRequests(currentTeam?.id);
    if (error) throw error;
    setRequests(data || []);
  } catch (error) {
    console.error('Failed to load requests:', error);
    toast({
      title: 'Erreur',
      description: 'Impossible de charger les demandes.',
      variant: 'destructive'
    });
  } finally {
    setIsLoading(false);
  }
};

// Update useEffect to depend on currentTeam
useEffect(() => {
  loadRequests();
}, [user, currentTeam]);
```

---

## Phase 5: Data Migration Strategy

### 5.1 Migration Script for Existing Users

**File: `supabase/migrations/01_teams_migration.sql`**

```sql
-- Migration script to handle existing users and data

-- Step 1: Create personal teams for existing users
INSERT INTO "public"."teams" ("name", "description", "created_by")
SELECT
  COALESCE(p.full_name, 'Mon équipe') || ' (Personnel)',
  'Équipe personnelle créée automatiquement',
  p.id
FROM "public"."profiles" p
WHERE NOT EXISTS (
  SELECT 1 FROM "public"."teams" t WHERE t.created_by = p.id
);

-- Step 2: Add users as owners of their personal teams
INSERT INTO "public"."team_memberships" ("team_id", "user_id", "role", "invited_by")
SELECT
  t.id,
  t.created_by,
  'owner',
  t.created_by
FROM "public"."teams" t
WHERE NOT EXISTS (
  SELECT 1 FROM "public"."team_memberships" tm
  WHERE tm.team_id = t.id AND tm.user_id = t.created_by
);

-- Step 3: Assign existing requests to personal teams
UPDATE "public"."requests"
SET "team_id" = (
  SELECT t.id
  FROM "public"."teams" t
  WHERE t.created_by = "requests"."user_id"
  LIMIT 1
)
WHERE "team_id" IS NULL;

-- Step 4: Update profiles with default team
UPDATE "public"."profiles"
SET "default_team_id" = (
  SELECT t.id
  FROM "public"."teams" t
  WHERE t.created_by = "profiles"."id"
  LIMIT 1
)
WHERE "default_team_id" IS NULL;
```

### 5.2 Rollback Strategy

```sql
-- Rollback script if needed
-- Remove team assignments from requests
UPDATE "public"."requests" SET "team_id" = NULL;

-- Remove team-related data
DELETE FROM "public"."team_invitations";
DELETE FROM "public"."team_memberships";
DELETE FROM "public"."teams";

-- Remove columns (if needed)
-- ALTER TABLE "public"."requests" DROP COLUMN "team_id";
-- ALTER TABLE "public"."profiles" DROP COLUMN "default_team_id";
```

---

## Phase 6: Security Considerations

### 6.1 Permission System

**Role Hierarchy:**
- **Owner**: Full control, can delete team, manage all members
- **Admin**: Can invite/remove members, manage team settings
- **Member**: Can view and edit team requests, cannot manage team
- **Viewer**: Read-only access to team requests

### 6.2 Data Access Controls

**Request Access Rules:**
1. Users can always access their own requests (backward compatibility)
2. Team members can access requests assigned to their team
3. Viewers have read-only access
4. Members and above can create/edit requests

**Security Measures:**
- All team operations protected by RLS policies
- Invitation tokens expire after 7 days
- Email verification required for invitations
- Audit trail for team membership changes

---

## Phase 7: Implementation Timeline

### **Week 1: Database Foundation**
- [ ] Create database schema (tables, enums, indexes)
- [ ] Implement RLS policies
- [ ] Create database functions
- [ ] Test data migration script

### **Week 2: Backend Services**
- [ ] Implement TeamService class
- [ ] Implement InvitationService class
- [ ] Update RequestService for team support
- [ ] Create API integration tests

### **Week 3: Frontend Core**
- [ ] Create TeamContext provider
- [ ] Implement team selector component
- [ ] Build team management dialog
- [ ] Create invitation acceptance flow

### **Week 4: UI Integration**
- [ ] Update navigation with team selector
- [ ] Modify dashboard for team context
- [ ] Update request creation flow
- [ ] Implement team switching

### **Week 5: Testing & Polish**
- [ ] End-to-end testing
- [ ] Performance optimization
- [ ] UI/UX refinements
- [ ] Documentation updates

---

## Phase 8: Testing Strategy

### 8.1 Unit Tests

**Database Functions:**
```sql
-- Test team creation
SELECT create_team_with_owner('Test Team', 'Description');

-- Test invitation acceptance
SELECT accept_team_invitation('test-token');
```

**Service Layer:**
```typescript
// Test team creation
describe('TeamService', () => {
  it('should create team and add owner', async () => {
    const result = await TeamService.createTeam('Test Team');
    expect(result.data).toBeDefined();
    expect(result.error).toBeNull();
  });
});
```

### 8.2 Integration Tests

**Team Workflow:**
1. Create team
2. Invite member
3. Accept invitation
4. Create shared request
5. Verify access permissions

### 8.3 Security Tests

**Access Control:**
- Verify RLS policies prevent unauthorized access
- Test invitation token security
- Validate role-based permissions

---

## Phase 9: Deployment Considerations

### 9.1 Feature Flags

Implement feature flags to gradually roll out teams:
```typescript
const TEAMS_ENABLED = process.env.VITE_TEAMS_FEATURE === 'true';
```

### 9.2 Backward Compatibility

- Existing users automatically get personal teams
- All existing requests remain accessible
- No breaking changes to current workflows

### 9.3 Performance Monitoring

- Monitor query performance with team joins
- Track invitation email delivery
- Monitor team switching performance

---

## Summary

This comprehensive plan transforms the single-user application into a collaborative platform while maintaining backward compatibility. The implementation follows a phased approach that minimizes risk and allows for iterative testing and refinement.

**Key Benefits:**
- **Seamless Collaboration**: Teams can work together on research requests
- **Flexible Permissions**: Role-based access control
- **Easy Onboarding**: Simple invitation system
- **Backward Compatible**: Existing users unaffected
- **Scalable Architecture**: Built for future enhancements

**Next Steps:**
1. Review and approve this plan
2. Set up development environment
3. Begin Phase 1 implementation
4. Regular progress reviews and adjustments
