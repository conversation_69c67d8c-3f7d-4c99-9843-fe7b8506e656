#!/usr/bin/env node

import pkg from '@google/genai';
const { GoogleGenAI } = pkg;
import { access, stat } from 'fs/promises';

/**
 * Gemini File Upload Script
 * 
 * This script uploads a local file to the Google Gemini Document Processing API
 * and returns the Gemini file ID for further processing.
 * 
 * Usage: node uploadGeminiFile.js <filePath> <mimeType>
 * Environment: GOOGLE_API_KEY must be set
 */

async function main() {
  try {
    // Validate command line arguments
    const args = process.argv.slice(2);
    if (args.length !== 2) {
      console.error('Error: Incorrect number of arguments');
      console.error('Usage: node uploadGeminiFile.js <filePath> <mimeType>');
      console.error('Example: node uploadGeminiFile.js /tmp/document.pdf application/pdf');
      process.exit(1);
    }

    const [filePath, mimeType] = args;

    // Validate environment variable
    const apiKey = process.env.GOOGLE_API_KEY;
    if (!apiKey) {
      console.error('Error: GOOGLE_API_KEY environment variable is not set');
      console.error('Please set the GOOGLE_API_KEY environment variable with your Google API key');
      process.exit(1);
    }

    // Validate input parameters
    if (!filePath || !mimeType) {
      console.error('Error: Both filePath and mimeType are required');
      console.error('Usage: node uploadGeminiFile.js <filePath> <mimeType>');
      process.exit(1);
    }

    // Check if file exists and is accessible
    try {
      await access(filePath);
    } catch (error) {
      console.error(`Error: File not found or not accessible: ${filePath}`);
      console.error('Please check that the file path is correct and the file exists');
      process.exit(1);
    }

    // Get file stats for size information
    let fileStats;
    try {
      fileStats = await stat(filePath);
    } catch (error) {
      console.error(`Error: Unable to read file stats for: ${filePath}`);
      console.error(`Details: ${error.message}`);
      process.exit(1);
    }

    // Initialize Google GenAI client
    let ai;
    try {
      ai = new GoogleGenAI({ apiKey });
    } catch (error) {
      console.error('Error: Failed to initialize Google GenAI client');
      console.error(`Details: ${error.message}`);
      process.exit(1);
    }

    // Upload file to Gemini API
    let uploadResult;
    try {
      uploadResult = await ai.files.upload({
        file: filePath,
        config: {
          displayName: `File uploaded from ${filePath}`,
          mimeType: mimeType
        }
      });
    } catch (error) {
      console.error('Error: Failed to upload file to Gemini API');
      console.error(`Details: ${error.message}`);
      
      // Provide more specific error information if available
      if (error.response) {
        console.error(`HTTP Status: ${error.response.status}`);
        console.error(`Response: ${JSON.stringify(error.response.data)}`);
      }
      
      process.exit(1);
    }

    // Wait for the file to be processed
    let fileInfo = uploadResult;
    try {
      while (fileInfo.state === 'PROCESSING') {
        console.error(`File is processing, current status: ${fileInfo.state}`);
        console.error('Waiting 2 seconds before retrying...');
        
        await new Promise((resolve) => {
          setTimeout(resolve, 2000);
        });
        
        fileInfo = await ai.files.get({ name: uploadResult.name });
      }
      
      if (fileInfo.state === 'FAILED') {
        throw new Error('File processing failed.');
      }
    } catch (error) {
      console.error('Error: Failed to check file processing status');
      console.error(`Details: ${error.message}`);
      process.exit(1);
    }

    // Validate upload result
    if (!fileInfo || !fileInfo.name) {
      console.error('Error: Invalid response from Gemini API - missing file.name');
      console.error('Upload may have failed or returned unexpected data');
      process.exit(1);
    }

    // Prepare successful response
    const result = {
      geminiFileName: fileInfo.name,
      geminiFileUri: fileInfo.uri || fileInfo.name,
      mimeType: fileInfo.mimeType || mimeType,
      sizeBytes: fileInfo.sizeBytes || fileStats.size,
      state: fileInfo.state
    };

    // Output the result as JSON to stdout for orchestrator consumption
    console.log(JSON.stringify(result));

  } catch (error) {
    // Catch any unexpected errors
    console.error('Error: Unexpected error occurred during file upload');
    console.error(`Details: ${error.message}`);
    if (error.stack) {
      console.error(`Stack trace: ${error.stack}`);
    }
    process.exit(1);
  }
}

// Handle uncaught exceptions and unhandled rejections
process.on('uncaughtException', (error) => {
  console.error('Error: Uncaught exception occurred');
  console.error(`Details: ${error.message}`);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('Error: Unhandled promise rejection');
  console.error(`Details: ${reason}`);
  process.exit(1);
});

// Execute main function
main();
