

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;


CREATE SCHEMA IF NOT EXISTS "public";


ALTER SCHEMA "public" OWNER TO "pg_database_owner";


COMMENT ON SCHEMA "public" IS 'standard public schema';



CREATE TYPE "public"."acte_status_enum" AS ENUM (
    'Pending',
    'Ready for Analysis',
    'Analysis in Progress',
    'Analysis Completed',
    'Document not Available'
);


ALTER TYPE "public"."acte_status_enum" OWNER TO "postgres";


CREATE TYPE "public"."acte_type_enum" AS ENUM (
    'Acte',
    'Avis d''adresse',
    'Radiation',
    'Acte divers'
);


ALTER TYPE "public"."acte_type_enum" OWNER TO "postgres";


CREATE TYPE "public"."document_source_enum" AS ENUM (
    'acte',
    'index'
);


ALTER TYPE "public"."document_source_enum" OWNER TO "postgres";


CREATE TYPE "public"."extraction_status_enum" AS ENUM (
    'En_attente',
    'En_traitement',
    'Telecharge',
    'Disponible_sur_Drive',
    'Document_introuvable'
);


ALTER TYPE "public"."extraction_status_enum" OWNER TO "postgres";


CREATE TYPE "public"."index_phase_status_enum" AS ENUM (
    'Pending',
    'Ready for Analysis',
    'Analysis in Progress',
    'Analysis Completed',
    'Document not Available'
);


ALTER TYPE "public"."index_phase_status_enum" OWNER TO "postgres";


CREATE TYPE "public"."index_status_enum" AS ENUM (
    'Phase 1',
    'Waiting Phase 2',
    'Phase 2',
    'Phase 3',
    'Completed'
);


ALTER TYPE "public"."index_status_enum" OWNER TO "postgres";


CREATE TYPE "public"."phase_2_status_enum" AS ENUM (
    'Pending',
    'Actes Analysis in Progress',
    'Analysis Completed'
);


ALTER TYPE "public"."phase_2_status_enum" OWNER TO "postgres";


CREATE TYPE "public"."request_status_enum" AS ENUM (
    'Pending',
    'In Progress',
    'Phase 1',
    'Phase 2',
    'Phase 3',
    'Completed'
);


ALTER TYPE "public"."request_status_enum" OWNER TO "postgres";

SET default_tablespace = '';

SET default_table_access_method = "heap";


CREATE TABLE IF NOT EXISTS "public"."actes" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "request_id" "uuid" NOT NULL,
    "index_id" "uuid",
    "doc_id" "text",
    "doc_url" "text",
    "acte_publication_number" "text",
    "acte_nature" "text",
    "acte_parties" "text",
    "acte_details" "text",
    "acte_summary" "text",
    "acte_notary_minute" "text",
    "writting" "text",
    "relevance_explanation" "text",
    "relevance_rating" integer,
    "created_at" timestamp with time zone DEFAULT "now"(),
    "updated_at" timestamp with time zone DEFAULT "now"(),
    "other_details" "jsonb",
    "acte_publication_date" "text",
    "circonscription_fonciere" "text",
    "matrimonie" "text",
    "is_radiated" boolean DEFAULT false,
    "radiation_number" "text",
    "source_type" "text" DEFAULT 'index'::"text",
    "source_id" "uuid",
    "status" "public"."acte_status_enum" DEFAULT 'Pending'::"public"."acte_status_enum" NOT NULL,
    "document_ready" boolean DEFAULT false NOT NULL,
    "document_completed" boolean DEFAULT false NOT NULL,
    "doc_number" integer,
    "file_content" "text"
);


ALTER TABLE "public"."actes" OWNER TO "postgres";


COMMENT ON COLUMN "public"."actes"."source_type" IS 'Type of source document where this acte was discovered: "index" (default) or "acte"';



COMMENT ON COLUMN "public"."actes"."source_id" IS 'ID of the source document (index or acte) where this acte was discovered';



CREATE OR REPLACE FUNCTION "public"."check_additional_actes"("p_index_id" "uuid", "p_acte_level" integer) RETURNS SETOF "public"."actes"
    LANGUAGE "plpgsql"
    AS $$
BEGIN
  RETURN QUERY
  SELECT *
  FROM actes
  WHERE index_id = p_index_id
  AND acte_level > p_acte_level
  AND NOT EXISTS (
    SELECT 1 FROM processing_queue
    WHERE document_type = 'acte'
    AND document_id = actes.id
    AND status != 'completed'
  );
END;
$$;


ALTER FUNCTION "public"."check_additional_actes"("p_index_id" "uuid", "p_acte_level" integer) OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."get_final_doc_creation_instructions"() RETURNS "text"
    LANGUAGE "plpgsql" STABLE
    AS $_$
DECLARE
  v_instructions TEXT;
BEGIN
  v_instructions := $$
=============================================
FINAL DOCUMENT CREATION PROCESS DOCUMENTATION
=============================================

Overview:
When a request completes all three processing phases, it enters the FINALIZING state. 
At this point, the final document needs to be created by sending a webhook request to the 
final-doc-creator workflow in n8n.

Webhook Details:
URL: https://n8n.paraito.ca/webhook/final-doc-creator
Method: POST
Content-Type: application/json

Request Body:
{
  "request_id": "[UUID of the request]"
}

Example curl command:
curl -X POST -H "Content-Type: application/json" \
     -d '{"request_id": "29b4c1cd-c6d5-4410-90e1-61cacb5506fb"}' \
     https://n8n.paraito.ca/webhook/final-doc-creator

Process Flow:
1. The request transitions to FINALIZING state when Phase 3 processing is complete
2. The external n8n webhook is called with the request_id
3. The n8n workflow generates the final document
4. The n8n workflow updates the request status to COMPLETED when finished
5. The request is now complete

Implementation Notes:
- A trigger on the requests table detects the transition to FINALIZING state
- In a production environment, you should set up a reliable mechanism to call the webhook
  (e.g., a task queue, an n8n polling workflow, or a serverless function)
- The manually_trigger_final_doc_creation() function can be used to manually trigger the process
  for specific requests
- Never manually set a request status to COMPLETED - this should only be done by the n8n workflow
$$;

  RETURN v_instructions;
END;
$_$;


ALTER FUNCTION "public"."get_final_doc_creation_instructions"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."handle_extraction_queue_logic"() RETURNS "trigger"
    LANGUAGE "plpgsql"
    AS $$
BEGIN
    -- Rule: Default acte_type to 'Acte' on insert if document_source is 'acte'
    IF TG_OP = 'INSERT' THEN
        IF NEW.document_source = 'acte' THEN
            NEW.acte_type = 'Acte';
        END IF;
    END IF;

    -- Rules for updates (primarily when retry_count changes)
    IF TG_OP = 'UPDATE' THEN
        -- Only apply retry-based logic if retry_count actually changed or is being set
        IF NEW.retry_count IS DISTINCT FROM OLD.retry_count THEN
            -- Rule: When retry_count = 2 and source is 'acte', change acte_type to 'Radiation'
            IF NEW.document_source = 'acte' AND NEW.retry_count = 2 THEN
                NEW.acte_type = 'Radiation';
            END IF;

            -- Rule: When retry_count = 3, set status to 'Document_introuvable'
            IF NEW.retry_count >= 3 THEN
                NEW.status = 'Document_introuvable';
            ELSE
                -- If retry_count was incremented due to a failure, but is < 3,
                -- and the status was not already set to a non-failure state,
                -- set it back to 'En_attente' for the next attempt.
                IF NEW.status NOT IN ('Telecharge', 'Disponible_sur_Drive', 'En_traitement') THEN
                    NEW.status = 'En_attente';
                END IF;
            END IF;
        END IF;
    END IF;

    RETURN NEW;
END;
$$;


ALTER FUNCTION "public"."handle_extraction_queue_logic"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."handle_new_user"() RETURNS "trigger"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
BEGIN
  INSERT INTO public.profiles (id, full_name)
  VALUES (new.id, new.raw_user_meta_data->>'full_name');
  RETURN NEW;
END;
$$;


ALTER FUNCTION "public"."handle_new_user"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."manually_trigger_final_doc_creation"("p_request_id" "uuid") RETURNS "text"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
  v_webhook_url TEXT := 'https://n8n.paraito.ca/webhook/final-doc-creator';
  v_request_status TEXT;
  v_response TEXT;
BEGIN
  -- Check the request exists and is in an appropriate state
  SELECT status INTO v_request_status FROM public.requests WHERE id = p_request_id;
  
  IF NOT FOUND THEN
    RETURN 'Error: Request ID ' || p_request_id || ' not found.';
  END IF;
  
  -- Update the request status to FINALIZING if it's not already
  IF v_request_status <> 'FINALIZING' THEN
    UPDATE public.requests
    SET status = 'FINALIZING',
        updated_at = now()
    WHERE id = p_request_id;
    
    v_response := 'Request status updated to FINALIZING. ';
  ELSE
    v_response := 'Request was already in FINALIZING state. ';
  END IF;
  
  -- Return the webhook information that should be called
  v_response := v_response || 'To finalize the document, call this webhook: ' || v_webhook_url || 
                ' with JSON body: {"request_id": "' || p_request_id || '"}';
                
  RETURN v_response;
END;
$$;


ALTER FUNCTION "public"."manually_trigger_final_doc_creation"("p_request_id" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."manually_trigger_final_doc_webhook"("p_request_id" "uuid") RETURNS "text"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
  v_status TEXT;
  v_message TEXT;
BEGIN
  -- Check if the request exists
  SELECT status INTO v_status FROM public.requests WHERE id = p_request_id;
  
  IF NOT FOUND THEN
    RETURN 'Error: Request ID ' || p_request_id || ' not found.';
  END IF;
  
  -- Create a log entry for the webhook
  INSERT INTO public.error_log (flow_name, error, created_at)
  VALUES (
    'Final Doc Webhook', 
    format('WEBHOOK NEEDED: Request ID %s needs final doc. Call webhook with body {"request_id": "%s"}', p_request_id, p_request_id),
    now()
  );
  
  v_message := 'Webhook log created for request ' || p_request_id || '.' || E'\n';
  v_message := v_message || 'To create the final document, call:' || E'\n';
  v_message := v_message || 'curl -X POST -H "Content-Type: application/json" -d ''{"request_id": "' || p_request_id || '"}'' https://n8n.paraito.ca/webhook/final-doc-creator';
  
  RETURN v_message;
END;
$$;


ALTER FUNCTION "public"."manually_trigger_final_doc_webhook"("p_request_id" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."mark_queue_item_completed"("p_queue_id" "uuid") RETURNS "void"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
  v_queue_item RECORD;
  v_request RECORD;
  v_remaining_count INTEGER := 0;
  v_error_msg TEXT;
  v_debug_info TEXT;
  v_current_status TEXT;
  v_phase_transition BOOLEAN := FALSE;
BEGIN
  -- Log start of function execution
  INSERT INTO public.error_log (flow_name, error, created_at)
  VALUES ('mark_queue_completed', format('Starting mark_queue_item_completed for queue item %s', p_queue_id), now());
  
  BEGIN -- Use inner block for exception handling
    -- Check current status first
    SELECT status INTO v_current_status FROM public.processing_queue WHERE id = p_queue_id;
  
    IF NOT FOUND THEN
      RAISE WARNING 'Queue item % not found.', p_queue_id;
      
      -- Log error
      INSERT INTO public.error_log (flow_name, error, created_at)
      VALUES ('mark_queue_completed', format('Queue item %s not found', p_queue_id), now());
      
      RETURN;
    END IF;
  
    -- If already completed, just log and exit gracefully
    IF v_current_status = 'completed' THEN
       RAISE NOTICE '[%] Queue item % already marked completed. Skipping.', 'mark_queue_item_completed', p_queue_id;
       
       -- Log skip
       INSERT INTO public.error_log (flow_name, error, created_at)
       VALUES ('mark_queue_completed', format('Queue item %s already completed. Skipping.', p_queue_id), now());
       
       RETURN;
    END IF;
  
    -- Fetch the queue item details (needed for request lookup)
    SELECT * INTO v_queue_item FROM public.processing_queue WHERE id = p_queue_id;
  
    -- Fetch the corresponding request details
    SELECT * INTO v_request FROM public.requests WHERE id = v_queue_item.request_id;
    IF NOT FOUND THEN
      RAISE WARNING '[%] Request % not found for queue item %.', 'mark_queue_item_completed', v_queue_item.request_id, p_queue_id;
      
      -- Log error
      INSERT INTO public.error_log (flow_name, error, created_at)
      VALUES ('mark_queue_completed', 
              format('Request %s not found for queue item %s', v_queue_item.request_id, p_queue_id), 
              now());
      
      -- Update queue item to error state
      UPDATE public.processing_queue 
      SET status = 'error', 
          updated_at = now() 
      WHERE id = p_queue_id;
      
      RETURN;
    END IF;
  
    -- Mark the current queue item as completed (only if not already completed)
    UPDATE public.processing_queue
    SET status = 'completed', updated_at = now()
    WHERE id = p_queue_id;
    
    -- Log completion
    INSERT INTO public.error_log (flow_name, error, created_at)
    VALUES ('mark_queue_completed', 
            format('Completed queue item %s (request %s, phase %s)', 
                   p_queue_id, v_request.id, v_request.current_processing_phase), 
            now());
    
    -- Check if there are any remaining queue items for the current processing phase
    SELECT COUNT(*) INTO v_remaining_count
    FROM public.processing_queue
    WHERE request_id = v_request.id
      AND processing_phase = v_request.current_processing_phase
      AND status IN ('queued', 'processing');
  
    -- Log remaining count
    INSERT INTO public.error_log (flow_name, error, created_at)
    VALUES ('mark_queue_completed', 
            format('Request %s has %s remaining items in phase %s', 
                   v_request.id, v_remaining_count, v_request.current_processing_phase), 
            now());
    
    -- If there are no more items to process in the current phase, consider phase transition
    IF v_remaining_count = 0 THEN
      RAISE NOTICE '[%] All queue items completed for request % phase %. Evaluating phase transition.', 
                   'mark_queue_item_completed', v_request.id, v_request.current_processing_phase;
  
      -- Phase transition logic based on current phase
      IF v_request.current_processing_phase = 1 THEN
        -- Phase 1 to Phase 2 transition
        -- Set the new phase
        UPDATE public.requests
        SET current_processing_phase = 2, 
            status = 'PROCESSING_PHASE_2', -- THIS LINE IS CRUCIAL
            updated_at = now()
        WHERE id = v_request.id;
        
        -- Log phase transition
        INSERT INTO public.error_log (flow_name, error, created_at)
        VALUES ('mark_queue_completed', 
                format('Transitioned request %s from Phase 1 to Phase 2', v_request.id), 
                now());
        
        -- Call helper function to queue eligible actes for Phase 2
        BEGIN
          PERFORM public.queue_eligible_documents_for_phase(v_request.id, 2);
          
          -- Log success of queueing
          INSERT INTO public.error_log (flow_name, error, created_at)
          VALUES ('mark_queue_completed', 
                  format('Successfully queued Phase 2 documents for request %s', v_request.id), 
                  now());
        EXCEPTION WHEN OTHERS THEN
          -- Log error in queueing
          GET STACKED DIAGNOSTICS 
            v_error_msg = MESSAGE_TEXT,
            v_debug_info = PG_EXCEPTION_DETAIL;
            
          INSERT INTO public.error_log (flow_name, error, created_at)
          VALUES ('mark_queue_completed', 
                  format('Error queueing Phase 2 documents for request %s: %s. Details: %s', 
                         v_request.id, v_error_msg, v_debug_info), 
                  now());
        END;
        
        RAISE NOTICE '[%] Transitioned request % from Phase 1 to Phase 2.', 'mark_queue_item_completed', v_request.id;
        v_phase_transition := TRUE;
  
      ELSIF v_request.current_processing_phase = 2 THEN
        -- Phase 2 to Phase 3 transition
        -- Set the new phase
        UPDATE public.requests
        SET current_processing_phase = 3, 
            status = 'PROCESSING_PHASE_3', -- THIS LINE IS CRUCIAL
            updated_at = now()
        WHERE id = v_request.id;
        
        -- Log phase transition
        INSERT INTO public.error_log (flow_name, error, created_at)
        VALUES ('mark_queue_completed', 
                format('Transitioned request %s from Phase 2 to Phase 3', v_request.id), 
                now());
        
        -- Call helper function to queue eligible documents for Phase 3
        BEGIN
          PERFORM public.queue_eligible_documents_for_phase(v_request.id, 3);
          
          -- Log success of queueing
          INSERT INTO public.error_log (flow_name, error, created_at)
          VALUES ('mark_queue_completed', 
                  format('Successfully queued Phase 3 documents for request %s', v_request.id), 
                  now());
        EXCEPTION WHEN OTHERS THEN
          -- Log error in queueing
          GET STACKED DIAGNOSTICS 
            v_error_msg = MESSAGE_TEXT,
            v_debug_info = PG_EXCEPTION_DETAIL;
            
          INSERT INTO public.error_log (flow_name, error, created_at)
          VALUES ('mark_queue_completed', 
                  format('Error queueing Phase 3 documents for request %s: %s. Details: %s', 
                         v_request.id, v_error_msg, v_debug_info), 
                  now());
        END;
        
        RAISE NOTICE '[%] Transitioned request % from Phase 2 to Phase 3.', 'mark_queue_item_completed', v_request.id;
        v_phase_transition := TRUE;
        
      ELSIF v_request.current_processing_phase = 3 THEN
        -- Phase 3 to Final transition
        -- Set status to FINALIZING instead of COMPLETED
        
        UPDATE public.requests
        SET current_processing_phase = NULL,
            status = 'FINALIZING', -- Changed from COMPLETED to FINALIZING
            updated_at = now()
        WHERE id = v_request.id;
        
        -- Log finalization
        INSERT INTO public.error_log (flow_name, error, created_at)
        VALUES ('mark_queue_completed', 
                format('Request %s moved to FINALIZING state - awaiting final document creation', v_request.id), 
                now());
        
        -- The final_doc_creator webhook will be called elsewhere and will set status to COMPLETED
        -- when finished
        
        RAISE NOTICE '[%] Request % is now in FINALIZING state. Final document creation will be triggered.', 
                    'mark_queue_item_completed', v_request.id;
                    
        -- Log the webhook info for reference (but don't call it directly from here)
        RAISE NOTICE '[%] Final document creation should be triggered via webhook: https://n8n.paraito.ca/webhook/final-doc-creator with request_id=%.', 
                   'mark_queue_item_completed', v_request.id;
      END IF;
    ELSE
      RAISE NOTICE '[%] Still % items remaining in processing phase % for request %.', 
                  'mark_queue_item_completed', v_remaining_count, v_request.current_processing_phase, v_request.id;
                  
      -- Log remaining items
      INSERT INTO public.error_log (flow_name, error, created_at)
      VALUES ('mark_queue_completed', 
              format('Still %s items remaining in phase %s for request %s', 
                     v_remaining_count, v_request.current_processing_phase, v_request.id), 
              now());
    END IF;
  
  EXCEPTION WHEN OTHERS THEN
    -- Capture the error details
    GET STACKED DIAGNOSTICS 
      v_error_msg = MESSAGE_TEXT,
      v_debug_info = PG_EXCEPTION_DETAIL;
    
    -- Log the error
    INSERT INTO public.error_log (flow_name, error, created_at) 
    VALUES ('mark_queue_completed_error', 
            format('Error in mark_queue_item_completed for queue %s: %s. Detail: %s', 
                   p_queue_id, v_error_msg, v_debug_info),
            now());
    
    -- Re-raise the exception
    RAISE EXCEPTION 'Error in mark_queue_item_completed: %', v_error_msg;
  END;
  
END;
$$;


ALTER FUNCTION "public"."mark_queue_item_completed"("p_queue_id" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."mark_queue_item_error"("p_queue_id" "uuid", "p_error_message" "text") RETURNS "void"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
  v_retry_limit INT := 3; -- Max number of retries
  v_current_retry_count INT;
BEGIN
  SELECT retry_count INTO v_current_retry_count
  FROM public.processing_queue
  WHERE id = p_queue_id;

  IF FOUND THEN
    IF v_current_retry_count < v_retry_limit THEN
      -- Increment retry count and set back to queued (could add delay logic here)
      UPDATE public.processing_queue
      SET status = 'queued', -- Or maybe a dedicated 'retry_queued' status?
          retry_count = v_current_retry_count + 1,
          updated_at = now() -- Reset updated_at for delay checks?
      WHERE id = p_queue_id;
      -- Log the error message somewhere if needed (e.g., an error log table or update requests.error_message)
      UPDATE public.requests SET error_message = 'Queue item ' || p_queue_id || ' failed: ' || p_error_message WHERE id = (SELECT request_id FROM processing_queue WHERE id = p_queue_id);

    ELSE
      -- Max retries reached, mark as permanent error
      UPDATE public.processing_queue
      SET status = 'error',
          retry_count = v_current_retry_count + 1,
          updated_at = now()
      WHERE id = p_queue_id;
      -- Update request status to ERROR
      UPDATE public.requests
      SET status = 'ERROR',
          error_message = 'Queue item ' || p_queue_id || ' failed after max retries: ' || p_error_message,
          updated_at = now()
      WHERE id = (SELECT request_id FROM processing_queue WHERE id = p_queue_id);
    END IF;
  ELSE
      RAISE WARNING 'Queue item % not found when trying to mark as error.', p_queue_id;
  END IF;
END;
$$;


ALTER FUNCTION "public"."mark_queue_item_error"("p_queue_id" "uuid", "p_error_message" "text") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."queue_additional_actes"("p_request_id" "uuid", "p_index_id" "uuid", "p_acte_ids" "uuid"[], "p_phase" integer) RETURNS "void"
    LANGUAGE "plpgsql"
    AS $$
BEGIN
  -- Queue additional actes for processing with same phase
  INSERT INTO processing_queue (request_id, document_type, document_id, parent_index_id, processing_phase, priority, status)
  SELECT 
    p_request_id as request_id,
    'acte' as document_type,
    id as document_id,
    p_index_id as parent_index_id,
    p_phase as processing_phase,
    CASE 
      WHEN p_phase = 2 THEN 500
      ELSE 100
    END as priority,
    'queued' as status
  FROM actes
  WHERE id = ANY(p_acte_ids)
  AND NOT EXISTS (
    SELECT 1 FROM processing_queue
    WHERE document_type = 'acte'
    AND document_id = actes.id
    AND status != 'completed'
  );
END;
$$;


ALTER FUNCTION "public"."queue_additional_actes"("p_request_id" "uuid", "p_index_id" "uuid", "p_acte_ids" "uuid"[], "p_phase" integer) OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."queue_eligible_documents_for_phase"("p_request_id" "uuid", "p_phase" integer) RETURNS "void"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
  v_count integer := 0;
  v_error_msg text;
  v_debug_info text;
BEGIN
  -- Log start of function
  INSERT INTO public.error_log (flow_name, error, created_at) 
  VALUES ('queue_documents', format('Starting queue_eligible_documents_for_phase for request %s phase %s', p_request_id, p_phase), now());
  
  BEGIN -- Use an inner block for exception handling
    -- Queue eligible documents based on phase
    IF p_phase = 1 THEN
      -- Phase 1: Queue indexes that are ready for analysis
      INSERT INTO public.processing_queue (request_id, document_type, document_id, processing_phase, priority, status)
      SELECT 
        i.request_id, 
        'index', 
        i.id, 
        1, 
        CASE WHEN i.index_initial THEN 1000 ELSE (1000 - (COALESCE(i.index_level, 1) * 10)) END, 
        'queued'
      FROM public.index i
      WHERE i.request_id = p_request_id
        AND i.doc_status = 'Ready for Analysis'
        AND i.index_status = 'Ready for Analysis'
        AND NOT EXISTS (
          SELECT 1 FROM public.processing_queue pq 
          WHERE pq.document_id = i.id 
          AND pq.document_type = 'index'
          AND pq.processing_phase = 1
          AND pq.status IN ('queued', 'processing')
        );
      
      GET DIAGNOSTICS v_count = ROW_COUNT;
      
      -- Log the result
      INSERT INTO public.error_log (flow_name, error, created_at) 
      VALUES ('queue_documents', format('Queued %s new indexes for Phase 1 of request %s', v_count, p_request_id), now());
      
      RAISE NOTICE '[queue_eligible_documents_for_phase] Queued % new indexes for Phase 1', v_count;
      
    ELSIF p_phase = 2 THEN
      -- Phase 2: First queue regular actes from completed indexes
      -- These are actes where source_type is 'index' (default) or NULL
      INSERT INTO public.processing_queue (request_id, document_type, document_id, parent_index_id, processing_phase, priority, status)
      SELECT 
        a.request_id, 
        'acte', 
        a.id, 
        a.index_id,
        2, 
        500 - (COALESCE(i.index_level, 1) * 10), 
        'queued'
      FROM public.actes a
      JOIN public.index i ON a.index_id = i.id
      WHERE a.request_id = p_request_id
        AND a.doc_status = 'Ready for Analysis'
        AND (a.source_type IS NULL OR a.source_type = 'index') -- Only include actes from indexes
        AND i.index_status = 'Analysis Completed'
        AND NOT EXISTS (
          SELECT 1 FROM public.processing_queue pq 
          WHERE pq.document_id = a.id 
          AND pq.document_type = 'acte'
          AND (pq.processing_phase = 2 OR pq.processing_phase = 3) -- Check both phases
          AND pq.status IN ('queued', 'processing', 'completed')
        );
        
      GET DIAGNOSTICS v_count = ROW_COUNT;
      
      -- Log the result
      INSERT INTO public.error_log (flow_name, error, created_at) 
      VALUES ('queue_documents', format('Queued %s new actes from indexes for Phase 2 of request %s', v_count, p_request_id), now());
      
      RAISE NOTICE '[queue_eligible_documents_for_phase] Queued % new actes from indexes for Phase 2', v_count;

      -- SPECIFIC FOR ACTES DISCOVERED FROM OTHER ACTES
      -- Queue actes where source_type is 'acte' with higher priority
      INSERT INTO public.processing_queue (request_id, document_type, document_id, parent_index_id, processing_phase, priority, status)
      SELECT 
        a.request_id, 
        'acte', 
        a.id, 
        a.index_id,
        2, 
        650, -- Higher priority than regular actes to process them immediately
        'queued'
      FROM public.actes a
      WHERE a.request_id = p_request_id
        AND a.doc_status = 'Ready for Analysis'
        AND a.source_type = 'acte' -- ONLY include actes from other actes
        AND NOT EXISTS (
          SELECT 1 FROM public.processing_queue pq 
          WHERE pq.document_id = a.id 
          AND pq.document_type = 'acte'
          AND (pq.processing_phase = 2 OR pq.processing_phase = 3) -- Check both phases
          AND pq.status IN ('queued', 'processing', 'completed')
        );
        
      GET DIAGNOSTICS v_count = ROW_COUNT;
      
      -- Log the result
      INSERT INTO public.error_log (flow_name, error, created_at) 
      VALUES ('queue_documents', format('Queued %s actes discovered from other actes for immediate processing in Phase 2 of request %s', v_count, p_request_id), now());
      
      RAISE NOTICE '[queue_eligible_documents_for_phase] Queued % actes discovered from other actes for immediate processing', v_count;
      
    ELSIF p_phase = 3 THEN
      -- Phase 3: Queue indexes for reanalysis 
      -- Log before update
      INSERT INTO public.error_log (flow_name, error, created_at) 
      VALUES ('queue_documents', format('Preparing Phase 3 for request %s - updating indexes', p_request_id), now());
      
      -- First, update the analysis_turn for all indexes
      UPDATE public.index
      SET analysis_turn = COALESCE(analysis_turn, 0) + 1,
          index_status = 'Ready for Analysis', -- Reset to ready for the new analysis turn
          doc_status = 'Ready for Analysis'    -- Reset doc status as well
      WHERE request_id = p_request_id
      AND index_status = 'Analysis Completed';
      
      GET DIAGNOSTICS v_count = ROW_COUNT;
      
      -- Log update result
      INSERT INTO public.error_log (flow_name, error, created_at) 
      VALUES ('queue_documents', format('Updated %s indexes for Phase 3 re-analysis of request %s', v_count, p_request_id), now());
      
      -- Now queue the indexes for reanalysis
      INSERT INTO public.processing_queue (request_id, document_type, document_id, processing_phase, priority, status)
      SELECT 
        i.request_id, 
        'index', 
        i.id, 
        3, 
        900 - (COALESCE(i.index_level, 1) * 10), -- High priority but lower than phase 1
        'queued'
      FROM public.index i
      WHERE i.request_id = p_request_id
        AND i.doc_status = 'Ready for Analysis'
        AND i.index_status = 'Ready for Analysis'
        AND NOT EXISTS (
          SELECT 1 FROM public.processing_queue pq 
          WHERE pq.document_id = i.id 
          AND pq.document_type = 'index'
          AND pq.processing_phase = 3
          AND pq.status IN ('queued', 'processing')
        );
        
      GET DIAGNOSTICS v_count = ROW_COUNT;
      
      -- Log queue result
      INSERT INTO public.error_log (flow_name, error, created_at) 
      VALUES ('queue_documents', format('Queued %s indexes for Phase 3 re-analysis of request %s', v_count, p_request_id), now());
      
      RAISE NOTICE '[queue_eligible_documents_for_phase] Queued % indexes for Phase 3 re-analysis', v_count;
      
      -- Queue any remaining regular actes from indexes
      INSERT INTO public.processing_queue (request_id, document_type, document_id, parent_index_id, processing_phase, priority, status)
      SELECT 
        a.request_id, 
        'acte', 
        a.id, 
        a.index_id,
        3, 
        600 - (COALESCE(i.index_level, 1) * 10), -- Medium priority 
        'queued'
      FROM public.actes a
      JOIN public.index i ON a.index_id = i.id
      WHERE a.request_id = p_request_id
        AND a.doc_status = 'Ready for Analysis'
        AND (a.source_type IS NULL OR a.source_type = 'index') -- Only include actes from indexes
        AND NOT EXISTS (
          SELECT 1 FROM public.processing_queue pq 
          WHERE pq.document_id = a.id 
          AND pq.document_type = 'acte'
          AND (pq.processing_phase = 2 OR pq.processing_phase = 3) -- Check both phases
          AND pq.status IN ('queued', 'processing', 'completed')
        );
        
      GET DIAGNOSTICS v_count = ROW_COUNT;
      
      -- Log acte queue result
      INSERT INTO public.error_log (flow_name, error, created_at) 
      VALUES ('queue_documents', format('Queued %s remaining actes from indexes for Phase 3 of request %s', v_count, p_request_id), now());
      
      RAISE NOTICE '[queue_eligible_documents_for_phase] Queued % remaining actes from indexes for Phase 3', v_count;
      
      -- Finally, queue any actes discovered from other actes with highest priority
      INSERT INTO public.processing_queue (request_id, document_type, document_id, parent_index_id, processing_phase, priority, status)
      SELECT 
        a.request_id, 
        'acte', 
        a.id, 
        a.index_id,
        3, 
        750, -- Higher priority than regular actes
        'queued'
      FROM public.actes a
      WHERE a.request_id = p_request_id
        AND a.doc_status = 'Ready for Analysis'
        AND a.source_type = 'acte' -- ONLY include actes from other actes
        AND NOT EXISTS (
          SELECT 1 FROM public.processing_queue pq 
          WHERE pq.document_id = a.id 
          AND pq.document_type = 'acte'
          AND (pq.processing_phase = 2 OR pq.processing_phase = 3) -- Check both phases
          AND pq.status IN ('queued', 'processing', 'completed')
        );
        
      GET DIAGNOSTICS v_count = ROW_COUNT;
      
      -- Log acte queue result
      INSERT INTO public.error_log (flow_name, error, created_at) 
      VALUES ('queue_documents', format('Queued %s remaining actes discovered from other actes for Phase 3 of request %s', v_count, p_request_id), now());
      
      RAISE NOTICE '[queue_eligible_documents_for_phase] Queued % remaining actes discovered from other actes for Phase 3', v_count;
    END IF;
  
  EXCEPTION WHEN OTHERS THEN
    -- Capture the error details
    GET STACKED DIAGNOSTICS 
      v_error_msg = MESSAGE_TEXT,
      v_debug_info = PG_EXCEPTION_DETAIL;
    
    -- Log the error
    INSERT INTO public.error_log (flow_name, error, created_at) 
    VALUES ('queue_documents_error', 
            format('Error in queue_eligible_documents_for_phase for request %s phase %s: %s. Detail: %s', 
                   p_request_id, p_phase, v_error_msg, v_debug_info),
            now());
    
    -- Re-raise the exception
    RAISE EXCEPTION 'Error in queue_eligible_documents_for_phase: %', v_error_msg;
  END;
  
END;
$$;


ALTER FUNCTION "public"."queue_eligible_documents_for_phase"("p_request_id" "uuid", "p_phase" integer) OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."queue_index_actes"("p_request_id" "uuid", "p_index_id" "uuid", "p_phase" integer, "p_index_level" integer) RETURNS "void"
    LANGUAGE "plpgsql"
    AS $$
BEGIN
  -- Insert queue items for actes (Phase 2 or 3 depending on current phase)
  INSERT INTO processing_queue (request_id, document_type, document_id, parent_index_id, processing_phase, priority, status)
  SELECT 
    p_request_id as request_id,
    'acte' as document_type,
    id as document_id,
    p_index_id as parent_index_id,
    CASE WHEN p_phase = 1 THEN 2 ELSE 3 END as processing_phase,
    CASE 
      WHEN p_phase = 1 THEN (500 - (p_index_level * 10))
      ELSE (100 - (p_index_level * 10))
    END as priority,
    'queued' as status
  FROM actes
  WHERE request_id = p_request_id
    AND index_id = p_index_id
    AND NOT EXISTS (
      SELECT 1 FROM processing_queue
      WHERE document_type = 'acte'
      AND document_id = actes.id
      AND (processing_phase = 2 OR processing_phase = 3)
      AND status != 'completed'
    );
END;
$$;


ALTER FUNCTION "public"."queue_index_actes"("p_request_id" "uuid", "p_index_id" "uuid", "p_phase" integer, "p_index_level" integer) OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."queue_index_concordances"("p_request_id" "uuid", "p_source_index_id" "uuid") RETURNS "void"
    LANGUAGE "plpgsql"
    AS $$
BEGIN
  -- Insert queue items for concordances (Phase 1)
  INSERT INTO processing_queue (request_id, document_type, document_id, parent_index_id, processing_phase, priority, status)
  SELECT 
    p_request_id as request_id,
    'index' as document_type,
    id as document_id,
    NULL as parent_index_id,
    1 as processing_phase,
    (1000 - (COALESCE(index_level, 1) * 10)) as priority,
    'queued' as status
  FROM index
  WHERE request_id = p_request_id
    AND id != p_source_index_id
    AND index_initial = false
    AND NOT EXISTS (
      SELECT 1 FROM processing_queue 
      WHERE document_type = 'index' 
      AND document_id = index.id
      AND processing_phase = 1
      AND status != 'completed'
    );
END;
$$;


ALTER FUNCTION "public"."queue_index_concordances"("p_request_id" "uuid", "p_source_index_id" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."queue_index_reanalysis"("p_request_id" "uuid", "p_index_id" "uuid", "p_index_level" integer, "p_phase" integer) RETURNS "void"
    LANGUAGE "plpgsql"
    AS $$
BEGIN
  -- Only queue for re-analysis if we're in phase 2
  IF p_phase = 2 THEN
    -- Update index status to trigger re-analysis
    UPDATE index
    SET index_status = 'Ready for Analysis'
    WHERE id = p_index_id;
    
    -- Add index to queue for phase 3
    INSERT INTO processing_queue (
      request_id, document_type, document_id, parent_index_id, 
      processing_phase, priority, status
    )
    VALUES (
      p_request_id,
      'index',
      p_index_id,
      NULL,
      3,
      (100 - (p_index_level * 10)),
      'queued'
    )
    ON CONFLICT (document_type, document_id, status) 
    DO NOTHING;
  END IF;
END;
$$;


ALTER FUNCTION "public"."queue_index_reanalysis"("p_request_id" "uuid", "p_index_id" "uuid", "p_index_level" integer, "p_phase" integer) OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."ready_for_analysis"("doc_type" "text", "doc_id" "uuid") RETURNS boolean
    LANGUAGE "plpgsql"
    AS $$
BEGIN
    IF doc_type = 'index' THEN
        RETURN EXISTS (SELECT 1 FROM index WHERE id = doc_id AND doc_status = 'Ready for Analysis');
    ELSIF doc_type = 'acte' THEN
        RETURN EXISTS (SELECT 1 FROM actes WHERE id = doc_id AND doc_status = 'Ready for Analysis');
    ELSE
        RETURN FALSE;
    END IF;
END;
$$;


ALTER FUNCTION "public"."ready_for_analysis"("doc_type" "text", "doc_id" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."trigger_final_doc_creation"() RETURNS "trigger"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
  webhook_url TEXT := 'https://n8n.paraito.ca/webhook/final-doc-creator';
  v_request_id TEXT;
BEGIN
  -- Only trigger for requests transitioning to FINALIZING state
  IF (TG_OP = 'UPDATE') 
     AND (OLD.status <> 'FINALIZING') 
     AND (NEW.status = 'FINALIZING') THEN
     
    -- Log the webhook call
    RAISE NOTICE '[trigger_final_doc_creation] Request % is being finalized. Triggering webhook.', NEW.id;
    
    -- Store the request ID for the webhook
    v_request_id := NEW.id::TEXT;
    
    -- We can't directly call external webhooks from a trigger function,
    -- but we can log it as a notice and provide guidance
    RAISE NOTICE '[trigger_final_doc_creation] Would call webhook: % with request_id=%', webhook_url, v_request_id;
    RAISE NOTICE '[trigger_final_doc_creation] This must be implemented via an external service like n8n monitoring the requests';
    
    -- In a real implementation, you might:
    -- 1. Insert into a webhook_queue table that an external service monitors
    -- 2. Use pg_notify to signal an external listener
    -- 3. Have a cron job that checks for requests in FINALIZING state
    
    -- For now, we'll simulate this with a notice and rely on external monitoring
  END IF;
  
  RETURN NEW;
END;
$$;


ALTER FUNCTION "public"."trigger_final_doc_creation"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."trigger_final_doc_webhook"() RETURNS "trigger"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
BEGIN
  -- Only trigger when status changes to FINALIZING
  IF (TG_OP = 'UPDATE') 
     AND (OLD.status <> 'FINALIZING') 
     AND (NEW.status = 'FINALIZING') THEN
     
    -- Log that webhook should be called
    INSERT INTO public.error_log (flow_name, error, created_at)
    VALUES (
      'Final Doc Webhook', 
      format('WEBHOOK NEEDED: Request ID %s changed to FINALIZING. Call webhook with body {"request_id": "%s"}', NEW.id, NEW.id),
      now()
    );
    
    RAISE NOTICE 'WEBHOOK NEEDED: Call https://n8n.paraito.ca/webhook/final-doc-creator with request_id=%', NEW.id;
  END IF;
  
  RETURN NEW;
END;
$$;


ALTER FUNCTION "public"."trigger_final_doc_webhook"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."update_acte_document_completed"() RETURNS "trigger"
    LANGUAGE "plpgsql"
    AS $$
BEGIN
    -- IF status = Analysis Completed OR Document not Available, set document_completed to true
    IF (NEW.status = 'Analysis Completed' OR NEW.status = 'Document not Available') THEN
        NEW.document_completed := true;
    ELSE
        NEW.document_completed := false;
    END IF;
    RETURN NEW;
END;
$$;


ALTER FUNCTION "public"."update_acte_document_completed"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."update_acte_document_ready"() RETURNS "trigger"
    LANGUAGE "plpgsql"
    AS $$
BEGIN
    IF (NEW.doc_id IS NOT NULL) THEN
        NEW.document_ready := true;
    ELSE
        NEW.document_ready := false;
    END IF;
    RETURN NEW;
END;
$$;


ALTER FUNCTION "public"."update_acte_document_ready"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."update_acte_status_on_acquisition_round"() RETURNS "trigger"
    LANGUAGE "plpgsql"
    AS $$
BEGIN
    -- If document_acquisition_round = 3 AND doc_id does not exist, set status to 'Document not Available'
    IF (NEW.document_acquisition_round = 3 AND NEW.doc_id IS NULL) THEN
        NEW.status := 'Document not Available';
    END IF;
    RETURN NEW;
END;
$$;


ALTER FUNCTION "public"."update_acte_status_on_acquisition_round"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."update_complete_summary"() RETURNS "trigger"
    LANGUAGE "plpgsql"
    AS $$
DECLARE
  req_summary TEXT;
  idx_summaries TEXT[];
  act_summaries TEXT[];
BEGIN
  -- Get request summary
  SELECT request_summary INTO req_summary 
  FROM requests 
  WHERE id = NEW.request_id;
  
  -- Get index summaries
  SELECT array_agg(index_summary) INTO idx_summaries 
  FROM index 
  WHERE request_id = NEW.request_id 
  AND index_summary IS NOT NULL;
  
  -- Get acte summaries
  SELECT array_agg(acte_summary) INTO act_summaries 
  FROM actes 
  WHERE request_id = NEW.request_id 
  AND acte_summary IS NOT NULL;
  
  -- Combine all summaries
  UPDATE requests
  SET complete_summary = 
    ARRAY[req_summary] || 
    COALESCE(idx_summaries, ARRAY[]::TEXT[]) || 
    COALESCE(act_summaries, ARRAY[]::TEXT[])
  WHERE id = NEW.request_id;
  
  RETURN NEW;
END;
$$;


ALTER FUNCTION "public"."update_complete_summary"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."update_index_acte_counts"() RETURNS "trigger"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
    affected_index_id UUID;
BEGIN
    -- Determine which index_id we need to update based on operation
    IF TG_OP = 'DELETE' THEN
        affected_index_id := OLD.index_id;
        RAISE NOTICE 'DELETE operation for acte_id=%, index_id=%', OLD.id, affected_index_id;
    ELSIF TG_OP = 'UPDATE' THEN 
        -- If index_id changed, handle the old one separately first
        IF OLD.index_id IS DISTINCT FROM NEW.index_id AND OLD.index_id IS NOT NULL THEN
            RAISE NOTICE 'UPDATE operation (index_id changed): Updating old index_id=% for acte_id=%', OLD.index_id, NEW.id;
            
            -- Update the OLD index_id first
            EXECUTE format('
                UPDATE public.index 
                SET 
                    related_actes = (
                        SELECT COUNT(*) FROM public.actes 
                        WHERE actes.index_id = %L
                    ),
                    actes_completed = (
                        SELECT COUNT(*) FROM public.actes 
                        WHERE actes.index_id = %L AND actes.document_completed = TRUE
                    )
                WHERE id = %L',
                OLD.index_id, OLD.index_id, OLD.index_id
            );
        END IF;
        
        -- Now handle the current (new) index_id
        affected_index_id := NEW.index_id;
        RAISE NOTICE 'UPDATE operation for acte_id=%, new index_id=%, document_completed=%', 
                     NEW.id, affected_index_id, NEW.document_completed;
    ELSE -- INSERT
        affected_index_id := NEW.index_id;
        RAISE NOTICE 'INSERT operation for acte_id=%, index_id=%', NEW.id, affected_index_id;
    END IF;
    
    -- Skip if index_id is NULL
    IF affected_index_id IS NULL THEN
        RAISE NOTICE 'Skipping update as affected_index_id is NULL';
        RETURN NULL;
    END IF;
    
    -- Update the counts for the affected index
    RAISE NOTICE 'Updating counts for index_id=%', affected_index_id;
    
    EXECUTE format('
        UPDATE public.index 
        SET 
            related_actes = (
                SELECT COUNT(*) FROM public.actes 
                WHERE actes.index_id = %L
            ),
            actes_completed = (
                SELECT COUNT(*) FROM public.actes 
                WHERE actes.index_id = %L AND actes.document_completed = TRUE
            )
        WHERE id = %L',
        affected_index_id, affected_index_id, affected_index_id
    );
    
    RAISE NOTICE 'Update completed successfully for index_id=%', affected_index_id;
    
    RETURN NULL;
END;
$$;


ALTER FUNCTION "public"."update_index_acte_counts"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."update_index_actes_completed_count"() RETURNS "trigger"
    LANGUAGE "plpgsql"
    AS $$
DECLARE
    idx_id uuid;
BEGIN
    IF TG_OP = 'DELETE' THEN
        idx_id := OLD.index_id;
    ELSE
        idx_id := NEW.index_id;
    END IF;

    -- Update actes_completed count in the index table
    UPDATE public.index
    SET actes_completed = (SELECT COUNT(*) FROM public.actes WHERE index_id = idx_id AND document_completed = true)
    WHERE id = idx_id;
    
    RETURN NULL;
END;
$$;


ALTER FUNCTION "public"."update_index_actes_completed_count"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."update_index_document_ready"() RETURNS "trigger"
    LANGUAGE "plpgsql"
    AS $$
BEGIN
    IF (NEW.doc_id IS NOT NULL) THEN
        NEW.document_ready := true;
    ELSE
        NEW.document_ready := false;
    END IF;
    RETURN NEW;
END;
$$;


ALTER FUNCTION "public"."update_index_document_ready"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."update_index_is_completed"() RETURNS "trigger"
    LANGUAGE "plpgsql"
    AS $$
BEGIN
    -- If status = Completed, set is_completed to true
    IF (NEW.status = 'Completed') THEN
        NEW.is_completed := true;
    ELSE
        NEW.is_completed := false;
    END IF;
    RETURN NEW;
END;
$$;


ALTER FUNCTION "public"."update_index_is_completed"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."update_index_phase1_completed"() RETURNS "trigger"
    LANGUAGE "plpgsql"
    AS $$
BEGIN
    -- IF phase_1_status = Analysis Completed OR Document not Available, set phase_1_completed to true
    IF (NEW.phase_1_status = 'Analysis Completed' OR NEW.phase_1_status = 'Document not Available') THEN
        NEW.phase_1_completed := true;
    ELSE
        NEW.phase_1_completed := false;
    END IF;
    RETURN NEW;
END;
$$;


ALTER FUNCTION "public"."update_index_phase1_completed"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."update_index_phase1_status_on_acquisition_round"() RETURNS "trigger"
    LANGUAGE "plpgsql"
    AS $$
BEGIN
    -- If document_acquisition_round = 3 AND doc_id does not exist, set status to 'Document not Available'
    IF (NEW.document_acquisition_round = 3 AND NEW.doc_id IS NULL) THEN
        NEW.phase_1_status := 'Document not Available';
    END IF;
    RETURN NEW;
END;
$$;


ALTER FUNCTION "public"."update_index_phase1_status_on_acquisition_round"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."update_index_phase2_completed"() RETURNS "trigger"
    LANGUAGE "plpgsql"
    AS $$
BEGIN
    -- Set phase_2_completed to true when phase_2_status = 'Analysis Completed'
    IF (NEW.phase_2_status = 'Analysis Completed') THEN
        NEW.phase_2_completed := true;
    ELSE
        NEW.phase_2_completed := false;
    END IF;
    RETURN NEW;
END;
$$;


ALTER FUNCTION "public"."update_index_phase2_completed"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."update_index_phase2_completed_after"() RETURNS "trigger"
    LANGUAGE "plpgsql"
    AS $$
DECLARE
    should_complete boolean;
BEGIN
    -- Determine if phase_2 should be completed
    should_complete := (NEW.phase_2_status = 'Analysis Completed');
    
    -- If phase_2_completed would change, update it directly
    IF (NEW.phase_2_completed IS DISTINCT FROM should_complete) THEN
        UPDATE public.index
        SET phase_2_completed = should_complete
        WHERE id = NEW.id;
    END IF;
    
    RETURN NULL;
END;
$$;


ALTER FUNCTION "public"."update_index_phase2_completed_after"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."update_index_phase2_status"() RETURNS "trigger"
    LANGUAGE "plpgsql"
    AS $$
BEGIN
    -- If related_actes equals actes_completed and both are greater than 0, set phase_2_status to 'Analysis Completed'
    IF (NEW.related_actes = NEW.actes_completed AND NEW.related_actes > 0) THEN
        NEW.phase_2_status := 'Analysis Completed';
    ELSIF (NEW.actes_completed > 0) THEN
        NEW.phase_2_status := 'Actes Analysis in Progress';
    ELSE
        NEW.phase_2_status := 'Pending';
    END IF;
    RETURN NEW;
END;
$$;


ALTER FUNCTION "public"."update_index_phase2_status"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."update_index_phase2_status_after"() RETURNS "trigger"
    LANGUAGE "plpgsql"
    AS $$
DECLARE
    new_status phase_2_status_enum;
BEGIN
    -- Determine the new status
    IF (NEW.related_actes = NEW.actes_completed AND NEW.related_actes > 0) THEN
        new_status := 'Analysis Completed';
    ELSIF (NEW.actes_completed > 0) THEN
        new_status := 'Actes Analysis in Progress';
    ELSE
        new_status := 'Pending';
    END IF;
    
    -- If the status would change, update it directly
    IF (NEW.phase_2_status IS DISTINCT FROM new_status) THEN
        UPDATE public.index
        SET phase_2_status = new_status
        WHERE id = NEW.id;
    END IF;
    
    RETURN NULL;
END;
$$;


ALTER FUNCTION "public"."update_index_phase2_status_after"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."update_index_related_actes_count"() RETURNS "trigger"
    LANGUAGE "plpgsql"
    AS $$
DECLARE
    idx_id uuid;
BEGIN
    IF TG_OP = 'DELETE' THEN
        idx_id := OLD.index_id;
    ELSE
        idx_id := NEW.index_id;
    END IF;

    -- Update related_actes count in the index table
    UPDATE public.index
    SET related_actes = (SELECT COUNT(*) FROM public.actes WHERE index_id = idx_id)
    WHERE id = idx_id;
    
    RETURN NULL;
END;
$$;


ALTER FUNCTION "public"."update_index_related_actes_count"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."update_index_status"() RETURNS "trigger"
    LANGUAGE "plpgsql"
    AS $$
BEGIN
    -- If already in Phase 3, never revert back to Phase 2
    IF (OLD.status = 'Phase 3' AND NEW.status != 'Completed') THEN
        NEW.status := 'Phase 3';
    -- Otherwise, apply normal status logic
    ELSIF (NEW.phase_1_completed = true AND NEW.phase_2_completed = true AND NEW.phase_3_completed = true) THEN
        NEW.status := 'Completed';
    ELSIF (NEW.phase_1_completed = true AND NEW.phase_2_completed = true) THEN
        NEW.status := 'Phase 3';
    ELSIF (NEW.phase_1_completed = true) THEN
        NEW.status := 'Phase 2';
    ELSE
        NEW.status := 'Phase 1';
    END IF;
    RETURN NEW;
END;
$$;


ALTER FUNCTION "public"."update_index_status"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."update_modified_column"() RETURNS "trigger"
    LANGUAGE "plpgsql"
    AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW; 
END;
$$;


ALTER FUNCTION "public"."update_modified_column"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."update_request_counters"() RETURNS "trigger"
    LANGUAGE "plpgsql"
    AS $$
BEGIN
  -- Update number_of_index
  UPDATE requests
  SET number_of_index = (
    SELECT COUNT(*) FROM index WHERE request_id = NEW.request_id
  )
  WHERE id = NEW.request_id;
  
  RETURN NEW;
END;
$$;


ALTER FUNCTION "public"."update_request_counters"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."update_request_index_completed_count"() RETURNS "trigger"
    LANGUAGE "plpgsql"
    AS $$
DECLARE
    req_id uuid;
BEGIN
    IF TG_OP = 'DELETE' THEN
        req_id := OLD.request_id;
    ELSE
        req_id := NEW.request_id;
    END IF;

    -- Update index_completed count in the requests table
    UPDATE public.requests
    SET index_completed = (SELECT COUNT(*) FROM public.index WHERE request_id = req_id AND is_completed = true)
    WHERE id = req_id;
    
    RETURN NULL;
END;
$$;


ALTER FUNCTION "public"."update_request_index_completed_count"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."update_timestamp"() RETURNS "trigger"
    LANGUAGE "plpgsql"
    AS $$
BEGIN
   NEW.updated_at = NOW();
   RETURN NEW;
END;
$$;


ALTER FUNCTION "public"."update_timestamp"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."update_updated_at_column"() RETURNS "trigger"
    LANGUAGE "plpgsql"
    AS $$
BEGIN
   NEW.updated_at = now();
   RETURN NEW;
END;
$$;


ALTER FUNCTION "public"."update_updated_at_column"() OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."acte_types" (
    "id" integer NOT NULL,
    "category" "text" NOT NULL,
    "acte_type" "text" NOT NULL,
    "extraction_analysis" "text",
    "edge_cases" "text"
);


ALTER TABLE "public"."acte_types" OWNER TO "postgres";


CREATE SEQUENCE IF NOT EXISTS "public"."acte_types_id_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE "public"."acte_types_id_seq" OWNER TO "postgres";


ALTER SEQUENCE "public"."acte_types_id_seq" OWNED BY "public"."acte_types"."id";



CREATE TABLE IF NOT EXISTS "public"."actes_documents" (
    "acte_publication_number" "text" NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "acte_type" integer,
    "file_content" "text",
    "publication_date" "date",
    "alternative_names" "jsonb",
    "document_id" "text",
    "document_url" "text",
    "doc_summary" "text"
);


ALTER TABLE "public"."actes_documents" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."chat_sessions" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "user_id" "uuid",
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "updated_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "metadata" "jsonb"
);


ALTER TABLE "public"."chat_sessions" OWNER TO "postgres";


COMMENT ON TABLE "public"."chat_sessions" IS 'Stores individual conversation threads or sessions.';



COMMENT ON COLUMN "public"."chat_sessions"."id" IS 'Unique identifier for the chat session.';



COMMENT ON COLUMN "public"."chat_sessions"."user_id" IS 'Foreign key linking to the authenticated user (auth.users table). Nullable for anonymous chats.';



COMMENT ON COLUMN "public"."chat_sessions"."created_at" IS 'Timestamp indicating when the chat session was created.';



COMMENT ON COLUMN "public"."chat_sessions"."updated_at" IS 'Timestamp indicating the last activity within the session.';



COMMENT ON COLUMN "public"."chat_sessions"."metadata" IS 'JSONB field for storing arbitrary session-related metadata.';



CREATE TABLE IF NOT EXISTS "public"."dates" (
    "id" bigint NOT NULL,
    "legal_document_id" bigint,
    "date" timestamp with time zone NOT NULL,
    "date_type" "text",
    "range_start" timestamp with time zone,
    "range_end" timestamp with time zone
);


ALTER TABLE "public"."dates" OWNER TO "postgres";


ALTER TABLE "public"."dates" ALTER COLUMN "id" ADD GENERATED ALWAYS AS IDENTITY (
    SEQUENCE NAME "public"."dates_id_seq"
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);



CREATE TABLE IF NOT EXISTS "public"."error_log" (
    "id" bigint NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "flow_name" "text",
    "error" "text"
);


ALTER TABLE "public"."error_log" OWNER TO "postgres";


ALTER TABLE "public"."error_log" ALTER COLUMN "id" ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME "public"."error_log_id_seq"
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);



CREATE TABLE IF NOT EXISTS "public"."extraction_queue" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "document_source" "public"."document_source_enum" NOT NULL,
    "document_number" "text" NOT NULL,
    "circonscription_fonciere" "text",
    "acte_type" "public"."acte_type_enum",
    "cadastre" "text",
    "designation_secondaire" "text",
    "status" "public"."extraction_status_enum" DEFAULT 'En_attente'::"public"."extraction_status_enum" NOT NULL,
    "doc_id" "text",
    "doc_url" "text",
    "local_file_path" "text",
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "updated_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "retry_count" integer DEFAULT 0 NOT NULL,
    "error_message" "text",
    "processing_started_at" timestamp with time zone,
    "acte_id" "uuid",
    "index_id" "uuid",
    "logged" boolean DEFAULT false NOT NULL,
    "file_content" "text",
    "request_id" "uuid",
    CONSTRAINT "extraction_queue_status_check" CHECK ((("status")::"text" = ANY (ARRAY['En_attente'::"text", 'En_traitement'::"text", 'Telecharge'::"text", 'Disponible_sur_Drive'::"text", 'Document_introuvable'::"text"])))
);


ALTER TABLE "public"."extraction_queue" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."index" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "request_id" "uuid" NOT NULL,
    "lot_number" "text",
    "circonscription" "text",
    "cadastre" "text",
    "doc_id" "text",
    "doc_url" "text",
    "index_summary" "text",
    "relevance_explanation" "text",
    "relevance_rating" integer,
    "created_at" timestamp with time zone DEFAULT "now"(),
    "updated_at" timestamp with time zone DEFAULT "now"(),
    "designation_secondaire" "text",
    "index_initial" boolean DEFAULT false NOT NULL,
    "document_ready" boolean DEFAULT false NOT NULL,
    "phase_1_status" "public"."index_phase_status_enum" DEFAULT 'Pending'::"public"."index_phase_status_enum" NOT NULL,
    "document_acquisition_round" integer DEFAULT 0 NOT NULL,
    "phase_1_completed" boolean DEFAULT false NOT NULL,
    "related_actes" integer DEFAULT 0 NOT NULL,
    "actes_completed" integer DEFAULT 0 NOT NULL,
    "phase_2_completed" boolean DEFAULT false NOT NULL,
    "phase_3_completed" boolean DEFAULT false NOT NULL,
    "status" "public"."index_status_enum" DEFAULT 'Phase 1'::"public"."index_status_enum" NOT NULL,
    "is_completed" boolean DEFAULT false NOT NULL,
    "doc_number" numeric,
    "phase_2_status" "public"."phase_2_status_enum" DEFAULT 'Pending'::"public"."phase_2_status_enum" NOT NULL,
    "file_content" "text"
);


ALTER TABLE "public"."index" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."key_clauses_provisions" (
    "id" bigint NOT NULL,
    "legal_document_id" bigint,
    "clause_title" "text" NOT NULL,
    "clause_summary" "text" NOT NULL
);


ALTER TABLE "public"."key_clauses_provisions" OWNER TO "postgres";


ALTER TABLE "public"."key_clauses_provisions" ALTER COLUMN "id" ADD GENERATED ALWAYS AS IDENTITY (
    SEQUENCE NAME "public"."key_clauses_provisions_id_seq"
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);



CREATE TABLE IF NOT EXISTS "public"."legal_documents" (
    "id" bigint NOT NULL,
    "acte_publication_number" "text",
    "summary" "text",
    "file_content" "text",
    "publication_date" timestamp with time zone
);


ALTER TABLE "public"."legal_documents" OWNER TO "postgres";


ALTER TABLE "public"."legal_documents" ALTER COLUMN "id" ADD GENERATED ALWAYS AS IDENTITY (
    SEQUENCE NAME "public"."legal_documents_id_seq"
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);



CREATE TABLE IF NOT EXISTS "public"."messages" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "chat_session_id" "uuid" NOT NULL,
    "user_id" "uuid",
    "sender" "text" NOT NULL,
    "content" "text" NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "metadata" "jsonb",
    "request_id" "uuid",
    CONSTRAINT "messages_sender_check" CHECK (("sender" = ANY (ARRAY['user'::"text", 'bot'::"text", 'assistant'::"text"])))
);


ALTER TABLE "public"."messages" OWNER TO "postgres";


COMMENT ON TABLE "public"."messages" IS 'Stores individual messages belonging to a chat session.';



COMMENT ON COLUMN "public"."messages"."id" IS 'Unique identifier for the message.';



COMMENT ON COLUMN "public"."messages"."chat_session_id" IS 'Foreign key linking to the chat_sessions table. Messages are deleted if the session is deleted (ON DELETE CASCADE).';



COMMENT ON COLUMN "public"."messages"."user_id" IS 'Foreign key linking to the authenticated user who sent the message (if sender is ''user'').';



COMMENT ON COLUMN "public"."messages"."sender" IS 'Indicates if the message originated from the ''user'' or the ''bot'' (or ''assistant'').';



COMMENT ON COLUMN "public"."messages"."content" IS 'The textual content of the message.';



COMMENT ON COLUMN "public"."messages"."created_at" IS 'Timestamp indicating when the message was created.';



COMMENT ON COLUMN "public"."messages"."metadata" IS 'JSONB field for storing arbitrary message-related metadata.';



CREATE TABLE IF NOT EXISTS "public"."notary_and_references" (
    "id" bigint NOT NULL,
    "legal_document_id" bigint,
    "notary_name" "text",
    "jurisdiction" "text",
    "location" "text",
    "legal_references" "text"[]
);


ALTER TABLE "public"."notary_and_references" OWNER TO "postgres";


ALTER TABLE "public"."notary_and_references" ALTER COLUMN "id" ADD GENERATED ALWAYS AS IDENTITY (
    SEQUENCE NAME "public"."notary_and_references_id_seq"
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);



CREATE TABLE IF NOT EXISTS "public"."notifications" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "user_id" "uuid" NOT NULL,
    "title" "text" NOT NULL,
    "content" "text" NOT NULL,
    "request_id" "uuid",
    "is_read" boolean DEFAULT false,
    "created_at" timestamp with time zone DEFAULT "now"(),
    "updated_at" timestamp with time zone DEFAULT "now"()
);


ALTER TABLE "public"."notifications" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."parties" (
    "id" bigint NOT NULL,
    "legal_document_id" bigint,
    "name" "text" NOT NULL,
    "type" "text" NOT NULL,
    "legal_form" "text",
    "address" "text",
    "role" "text",
    "authorized_representative" "text",
    "legal_identifiers" "text"
);


ALTER TABLE "public"."parties" OWNER TO "postgres";


ALTER TABLE "public"."parties" ALTER COLUMN "id" ADD GENERATED ALWAYS AS IDENTITY (
    SEQUENCE NAME "public"."parties_id_seq"
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);



CREATE TABLE IF NOT EXISTS "public"."profiles" (
    "id" "uuid" NOT NULL,
    "full_name" "text",
    "company" "text",
    "avatar_url" "text",
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "updated_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "template_id" "text",
    "is_approved" boolean DEFAULT false NOT NULL,
    "Folder_url" "text",
    "File_instructions" "text"
);


ALTER TABLE "public"."profiles" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."requests" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "user_id" "uuid" NOT NULL,
    "request_summary" "text" NOT NULL,
    "seller_name" "text" NOT NULL,
    "seller_address" "text" NOT NULL,
    "complete_summary" "text"[] DEFAULT '{}'::"text"[],
    "number_of_index" integer DEFAULT 0,
    "number_of_actes" integer DEFAULT 0,
    "created_at" timestamp with time zone DEFAULT "now"(),
    "updated_at" timestamp with time zone DEFAULT "now"(),
    "folder_id" "text",
    "final_doc_id" "text",
    "folder_link" "text",
    "final_doc_link" "text",
    "long_term_memory_doc_id" "text",
    "sales_years" numeric DEFAULT '10'::numeric,
    "hypotheques_years" numeric DEFAULT '30'::numeric,
    "inclure_actes_radies" boolean DEFAULT true,
    "status" "text" DEFAULT 'Phase 1'::"text" NOT NULL,
    "completed_at" timestamp with time zone,
    "index_completed" integer DEFAULT 0 NOT NULL,
    "resume_etapes_recherche" "text",
    "servitudes" "text",
    "regimes_matrimoniaux" "text",
    "erreurs" "text",
    "autres_considerations" "text",
    "circonscription" "text"
);


ALTER TABLE "public"."requests" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."signatures_attestations" (
    "id" bigint NOT NULL,
    "legal_document_id" bigint,
    "signature_text" "text"
);


ALTER TABLE "public"."signatures_attestations" OWNER TO "postgres";


ALTER TABLE "public"."signatures_attestations" ALTER COLUMN "id" ADD GENERATED ALWAYS AS IDENTITY (
    SEQUENCE NAME "public"."signatures_attestations_id_seq"
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);



CREATE TABLE IF NOT EXISTS "public"."workflow_state" (
    "in_progress" boolean DEFAULT false,
    "workflow" "text" DEFAULT 'workflow'::"text" NOT NULL
);


ALTER TABLE "public"."workflow_state" OWNER TO "postgres";


ALTER TABLE ONLY "public"."acte_types" ALTER COLUMN "id" SET DEFAULT "nextval"('"public"."acte_types_id_seq"'::"regclass");



ALTER TABLE ONLY "public"."acte_types"
    ADD CONSTRAINT "acte_types_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."actes_documents"
    ADD CONSTRAINT "actes_documents_acte_publication_number_key" UNIQUE ("acte_publication_number");



ALTER TABLE ONLY "public"."actes_documents"
    ADD CONSTRAINT "actes_documents_pkey" PRIMARY KEY ("acte_publication_number");



ALTER TABLE ONLY "public"."actes"
    ADD CONSTRAINT "actes_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."chat_sessions"
    ADD CONSTRAINT "chat_sessions_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."dates"
    ADD CONSTRAINT "dates_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."error_log"
    ADD CONSTRAINT "error_log_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."extraction_queue"
    ADD CONSTRAINT "extraction_queue_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."index"
    ADD CONSTRAINT "index_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."key_clauses_provisions"
    ADD CONSTRAINT "key_clauses_provisions_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."legal_documents"
    ADD CONSTRAINT "legal_documents_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."messages"
    ADD CONSTRAINT "messages_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."notary_and_references"
    ADD CONSTRAINT "notary_and_references_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."notifications"
    ADD CONSTRAINT "notifications_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."parties"
    ADD CONSTRAINT "parties_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."profiles"
    ADD CONSTRAINT "profiles_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."requests"
    ADD CONSTRAINT "requests_id_key" UNIQUE ("id");



ALTER TABLE ONLY "public"."requests"
    ADD CONSTRAINT "requests_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."signatures_attestations"
    ADD CONSTRAINT "signatures_attestations_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."workflow_state"
    ADD CONSTRAINT "workflow_state_pkey" PRIMARY KEY ("workflow");



CREATE INDEX "idx_actes_index_id" ON "public"."actes" USING "btree" ("index_id");



CREATE INDEX "idx_actes_request_id" ON "public"."actes" USING "btree" ("request_id");



CREATE INDEX "idx_chat_sessions_user_id" ON "public"."chat_sessions" USING "btree" ("user_id");



CREATE INDEX "idx_extraction_queue_acte_id" ON "public"."extraction_queue" USING "btree" ("acte_id");



CREATE INDEX "idx_extraction_queue_created_at" ON "public"."extraction_queue" USING "btree" ("created_at");



CREATE INDEX "idx_extraction_queue_index_id" ON "public"."extraction_queue" USING "btree" ("index_id");



CREATE INDEX "idx_extraction_queue_retry_count" ON "public"."extraction_queue" USING "btree" ("retry_count");



CREATE INDEX "idx_extraction_queue_status" ON "public"."extraction_queue" USING "btree" ("status");



CREATE INDEX "idx_index_request_id" ON "public"."index" USING "btree" ("request_id");



CREATE INDEX "idx_messages_chat_session_id" ON "public"."messages" USING "btree" ("chat_session_id");



CREATE INDEX "idx_messages_created_at" ON "public"."messages" USING "btree" ("created_at");



CREATE INDEX "idx_messages_user_id" ON "public"."messages" USING "btree" ("user_id");



-- CREATE OR REPLACE TRIGGER "Trigger n8n on new user message" AFTER INSERT ON "public"."messages" FOR EACH ROW EXECUTE FUNCTION "supabase_functions"."http_request"('https://n8n.paraito.ca/webhook/chatbot', 'POST', '{"Content-type":"application/json"}', '{}', '5000');



CREATE OR REPLACE TRIGGER "actes_summary_trigger" AFTER INSERT OR UPDATE OF "acte_summary" ON "public"."actes" FOR EACH ROW EXECUTE FUNCTION "public"."update_complete_summary"();



CREATE OR REPLACE TRIGGER "extraction_queue_business_rules_trigger" BEFORE INSERT OR UPDATE ON "public"."extraction_queue" FOR EACH ROW EXECUTE FUNCTION "public"."handle_extraction_queue_logic"();



CREATE OR REPLACE TRIGGER "extraction_queue_updated_at_trigger" BEFORE UPDATE ON "public"."extraction_queue" FOR EACH ROW EXECUTE FUNCTION "public"."update_updated_at_column"();



CREATE OR REPLACE TRIGGER "index_delete_trigger" AFTER DELETE ON "public"."index" FOR EACH ROW EXECUTE FUNCTION "public"."update_request_counters"();



CREATE OR REPLACE TRIGGER "index_insert_trigger" AFTER INSERT ON "public"."index" FOR EACH ROW EXECUTE FUNCTION "public"."update_request_counters"();



CREATE OR REPLACE TRIGGER "index_summary_trigger" AFTER INSERT OR UPDATE OF "index_summary" ON "public"."index" FOR EACH ROW EXECUTE FUNCTION "public"."update_complete_summary"();



-- CREATE OR REPLACE TRIGGER "new_request" AFTER INSERT ON "public"."requests" FOR EACH ROW EXECUTE FUNCTION "supabase_functions"."http_request"('http://n8n.paraito.ca/webhook/new-request', 'POST', '{"Content-type":"application/json"}', '{}', '5000');



-- CREATE OR REPLACE TRIGGER "request_is_finalized" AFTER UPDATE ON "public"."requests" FOR EACH ROW EXECUTE FUNCTION "supabase_functions"."http_request"('https://n8n.paraito.ca/webhook/final-doc-creator', 'POST', '{"Content-type":"application/json"}', '{}', '5000');



CREATE OR REPLACE TRIGGER "requests_summary_trigger" AFTER UPDATE OF "request_summary" ON "public"."requests" FOR EACH ROW EXECUTE FUNCTION "public"."update_complete_summary"();



CREATE OR REPLACE TRIGGER "trg_actes_after_delete" AFTER DELETE ON "public"."actes" FOR EACH ROW EXECUTE FUNCTION "public"."update_index_acte_counts"();



CREATE OR REPLACE TRIGGER "trg_actes_after_insert" AFTER INSERT ON "public"."actes" FOR EACH ROW EXECUTE FUNCTION "public"."update_index_acte_counts"();



CREATE OR REPLACE TRIGGER "trg_actes_after_update" AFTER UPDATE ON "public"."actes" FOR EACH ROW WHEN ((("old"."document_completed" IS DISTINCT FROM "new"."document_completed") OR ("old"."index_id" IS DISTINCT FROM "new"."index_id"))) EXECUTE FUNCTION "public"."update_index_acte_counts"();



CREATE OR REPLACE TRIGGER "trigger_final_doc_creation_trigger" AFTER UPDATE ON "public"."requests" FOR EACH ROW EXECUTE FUNCTION "public"."trigger_final_doc_creation"();



CREATE OR REPLACE TRIGGER "trigger_final_doc_webhook_trigger" AFTER UPDATE ON "public"."requests" FOR EACH ROW EXECUTE FUNCTION "public"."trigger_final_doc_webhook"();



CREATE OR REPLACE TRIGGER "update_acte_document_completed_trigger" BEFORE INSERT OR UPDATE OF "status" ON "public"."actes" FOR EACH ROW EXECUTE FUNCTION "public"."update_acte_document_completed"();



CREATE OR REPLACE TRIGGER "update_acte_document_ready_trigger" BEFORE INSERT OR UPDATE OF "doc_id" ON "public"."actes" FOR EACH ROW EXECUTE FUNCTION "public"."update_acte_document_ready"();



CREATE OR REPLACE TRIGGER "update_actes_timestamp" BEFORE UPDATE ON "public"."actes" FOR EACH ROW EXECUTE FUNCTION "public"."update_timestamp"();



CREATE OR REPLACE TRIGGER "update_index_document_ready_trigger" BEFORE INSERT OR UPDATE OF "doc_id" ON "public"."index" FOR EACH ROW EXECUTE FUNCTION "public"."update_index_document_ready"();



CREATE OR REPLACE TRIGGER "update_index_is_completed_trigger" BEFORE UPDATE OF "status" ON "public"."index" FOR EACH ROW EXECUTE FUNCTION "public"."update_index_is_completed"();



CREATE OR REPLACE TRIGGER "update_index_phase1_completed_trigger" BEFORE INSERT OR UPDATE OF "phase_1_status" ON "public"."index" FOR EACH ROW EXECUTE FUNCTION "public"."update_index_phase1_completed"();



CREATE OR REPLACE TRIGGER "update_index_phase1_status_acquisition_round_trigger" BEFORE INSERT OR UPDATE OF "document_acquisition_round" ON "public"."index" FOR EACH ROW EXECUTE FUNCTION "public"."update_index_phase1_status_on_acquisition_round"();



CREATE OR REPLACE TRIGGER "update_index_phase2_completed_trigger" AFTER UPDATE OF "phase_2_status" ON "public"."index" FOR EACH ROW EXECUTE FUNCTION "public"."update_index_phase2_completed_after"();



CREATE OR REPLACE TRIGGER "update_index_phase2_status_trigger" AFTER UPDATE ON "public"."index" FOR EACH ROW WHEN ((("old"."related_actes" IS DISTINCT FROM "new"."related_actes") OR ("old"."actes_completed" IS DISTINCT FROM "new"."actes_completed"))) EXECUTE FUNCTION "public"."update_index_phase2_status_after"();



CREATE OR REPLACE TRIGGER "update_index_status_trigger" BEFORE UPDATE OF "phase_1_completed", "phase_2_completed", "phase_3_completed" ON "public"."index" FOR EACH ROW EXECUTE FUNCTION "public"."update_index_status"();



CREATE OR REPLACE TRIGGER "update_index_timestamp" BEFORE UPDATE ON "public"."index" FOR EACH ROW EXECUTE FUNCTION "public"."update_timestamp"();



CREATE OR REPLACE TRIGGER "update_notifications_timestamp" BEFORE UPDATE ON "public"."notifications" FOR EACH ROW EXECUTE FUNCTION "public"."update_timestamp"();



CREATE OR REPLACE TRIGGER "update_request_index_completed_count_trigger" AFTER INSERT OR DELETE OR UPDATE OF "request_id", "is_completed" ON "public"."index" FOR EACH ROW EXECUTE FUNCTION "public"."update_request_index_completed_count"();



CREATE OR REPLACE TRIGGER "update_requests_timestamp" BEFORE UPDATE ON "public"."requests" FOR EACH ROW EXECUTE FUNCTION "public"."update_timestamp"();



ALTER TABLE ONLY "public"."actes_documents"
    ADD CONSTRAINT "actes_documents_acte_type_fkey" FOREIGN KEY ("acte_type") REFERENCES "public"."acte_types"("id");



ALTER TABLE ONLY "public"."actes"
    ADD CONSTRAINT "actes_index_id_fkey" FOREIGN KEY ("index_id") REFERENCES "public"."index"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."actes"
    ADD CONSTRAINT "actes_request_id_fkey" FOREIGN KEY ("request_id") REFERENCES "public"."requests"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."chat_sessions"
    ADD CONSTRAINT "chat_sessions_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "auth"."users"("id") ON DELETE SET NULL;



ALTER TABLE ONLY "public"."dates"
    ADD CONSTRAINT "dates_legal_document_id_fkey" FOREIGN KEY ("legal_document_id") REFERENCES "public"."legal_documents"("id");



ALTER TABLE ONLY "public"."extraction_queue"
    ADD CONSTRAINT "extraction_queue_acte_id_fkey" FOREIGN KEY ("acte_id") REFERENCES "public"."actes"("id") ON DELETE SET NULL;



ALTER TABLE ONLY "public"."extraction_queue"
    ADD CONSTRAINT "extraction_queue_index_id_fkey" FOREIGN KEY ("index_id") REFERENCES "public"."index"("id") ON DELETE SET NULL;



ALTER TABLE ONLY "public"."extraction_queue"
    ADD CONSTRAINT "extraction_queue_request_id_fkey" FOREIGN KEY ("request_id") REFERENCES "public"."requests"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."index"
    ADD CONSTRAINT "index_request_id_fkey" FOREIGN KEY ("request_id") REFERENCES "public"."requests"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."key_clauses_provisions"
    ADD CONSTRAINT "key_clauses_provisions_legal_document_id_fkey" FOREIGN KEY ("legal_document_id") REFERENCES "public"."legal_documents"("id");



ALTER TABLE ONLY "public"."messages"
    ADD CONSTRAINT "messages_chat_session_id_fkey" FOREIGN KEY ("chat_session_id") REFERENCES "public"."chat_sessions"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."messages"
    ADD CONSTRAINT "messages_request_id_fkey" FOREIGN KEY ("request_id") REFERENCES "public"."requests"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."messages"
    ADD CONSTRAINT "messages_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "auth"."users"("id") ON DELETE SET NULL;



ALTER TABLE ONLY "public"."notary_and_references"
    ADD CONSTRAINT "notary_and_references_legal_document_id_fkey" FOREIGN KEY ("legal_document_id") REFERENCES "public"."legal_documents"("id");



ALTER TABLE ONLY "public"."notifications"
    ADD CONSTRAINT "notifications_request_id_fkey" FOREIGN KEY ("request_id") REFERENCES "public"."requests"("id");



ALTER TABLE ONLY "public"."notifications"
    ADD CONSTRAINT "notifications_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "auth"."users"("id");



ALTER TABLE ONLY "public"."parties"
    ADD CONSTRAINT "parties_legal_document_id_fkey" FOREIGN KEY ("legal_document_id") REFERENCES "public"."legal_documents"("id");



ALTER TABLE ONLY "public"."profiles"
    ADD CONSTRAINT "profiles_id_fkey" FOREIGN KEY ("id") REFERENCES "auth"."users"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."requests"
    ADD CONSTRAINT "requests_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "auth"."users"("id");



ALTER TABLE ONLY "public"."signatures_attestations"
    ADD CONSTRAINT "signatures_attestations_legal_document_id_fkey" FOREIGN KEY ("legal_document_id") REFERENCES "public"."legal_documents"("id");



CREATE POLICY "Allow authenticated users to insert chat sessions" ON "public"."chat_sessions" FOR INSERT TO "authenticated" WITH CHECK (true);



CREATE POLICY "Allow authenticated users to select chat sessions" ON "public"."chat_sessions" FOR SELECT TO "authenticated" USING (true);



CREATE POLICY "Allow authenticated users to select messages" ON "public"."messages" FOR SELECT TO "authenticated" USING (true);



CREATE POLICY "Allow users to insert their own messages" ON "public"."messages" FOR INSERT TO "authenticated" WITH CHECK (("auth"."uid"() = "user_id"));



CREATE POLICY "Users can delete actes for their requests" ON "public"."actes" FOR DELETE USING ((EXISTS ( SELECT 1
   FROM "public"."requests"
  WHERE (("requests"."id" = "actes"."request_id") AND ("requests"."user_id" = "auth"."uid"())))));



CREATE POLICY "Users can delete index entries for their requests" ON "public"."index" FOR DELETE USING ((EXISTS ( SELECT 1
   FROM "public"."requests"
  WHERE (("requests"."id" = "index"."request_id") AND ("requests"."user_id" = "auth"."uid"())))));



CREATE POLICY "Users can delete their own notifications" ON "public"."notifications" FOR DELETE USING (("auth"."uid"() = "user_id"));



CREATE POLICY "Users can delete their own requests" ON "public"."requests" FOR DELETE USING (("auth"."uid"() = "user_id"));



CREATE POLICY "Users can insert actes for their requests" ON "public"."actes" FOR INSERT WITH CHECK ((EXISTS ( SELECT 1
   FROM "public"."requests"
  WHERE (("requests"."id" = "actes"."request_id") AND ("requests"."user_id" = "auth"."uid"())))));



CREATE POLICY "Users can insert index entries for their requests" ON "public"."index" FOR INSERT WITH CHECK ((EXISTS ( SELECT 1
   FROM "public"."requests"
  WHERE (("requests"."id" = "index"."request_id") AND ("requests"."user_id" = "auth"."uid"())))));



CREATE POLICY "Users can insert their own notifications" ON "public"."notifications" FOR INSERT WITH CHECK (("auth"."uid"() = "user_id"));



CREATE POLICY "Users can insert their own requests" ON "public"."requests" FOR INSERT WITH CHECK (("auth"."uid"() = "user_id"));



CREATE POLICY "Users can update actes for their requests" ON "public"."actes" FOR UPDATE USING ((EXISTS ( SELECT 1
   FROM "public"."requests"
  WHERE (("requests"."id" = "actes"."request_id") AND ("requests"."user_id" = "auth"."uid"())))));



CREATE POLICY "Users can update index entries for their requests" ON "public"."index" FOR UPDATE USING ((EXISTS ( SELECT 1
   FROM "public"."requests"
  WHERE (("requests"."id" = "index"."request_id") AND ("requests"."user_id" = "auth"."uid"())))));



CREATE POLICY "Users can update their own notifications" ON "public"."notifications" FOR UPDATE USING (("auth"."uid"() = "user_id"));



CREATE POLICY "Users can update their own profile" ON "public"."profiles" FOR UPDATE USING (("auth"."uid"() = "id"));



CREATE POLICY "Users can update their own requests" ON "public"."requests" FOR UPDATE USING (("auth"."uid"() = "user_id"));



CREATE POLICY "Users can view actes for their requests" ON "public"."actes" FOR SELECT USING ((EXISTS ( SELECT 1
   FROM "public"."requests"
  WHERE (("requests"."id" = "actes"."request_id") AND ("requests"."user_id" = "auth"."uid"())))));



CREATE POLICY "Users can view index entries for their requests" ON "public"."index" FOR SELECT USING ((EXISTS ( SELECT 1
   FROM "public"."requests"
  WHERE (("requests"."id" = "index"."request_id") AND ("requests"."user_id" = "auth"."uid"())))));



CREATE POLICY "Users can view their own notifications" ON "public"."notifications" FOR SELECT USING (("auth"."uid"() = "user_id"));



CREATE POLICY "Users can view their own profile" ON "public"."profiles" FOR SELECT USING (("auth"."uid"() = "id"));



CREATE POLICY "Users can view their own requests" ON "public"."requests" FOR SELECT USING (("auth"."uid"() = "user_id"));



ALTER TABLE "public"."acte_types" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."actes" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."actes_documents" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."chat_sessions" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."dates" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."error_log" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."extraction_queue" ENABLE ROW LEVEL SECURITY;


CREATE POLICY "extraction_queue_all_operations_policy" ON "public"."extraction_queue" USING (true);



ALTER TABLE "public"."index" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."key_clauses_provisions" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."legal_documents" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."messages" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."notary_and_references" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."notifications" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."parties" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."profiles" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."requests" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."signatures_attestations" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."workflow_state" ENABLE ROW LEVEL SECURITY;


GRANT USAGE ON SCHEMA "public" TO "postgres";
GRANT USAGE ON SCHEMA "public" TO "anon";
GRANT USAGE ON SCHEMA "public" TO "authenticated";
GRANT USAGE ON SCHEMA "public" TO "service_role";



GRANT ALL ON TABLE "public"."actes" TO "anon";
GRANT ALL ON TABLE "public"."actes" TO "authenticated";
GRANT ALL ON TABLE "public"."actes" TO "service_role";



GRANT ALL ON FUNCTION "public"."check_additional_actes"("p_index_id" "uuid", "p_acte_level" integer) TO "anon";
GRANT ALL ON FUNCTION "public"."check_additional_actes"("p_index_id" "uuid", "p_acte_level" integer) TO "authenticated";
GRANT ALL ON FUNCTION "public"."check_additional_actes"("p_index_id" "uuid", "p_acte_level" integer) TO "service_role";



GRANT ALL ON FUNCTION "public"."get_final_doc_creation_instructions"() TO "anon";
GRANT ALL ON FUNCTION "public"."get_final_doc_creation_instructions"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."get_final_doc_creation_instructions"() TO "service_role";



GRANT ALL ON FUNCTION "public"."handle_extraction_queue_logic"() TO "anon";
GRANT ALL ON FUNCTION "public"."handle_extraction_queue_logic"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."handle_extraction_queue_logic"() TO "service_role";



GRANT ALL ON FUNCTION "public"."handle_new_user"() TO "anon";
GRANT ALL ON FUNCTION "public"."handle_new_user"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."handle_new_user"() TO "service_role";



GRANT ALL ON FUNCTION "public"."manually_trigger_final_doc_creation"("p_request_id" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."manually_trigger_final_doc_creation"("p_request_id" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."manually_trigger_final_doc_creation"("p_request_id" "uuid") TO "service_role";



GRANT ALL ON FUNCTION "public"."manually_trigger_final_doc_webhook"("p_request_id" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."manually_trigger_final_doc_webhook"("p_request_id" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."manually_trigger_final_doc_webhook"("p_request_id" "uuid") TO "service_role";



GRANT ALL ON FUNCTION "public"."mark_queue_item_completed"("p_queue_id" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."mark_queue_item_completed"("p_queue_id" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."mark_queue_item_completed"("p_queue_id" "uuid") TO "service_role";



GRANT ALL ON FUNCTION "public"."mark_queue_item_error"("p_queue_id" "uuid", "p_error_message" "text") TO "anon";
GRANT ALL ON FUNCTION "public"."mark_queue_item_error"("p_queue_id" "uuid", "p_error_message" "text") TO "authenticated";
GRANT ALL ON FUNCTION "public"."mark_queue_item_error"("p_queue_id" "uuid", "p_error_message" "text") TO "service_role";



GRANT ALL ON FUNCTION "public"."queue_additional_actes"("p_request_id" "uuid", "p_index_id" "uuid", "p_acte_ids" "uuid"[], "p_phase" integer) TO "anon";
GRANT ALL ON FUNCTION "public"."queue_additional_actes"("p_request_id" "uuid", "p_index_id" "uuid", "p_acte_ids" "uuid"[], "p_phase" integer) TO "authenticated";
GRANT ALL ON FUNCTION "public"."queue_additional_actes"("p_request_id" "uuid", "p_index_id" "uuid", "p_acte_ids" "uuid"[], "p_phase" integer) TO "service_role";



GRANT ALL ON FUNCTION "public"."queue_eligible_documents_for_phase"("p_request_id" "uuid", "p_phase" integer) TO "anon";
GRANT ALL ON FUNCTION "public"."queue_eligible_documents_for_phase"("p_request_id" "uuid", "p_phase" integer) TO "authenticated";
GRANT ALL ON FUNCTION "public"."queue_eligible_documents_for_phase"("p_request_id" "uuid", "p_phase" integer) TO "service_role";



GRANT ALL ON FUNCTION "public"."queue_index_actes"("p_request_id" "uuid", "p_index_id" "uuid", "p_phase" integer, "p_index_level" integer) TO "anon";
GRANT ALL ON FUNCTION "public"."queue_index_actes"("p_request_id" "uuid", "p_index_id" "uuid", "p_phase" integer, "p_index_level" integer) TO "authenticated";
GRANT ALL ON FUNCTION "public"."queue_index_actes"("p_request_id" "uuid", "p_index_id" "uuid", "p_phase" integer, "p_index_level" integer) TO "service_role";



GRANT ALL ON FUNCTION "public"."queue_index_concordances"("p_request_id" "uuid", "p_source_index_id" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."queue_index_concordances"("p_request_id" "uuid", "p_source_index_id" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."queue_index_concordances"("p_request_id" "uuid", "p_source_index_id" "uuid") TO "service_role";



GRANT ALL ON FUNCTION "public"."queue_index_reanalysis"("p_request_id" "uuid", "p_index_id" "uuid", "p_index_level" integer, "p_phase" integer) TO "anon";
GRANT ALL ON FUNCTION "public"."queue_index_reanalysis"("p_request_id" "uuid", "p_index_id" "uuid", "p_index_level" integer, "p_phase" integer) TO "authenticated";
GRANT ALL ON FUNCTION "public"."queue_index_reanalysis"("p_request_id" "uuid", "p_index_id" "uuid", "p_index_level" integer, "p_phase" integer) TO "service_role";



GRANT ALL ON FUNCTION "public"."ready_for_analysis"("doc_type" "text", "doc_id" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."ready_for_analysis"("doc_type" "text", "doc_id" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."ready_for_analysis"("doc_type" "text", "doc_id" "uuid") TO "service_role";



GRANT ALL ON FUNCTION "public"."trigger_final_doc_creation"() TO "anon";
GRANT ALL ON FUNCTION "public"."trigger_final_doc_creation"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."trigger_final_doc_creation"() TO "service_role";



GRANT ALL ON FUNCTION "public"."trigger_final_doc_webhook"() TO "anon";
GRANT ALL ON FUNCTION "public"."trigger_final_doc_webhook"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."trigger_final_doc_webhook"() TO "service_role";



GRANT ALL ON FUNCTION "public"."update_acte_document_completed"() TO "anon";
GRANT ALL ON FUNCTION "public"."update_acte_document_completed"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."update_acte_document_completed"() TO "service_role";



GRANT ALL ON FUNCTION "public"."update_acte_document_ready"() TO "anon";
GRANT ALL ON FUNCTION "public"."update_acte_document_ready"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."update_acte_document_ready"() TO "service_role";



GRANT ALL ON FUNCTION "public"."update_acte_status_on_acquisition_round"() TO "anon";
GRANT ALL ON FUNCTION "public"."update_acte_status_on_acquisition_round"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."update_acte_status_on_acquisition_round"() TO "service_role";



GRANT ALL ON FUNCTION "public"."update_complete_summary"() TO "anon";
GRANT ALL ON FUNCTION "public"."update_complete_summary"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."update_complete_summary"() TO "service_role";



GRANT ALL ON FUNCTION "public"."update_index_acte_counts"() TO "anon";
GRANT ALL ON FUNCTION "public"."update_index_acte_counts"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."update_index_acte_counts"() TO "service_role";



GRANT ALL ON FUNCTION "public"."update_index_actes_completed_count"() TO "anon";
GRANT ALL ON FUNCTION "public"."update_index_actes_completed_count"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."update_index_actes_completed_count"() TO "service_role";



GRANT ALL ON FUNCTION "public"."update_index_document_ready"() TO "anon";
GRANT ALL ON FUNCTION "public"."update_index_document_ready"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."update_index_document_ready"() TO "service_role";



GRANT ALL ON FUNCTION "public"."update_index_is_completed"() TO "anon";
GRANT ALL ON FUNCTION "public"."update_index_is_completed"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."update_index_is_completed"() TO "service_role";



GRANT ALL ON FUNCTION "public"."update_index_phase1_completed"() TO "anon";
GRANT ALL ON FUNCTION "public"."update_index_phase1_completed"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."update_index_phase1_completed"() TO "service_role";



GRANT ALL ON FUNCTION "public"."update_index_phase1_status_on_acquisition_round"() TO "anon";
GRANT ALL ON FUNCTION "public"."update_index_phase1_status_on_acquisition_round"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."update_index_phase1_status_on_acquisition_round"() TO "service_role";



GRANT ALL ON FUNCTION "public"."update_index_phase2_completed"() TO "anon";
GRANT ALL ON FUNCTION "public"."update_index_phase2_completed"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."update_index_phase2_completed"() TO "service_role";



GRANT ALL ON FUNCTION "public"."update_index_phase2_completed_after"() TO "anon";
GRANT ALL ON FUNCTION "public"."update_index_phase2_completed_after"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."update_index_phase2_completed_after"() TO "service_role";



GRANT ALL ON FUNCTION "public"."update_index_phase2_status"() TO "anon";
GRANT ALL ON FUNCTION "public"."update_index_phase2_status"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."update_index_phase2_status"() TO "service_role";



GRANT ALL ON FUNCTION "public"."update_index_phase2_status_after"() TO "anon";
GRANT ALL ON FUNCTION "public"."update_index_phase2_status_after"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."update_index_phase2_status_after"() TO "service_role";



GRANT ALL ON FUNCTION "public"."update_index_related_actes_count"() TO "anon";
GRANT ALL ON FUNCTION "public"."update_index_related_actes_count"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."update_index_related_actes_count"() TO "service_role";



GRANT ALL ON FUNCTION "public"."update_index_status"() TO "anon";
GRANT ALL ON FUNCTION "public"."update_index_status"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."update_index_status"() TO "service_role";



GRANT ALL ON FUNCTION "public"."update_modified_column"() TO "anon";
GRANT ALL ON FUNCTION "public"."update_modified_column"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."update_modified_column"() TO "service_role";



GRANT ALL ON FUNCTION "public"."update_request_counters"() TO "anon";
GRANT ALL ON FUNCTION "public"."update_request_counters"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."update_request_counters"() TO "service_role";



GRANT ALL ON FUNCTION "public"."update_request_index_completed_count"() TO "anon";
GRANT ALL ON FUNCTION "public"."update_request_index_completed_count"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."update_request_index_completed_count"() TO "service_role";



GRANT ALL ON FUNCTION "public"."update_timestamp"() TO "anon";
GRANT ALL ON FUNCTION "public"."update_timestamp"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."update_timestamp"() TO "service_role";



GRANT ALL ON FUNCTION "public"."update_updated_at_column"() TO "anon";
GRANT ALL ON FUNCTION "public"."update_updated_at_column"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."update_updated_at_column"() TO "service_role";



GRANT ALL ON TABLE "public"."acte_types" TO "anon";
GRANT ALL ON TABLE "public"."acte_types" TO "authenticated";
GRANT ALL ON TABLE "public"."acte_types" TO "service_role";



GRANT ALL ON SEQUENCE "public"."acte_types_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."acte_types_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."acte_types_id_seq" TO "service_role";



GRANT ALL ON TABLE "public"."actes_documents" TO "anon";
GRANT ALL ON TABLE "public"."actes_documents" TO "authenticated";
GRANT ALL ON TABLE "public"."actes_documents" TO "service_role";



GRANT ALL ON TABLE "public"."chat_sessions" TO "anon";
GRANT ALL ON TABLE "public"."chat_sessions" TO "authenticated";
GRANT ALL ON TABLE "public"."chat_sessions" TO "service_role";



GRANT ALL ON TABLE "public"."dates" TO "anon";
GRANT ALL ON TABLE "public"."dates" TO "authenticated";
GRANT ALL ON TABLE "public"."dates" TO "service_role";



GRANT ALL ON SEQUENCE "public"."dates_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."dates_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."dates_id_seq" TO "service_role";



GRANT ALL ON TABLE "public"."error_log" TO "anon";
GRANT ALL ON TABLE "public"."error_log" TO "authenticated";
GRANT ALL ON TABLE "public"."error_log" TO "service_role";



GRANT ALL ON SEQUENCE "public"."error_log_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."error_log_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."error_log_id_seq" TO "service_role";



GRANT ALL ON TABLE "public"."extraction_queue" TO "anon";
GRANT ALL ON TABLE "public"."extraction_queue" TO "authenticated";
GRANT ALL ON TABLE "public"."extraction_queue" TO "service_role";



GRANT ALL ON TABLE "public"."index" TO "anon";
GRANT ALL ON TABLE "public"."index" TO "authenticated";
GRANT ALL ON TABLE "public"."index" TO "service_role";



GRANT ALL ON TABLE "public"."key_clauses_provisions" TO "anon";
GRANT ALL ON TABLE "public"."key_clauses_provisions" TO "authenticated";
GRANT ALL ON TABLE "public"."key_clauses_provisions" TO "service_role";



GRANT ALL ON SEQUENCE "public"."key_clauses_provisions_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."key_clauses_provisions_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."key_clauses_provisions_id_seq" TO "service_role";



GRANT ALL ON TABLE "public"."legal_documents" TO "anon";
GRANT ALL ON TABLE "public"."legal_documents" TO "authenticated";
GRANT ALL ON TABLE "public"."legal_documents" TO "service_role";



GRANT ALL ON SEQUENCE "public"."legal_documents_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."legal_documents_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."legal_documents_id_seq" TO "service_role";



GRANT ALL ON TABLE "public"."messages" TO "anon";
GRANT ALL ON TABLE "public"."messages" TO "authenticated";
GRANT ALL ON TABLE "public"."messages" TO "service_role";



GRANT ALL ON TABLE "public"."notary_and_references" TO "anon";
GRANT ALL ON TABLE "public"."notary_and_references" TO "authenticated";
GRANT ALL ON TABLE "public"."notary_and_references" TO "service_role";



GRANT ALL ON SEQUENCE "public"."notary_and_references_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."notary_and_references_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."notary_and_references_id_seq" TO "service_role";



GRANT ALL ON TABLE "public"."notifications" TO "anon";
GRANT ALL ON TABLE "public"."notifications" TO "authenticated";
GRANT ALL ON TABLE "public"."notifications" TO "service_role";



GRANT ALL ON TABLE "public"."parties" TO "anon";
GRANT ALL ON TABLE "public"."parties" TO "authenticated";
GRANT ALL ON TABLE "public"."parties" TO "service_role";



GRANT ALL ON SEQUENCE "public"."parties_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."parties_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."parties_id_seq" TO "service_role";



GRANT ALL ON TABLE "public"."profiles" TO "anon";
GRANT ALL ON TABLE "public"."profiles" TO "authenticated";
GRANT ALL ON TABLE "public"."profiles" TO "service_role";



GRANT ALL ON TABLE "public"."requests" TO "anon";
GRANT ALL ON TABLE "public"."requests" TO "authenticated";
GRANT ALL ON TABLE "public"."requests" TO "service_role";



GRANT ALL ON TABLE "public"."signatures_attestations" TO "anon";
GRANT ALL ON TABLE "public"."signatures_attestations" TO "authenticated";
GRANT ALL ON TABLE "public"."signatures_attestations" TO "service_role";



GRANT ALL ON SEQUENCE "public"."signatures_attestations_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."signatures_attestations_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."signatures_attestations_id_seq" TO "service_role";



GRANT ALL ON TABLE "public"."workflow_state" TO "anon";
GRANT ALL ON TABLE "public"."workflow_state" TO "authenticated";
GRANT ALL ON TABLE "public"."workflow_state" TO "service_role";



ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON SEQUENCES  TO "postgres";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON SEQUENCES  TO "anon";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON SEQUENCES  TO "authenticated";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON SEQUENCES  TO "service_role";






ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON FUNCTIONS  TO "postgres";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON FUNCTIONS  TO "anon";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON FUNCTIONS  TO "authenticated";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON FUNCTIONS  TO "service_role";






ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON TABLES  TO "postgres";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON TABLES  TO "anon";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON TABLES  TO "authenticated";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON TABLES  TO "service_role";






RESET ALL;
