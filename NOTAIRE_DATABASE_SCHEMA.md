# Notaire Database Schema Documentation

## Overview
This document outlines the complete database schema for the Notaire application, a Quebec property title research system. The database is designed to handle property title searches, legal document analysis, and research workflow management.

---

## Core Tables

### __1. requests__
Main entity representing a property title research request.

- id (uuid, PRIMARY KEY, default: uuid_generate_v4())
- user_id (uuid, FOREIGN KEY → auth.users.id)
- request_summary (text, NOT NULL)
- seller_name (text, NOT NULL)
- seller_address (text, NOT NULL)
- complete_summary (text[], default: '{}')
- number_of_index (integer, default: 0)
- number_of_actes (integer, default: 0)
- created_at (timestamptz, default: now())
- updated_at (timestamptz, default: now())
- folder_id (text)
- final_doc_id (text)
- folder_link (text)
- final_doc_link (text)
- long_term_memory_doc_id (text)
- sales_years (numeric, default: 10)
- hypotheques_years (numeric, default: 30)
- inclure_actes_radies (boolean, default: true)
- status (text, default: 'Phase 1')
- completed_at (timestamptz)
- index_completed (integer, default: 0)
- resume_etapes_recherche (text)
- servitudes (text) *[Legacy markdown field]*
- regimes_matrimoniaux (text) *[Legacy markdown field]*
- erreurs (text) *[Legacy markdown field]*
- autres_considerations (text) *[Legacy markdown field]*
- circonscription (text)

### __2. profiles__
User profile information extending Supabase auth.

- id (uuid, PRIMARY KEY, FOREIGN KEY → auth.users.id)
- full_name (text)
- company (text)
- avatar_url (text)
- created_at (timestamptz, default: now())
- updated_at (timestamptz, default: now())
- template_id (text)
- is_approved (boolean, default: false)
- Folder_url (text)
- File_instructions (text)

---

## Document & Analysis Tables

### __3. index__
Property index documents from the land registry.

- id (uuid, PRIMARY KEY, default: uuid_generate_v4())
- request_id (uuid, FOREIGN KEY → requests.id)
- lot_number (text)
- circonscription (text)
- cadastre (text)
- doc_id (text)
- doc_url (text)
- index_summary (text)
- relevance_explanation (text)
- relevance_rating (integer)
- created_at (timestamptz, default: now())
- updated_at (timestamptz, default: now())
- designation_secondaire (text)
- index_initial (boolean, default: false)
- document_ready (boolean, default: false)
- phase_1_status (index_phase_status_enum, default: 'En_attente')
- document_acquisition_round (integer, default: 0)
- phase_1_completed (boolean, default: false)
- related_actes (integer, default: 0)
- actes_completed (integer, default: 0)
- phase_2_completed (boolean, default: false)
- phase_3_completed (boolean, default: false)
- status (index_status_enum, default: 'Phase_1')
- is_completed (boolean, default: false)
- doc_number (numeric)
- phase_2_status (phase_2_status_enum, default: 'En_attente')
- file_content (text)
- source_id (uuid)

### __4. actes__
Legal acts and documents discovered during research.

- id (uuid, PRIMARY KEY, default: uuid_generate_v4())
- request_id (uuid, FOREIGN KEY → requests.id)
- index_id (uuid, FOREIGN KEY → index.id)
- doc_id (text)
- doc_url (text)
- acte_publication_number (text)
- acte_nature (text)
- acte_parties (text)
- acte_details (text)
- acte_summary (text)
- acte_notary_minute (text)
- writting (text)
- relevance_explanation (text)
- relevance_rating (integer)
- created_at (timestamptz, default: now())
- updated_at (timestamptz, default: now())
- other_details (jsonb)
- acte_publication_date (text)
- circonscription_fonciere (text)
- matrimonie (text)
- is_radiated (boolean, default: false)
- radiation_number (text)
- source_type (text, default: 'index')
- source_id (uuid)
- status (acte_status_enum, default: 'En_attente')
- document_ready (boolean, default: false)
- document_completed (boolean, default: false)
- doc_number (integer)
- file_content (text)

### __5. acte_types__
Categorization system for legal acts.

- id (integer, PRIMARY KEY, auto-increment)
- category (text, NOT NULL)
- acte_type (text, NOT NULL)
- extraction_analysis (text)
- edge_cases (text)

### __6. actes_documents__
Document storage for acts with full content.

- acte_publication_number (text, PRIMARY KEY)
- created_at (timestamptz, default: now())
- acte_type (integer, FOREIGN KEY → acte_types.id)
- file_content (text)
- publication_date (date)
- alternative_names (jsonb)
- document_id (text)
- document_url (text)
- doc_summary (text)

### __7. extraction_queue__
Document extraction and processing queue.

- id (uuid, PRIMARY KEY, default: uuid_generate_v4())
- document_source (document_source_enum: 'acte' | 'index')
- document_number (text, NOT NULL)
- circonscription_fonciere (text)
- acte_type (acte_type_enum: 'Acte' | 'Avis d\'adresse' | 'Radiation' | 'Acte divers')
- cadastre (text)
- designation_secondaire (text)
- status (extraction_status_enum, default: 'En_attente')
- doc_id (text)
- doc_url (text)
- local_file_path (text)
- created_at (timestamptz, default: now())
- updated_at (timestamptz, default: now())
- retry_count (integer, default: 0)
- error_message (text)
- processing_started_at (timestamptz)
- acte_id (uuid, FOREIGN KEY → actes.id)
- index_id (uuid, FOREIGN KEY → index.id)
- logged (boolean, default: false)
- file_content (text)
- request_id (uuid, FOREIGN KEY → requests.id)

---

## Structured Analysis Tables (NEW)

### __8. request_errors__ (MULTIPLE)
Structured error tracking for research requests.

- id (uuid, PRIMARY KEY, default: uuid_generate_v4())
- request_id (uuid, FOREIGN KEY → requests.id)
- error_type (error_type_enum: 'temporelle' | 'documentaire' | 'chaine_de_titres' | 'conformite' | 'evaluation' | 'geographique' | 'limitation' | 'autre')
- title (text, NOT NULL)
- description (text, NOT NULL)
- severity (severity_enum: 'critique' | 'majeure' | 'mineure' | 'informationnelle')
- resolution_status (resolution_status_enum, default: 'en_attente': 'en_attente' | 'resolu' | 'reconnu')
- created_at (timestamptz, default: now())
- updated_at (timestamptz, default: now())
- sort_order (integer)

### __9. matrimonial_regimes__ (MULTIPLE)
Matrimonial regime information for individuals.

- id (uuid, PRIMARY KEY, default: uuid_generate_v4())
- request_id (uuid, FOREIGN KEY → requests.id)
- person_name (text, NOT NULL)
- spouse_name (text)
- regime_type (regime_type_enum: 'societe_acquets' | 'communaute_biens' | 'separation_biens' | 'celibataire' | 'divorce' | 'veuf' | 'autre')
- marriage_date (date)
- divorce_date (date)
- contract_details (text)
- impact_on_title (text, NOT NULL)
- created_at (timestamptz, default: now())
- updated_at (timestamptz, default: now())
- sort_order (integer)

### __10. title_chain__ (MULTIPLE)
Chronological property title chain tracking.

- id (uuid, PRIMARY KEY, default: uuid_generate_v4())
- request_id (uuid, FOREIGN KEY → requests.id)
- transaction_date (date, NOT NULL)
- transaction_type (transaction_type_enum: 'vente' | 'donation' | 'succession' | 'saisie' | 'cession' | 'transmission' | 'autre')
- from_party (text, NOT NULL)
- to_party (text, NOT NULL)
- price (numeric)
- registration_number (text, NOT NULL)
- notary (text)
- notes (text)
- created_at (timestamptz, default: now())
- updated_at (timestamptz, default: now())
- sort_order (integer)

### __11. servitudes__ (MULTIPLE)
Property servitudes and easements management.

- id (uuid, PRIMARY KEY, default: uuid_generate_v4())
- request_id (uuid, FOREIGN KEY → requests.id)
- servitude_type (servitude_type_enum: 'passage' | 'vue' | 'utilite_publique' | 'aqueduc_egout' | 'nuisance' | 'hydro_quebec' | 'bell' | 'autre')
- beneficiary (text, NOT NULL)
- burden_party (text)
- registration_number (text, NOT NULL)
- registration_date (date, NOT NULL)
- duration (duration_enum: 'perpetuelle' | 'temporaire' | 'conditionnelle')
- expiry_date (date)
- description (text, NOT NULL)
- status (servitude_status_enum, default: 'actif': 'actif' | 'expire' | 'radie')
- created_at (timestamptz, default: now())
- updated_at (timestamptz, default: now())
- sort_order (integer)

### __12. charges_mortgages__ (MULTIPLE)
Property charges and mortgages tracking.

- id (uuid, PRIMARY KEY, default: uuid_generate_v4())
- request_id (uuid, FOREIGN KEY → requests.id)
- charge_type (charge_type_enum: 'hypotheque' | 'privilege' | 'clause_resolutoire' | 'droit_preemption' | 'autre')
- creditor (text, NOT NULL)
- debtor (text, NOT NULL)
- amount (numeric, NOT NULL)
- interest_rate (numeric)
- registration_number (text, NOT NULL)
- registration_date (date, NOT NULL)
- status (charge_status_enum, default: 'actif': 'actif' | 'a_radier' | 'radie')
- radiation_number (text)
- radiation_date (date)
- notes (text)
- created_at (timestamptz, default: now())
- updated_at (timestamptz, default: now())
- sort_order (integer)

### __13. other_considerations__ (MULTIPLE)
Miscellaneous considerations and notes.

- id (uuid, PRIMARY KEY, default: uuid_generate_v4())
- request_id (uuid, FOREIGN KEY → requests.id)
- category (consideration_category_enum: 'cadastral' | 'financier' | 'physique' | 'juridique' | 'municipal' | 'environnemental' | 'autre')
- title (text, NOT NULL)
- description (text, NOT NULL)
- importance (importance_enum: 'haute' | 'moyenne' | 'basse')
- requires_action (boolean, default: false)
- action_taken (text)
- created_at (timestamptz, default: now())
- updated_at (timestamptz, default: now())
- sort_order (integer)

---

## Legal Document Analysis Tables

### __14. legal_documents__
Master table for legal document analysis.

- id (bigint, PRIMARY KEY, auto-increment)
- acte_publication_number (text)
- summary (text)
- file_content (text)
- publication_date (timestamptz)

### __15. parties__ (MULTIPLE)
Legal document parties analysis.

- id (bigint, PRIMARY KEY, auto-increment)
- legal_document_id (bigint, FOREIGN KEY → legal_documents.id)
- name (text, NOT NULL)
- type (text, NOT NULL)
- legal_form (text)
- address (text)
- role (text)
- authorized_representative (text)
- legal_identifiers (text)

### __16. dates__ (MULTIPLE)
Date analysis for legal documents.

- id (bigint, PRIMARY KEY, auto-increment)
- legal_document_id (bigint, FOREIGN KEY → legal_documents.id)
- date (timestamptz, NOT NULL)
- date_type (text)
- range_start (timestamptz)
- range_end (timestamptz)

### __17. notary_and_references__ (MULTIPLE)
Notary and legal reference information.

- id (bigint, PRIMARY KEY, auto-increment)
- legal_document_id (bigint, FOREIGN KEY → legal_documents.id)
- notary_name (text)
- jurisdiction (text)
- location (text)
- legal_references (text[])

### __18. key_clauses_provisions__ (MULTIPLE)
Important clauses and provisions extraction.

- id (bigint, PRIMARY KEY, auto-increment)
- legal_document_id (bigint, FOREIGN KEY → legal_documents.id)
- clause_title (text, NOT NULL)
- clause_summary (text, NOT NULL)

### __19. signatures_attestations__ (MULTIPLE)
Signature and attestation information.

- id (bigint, PRIMARY KEY, auto-increment)
- legal_document_id (bigint, FOREIGN KEY → legal_documents.id)
- signature_text (text)

---

## Communication & Workflow Tables

### __20. chat_sessions__
Chat conversation sessions.

- id (uuid, PRIMARY KEY, default: gen_random_uuid())
- user_id (uuid, FOREIGN KEY → auth.users.id)
- created_at (timestamptz, default: now())
- updated_at (timestamptz, default: now())
- metadata (jsonb)

### __21. messages__ (MULTIPLE)
Individual chat messages.

- id (uuid, PRIMARY KEY, default: gen_random_uuid())
- chat_session_id (uuid, FOREIGN KEY → chat_sessions.id)
- user_id (uuid, FOREIGN KEY → auth.users.id)
- sender (text, CHECK: 'user' | 'bot' | 'assistant')
- content (text, NOT NULL)
- created_at (timestamptz, default: now())
- metadata (jsonb)
- request_id (uuid, FOREIGN KEY → requests.id)

### __22. notifications__ (MULTIPLE)
User notification system.

- id (uuid, PRIMARY KEY, default: gen_random_uuid())
- user_id (uuid, FOREIGN KEY → auth.users.id)
- title (text, NOT NULL)
- content (text, NOT NULL)
- request_id (uuid, FOREIGN KEY → requests.id)
- is_read (boolean, default: false)
- created_at (timestamptz, default: now())
- updated_at (timestamptz, default: now())

---

## System Tables

### __23. error_log__
System error logging.

- id (bigint, PRIMARY KEY, auto-increment)
- created_at (timestamptz, default: now())
- flow_name (text)
- error (text)

### __24. workflow_state__
Workflow state management.

- workflow (text, PRIMARY KEY, default: 'workflow')
- in_progress (boolean, default: false)

---

## Database Features

### Row Level Security (RLS)
All user-facing tables implement RLS policies to ensure data isolation:
- **Enabled on**: requests, index, actes, request_errors, matrimonial_regimes, title_chain, servitudes, charges_mortgages, other_considerations, profiles, notifications, chat_sessions, messages, extraction_queue, acte_types, actes_documents, parties, legal_documents, dates, notary_and_references, key_clauses_provisions, signatures_attestations, error_log, workflow_state

### Enums
- **acte_status_enum**: 'En_attente' | 'Pret_pour_analyse' | 'Analyse_en_cours' | 'Analyse_terminee' | 'Document_indisponible'
- **acte_type_enum**: 'Acte' | 'Avis d\'adresse' | 'Radiation' | 'Acte divers'
- **charge_status_enum**: 'actif' | 'a_radier' | 'radie'
- **charge_type_enum**: 'hypotheque' | 'privilege' | 'clause_resolutoire' | 'droit_preemption' | 'autre'
- **consideration_category_enum**: 'cadastral' | 'financier' | 'physique' | 'juridique' | 'municipal' | 'environnemental' | 'autre'
- **document_source_enum**: 'acte' | 'index'
- **duration_enum**: 'perpetuelle' | 'temporaire' | 'conditionnelle'
- **error_type_enum**: 'temporelle' | 'documentaire' | 'chaine_de_titres' | 'conformite' | 'evaluation' | 'geographique' | 'limitation' | 'autre'
- **extraction_status_enum**: 'En_attente' | 'En_traitement' | 'Telecharge' | 'Disponible_sur_Drive' | 'Document_introuvable'
- **importance_enum**: 'haute' | 'moyenne' | 'basse'
- **index_phase_status_enum**: 'En_attente' | 'Pret_pour_analyse' | 'Analyse_en_cours' | 'Analyse_terminee' | 'Document_indisponible'
- **index_status_enum**: 'Phase_1' | 'En_attente_Phase_2' | 'Phase_2' | 'Phase_3' | 'Termine'
- **phase_2_status_enum**: 'En_attente' | 'Analyse_actes_en_cours' | 'Analyse_terminee'
- **regime_type_enum**: 'societe_acquets' | 'communaute_biens' | 'separation_biens' | 'celibataire' | 'divorce' | 'veuf' | 'autre'
- **resolution_status_enum**: 'en_attente' | 'resolu' | 'reconnu'
- **servitude_status_enum**: 'actif' | 'expire' | 'radie'
- **servitude_type_enum**: 'passage' | 'vue' | 'utilite_publique' | 'aqueduc_egout' | 'nuisance' | 'hydro_quebec' | 'bell' | 'autre'
- **severity_enum**: 'critique' | 'majeure' | 'mineure' | 'informationnelle'
- **transaction_type_enum**: 'vente' | 'donation' | 'succession' | 'saisie' | 'cession' | 'transmission' | 'autre'

### Audit Features
All tables include:
- **created_at**: Timestamp of record creation
- **updated_at**: Timestamp of last modification (auto-updated via triggers)
- **sort_order**: Manual ordering capability for user-defined sequences

### Key Relationships
- **requests** → Core entity with one-to-many relationships to all analysis tables
- **index** → **actes**: One index document can reference multiple legal acts
- **requests** → **index**: One request can have multiple index documents
- **requests** → **actes**: One request can have multiple legal acts
- **legal_documents** → **parties/dates/clauses**: One document can have multiple extracted elements
- **chat_sessions** → **messages**: One session can have multiple messages
- **users** → **requests/profiles/notifications**: User ownership and access control

### Migration Strategy
- **Legacy Fields**: The `requests` table contains legacy markdown fields (`servitudes`, `regimes_matrimoniaux`, `erreurs`, `autres_considerations`) that will be migrated to the new structured tables and eventually removed.
- **Data Migration**: Structured data tables are designed to replace free-form markdown with queryable, validated data structures.
