
import React from 'react';
import { <PERSON><PERSON>, TabsList, TabsTrigger, TabsContent } from '@/components/ui/tabs';
import { ErrorsList } from './sections/ErrorsList';
import { MatrimonialRegimesList } from './sections/MatrimonialRegimesList';
import { ServitudesList } from './sections/ServitudesList';
import { ChargesMortgagesList } from './sections/ChargesMortgagesList';
import { OtherConsiderationsList } from './sections/OtherConsiderationsList';

interface RequestTabbedSectionProps {
  requestId: string;
}

const RequestTabbedSection: React.FC<RequestTabbedSectionProps> = ({ requestId }) => {

  return (
    <div className="bg-card rounded-xl border border-border/40 shadow-sm overflow-hidden">
      <div className="border-b border-border/40 px-6 py-4">
        <h2 className="text-xl font-semibold">Faits saillants</h2>
      </div>
      <div className="p-6">
        <Tabs defaultValue="erreurs">
          <TabsList className="mb-4">
            <TabsTrigger value="erreurs">Erreurs</TabsTrigger>
            <TabsTrigger value="regimes">Régimes matrimoniaux</TabsTrigger>
            <TabsTrigger value="servitudes">Servitudes</TabsTrigger>
            <TabsTrigger value="charges">Charges et hypothèques</TabsTrigger>
            <TabsTrigger value="autres">Autres considérations</TabsTrigger>
          </TabsList>
          
          <TabsContent value="erreurs">
            <ErrorsList requestId={requestId} />
          </TabsContent>
          
          <TabsContent value="regimes">
            <MatrimonialRegimesList requestId={requestId} />
          </TabsContent>
          
          <TabsContent value="servitudes">
            <ServitudesList requestId={requestId} />
          </TabsContent>
          
          <TabsContent value="charges">
            <ChargesMortgagesList requestId={requestId} />
          </TabsContent>
          
          <TabsContent value="autres">
            <OtherConsiderationsList requestId={requestId} />
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default RequestTabbedSection;
