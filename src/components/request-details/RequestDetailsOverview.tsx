import React from 'react';
import { useQuery } from '@tanstack/react-query';
import { Calendar, MapPin, User, Building, Hash, AlertTriangle, CheckCircle2, Clock, TrendingUp, FileText, BarChart, Search, Network, Gavel, BookOpen } from 'lucide-react';
import { supabase } from '@/lib/supabase';
import MermaidGraph from '@/components/ui/mermaid-graph';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card';

interface RequestDetailsOverviewProps {
  request: any;
  indexEntries: any[];
  actesByIndex: { [key: string]: any[] };
  mermaidDefinition?: string;
  onNodeClick?: (nodeId: string, nodeType: 'request' | 'index' | 'acte' | 'radiated') => void;
}

const RequestDetailsOverview: React.FC<RequestDetailsOverviewProps> = ({
  request,
  indexEntries,
  actesByIndex,
  mermaidDefinition,
  onNodeClick
}) => {
  const requestId = request?.id;

  // Fetch counts for each analysis section
  const { data: servitudesCount = 0 } = useQuery({
    queryKey: ['servitudes-count', requestId],
    queryFn: async () => {
      if (!requestId) return 0;
      const { count, error } = await supabase
        .from('servitudes')
        .select('*', { count: 'exact', head: true })
        .eq('request_id', requestId);
      if (error) throw error;
      return count || 0;
    },
    enabled: !!requestId,
  });

  const { data: matrimonialRegimesCount = 0 } = useQuery({
    queryKey: ['matrimonial-regimes-count', requestId],
    queryFn: async () => {
      if (!requestId) return 0;
      const { count, error } = await supabase
        .from('matrimonial_regimes')
        .select('*', { count: 'exact', head: true })
        .eq('request_id', requestId);
      if (error) throw error;
      return count || 0;
    },
    enabled: !!requestId,
  });

  const { data: chargesMortgagesCount = 0 } = useQuery({
    queryKey: ['charges-mortgages-count', requestId],
    queryFn: async () => {
      if (!requestId) return 0;
      const { count, error } = await supabase
        .from('charges_mortgages')
        .select('*', { count: 'exact', head: true })
        .eq('request_id', requestId);
      if (error) throw error;
      return count || 0;
    },
    enabled: !!requestId,
  });

  const { data: otherConsiderationsCount = 0 } = useQuery({
    queryKey: ['other-considerations-count', requestId],
    queryFn: async () => {
      if (!requestId) return 0;
      const { count, error } = await supabase
        .from('other_considerations')
        .select('*', { count: 'exact', head: true })
        .eq('request_id', requestId);
      if (error) throw error;
      return count || 0;
    },
    enabled: !!requestId,
  });

  const { data: errorsStats = { resolved: 0, total: 0 } } = useQuery({
    queryKey: ['errors-stats', requestId],
    queryFn: async () => {
      if (!requestId) return { resolved: 0, total: 0 };
      const { data, error } = await supabase
        .from('request_errors')
        .select('resolution_status')
        .eq('request_id', requestId);
      if (error) throw error;
      
      const total = data.length;
      const resolved = data.filter(error => error.resolution_status === 'resolu').length;
      return { resolved, total };
    },
    enabled: !!requestId,
  });
  const formatDate = (dateString: string) => {
    try {
      return new Date(dateString).toLocaleDateString('fr-FR', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });
    } catch (error) {
      return 'Date invalide';
    }
  };

  const translateStatus = (status: string): string => {
    switch (status?.toLowerCase()) {
      case 'pending': return 'En attente';
      case 'inprogress':
      case 'in progress': return 'En cours';
      case 'completed': return 'Complété';
      case 'error': return 'Erreur';
      default: return status || 'Inconnu';
    }
  };

  return (
    <div className="space-y-6">
      {/* Enhanced Header with gradient background */}
      <div className="bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-blue-950 dark:to-indigo-900 rounded-xl p-6 border border-blue-200 dark:border-blue-800">
        <div className="flex items-start justify-between">
          <div className="flex items-center space-x-4">
            <div className="h-16 w-16 rounded-xl bg-primary/10 flex items-center justify-center">
              <Building className="h-8 w-8 text-primary" />
            </div>
            <div>
              <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
                {request?.seller_name || 'Chargement...'}
              </h1>
              <div className="flex items-center space-x-2 mt-2">
                <MapPin className="h-4 w-4 text-gray-500" />
                <p className="text-gray-600 dark:text-gray-300">
                  {request?.seller_address || ''}
                </p>
              </div>
              <div className="flex items-center space-x-4 mt-3">
                <div className="flex items-center space-x-1">
                  <Calendar className="h-4 w-4 text-gray-500" />
                  <span className="text-sm text-gray-600 dark:text-gray-400">
                    Créée le {formatDate(request?.created_at)}
                  </span>
                </div>
                <div className="flex items-center space-x-1">
                  <Hash className="h-4 w-4 text-gray-500" />
                  <span className="text-sm font-mono text-gray-600 dark:text-gray-400">
                    #{request?.id?.substring(0, 8)}
                  </span>
                </div>
              </div>
            </div>
          </div>
          <div className="text-right">
            <div className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${
              request?.status?.toLowerCase() === 'completed'
                ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                : request?.status?.toLowerCase() === 'error'
                ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
                : 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
            }`}>
              {request?.status?.toLowerCase() === 'completed' && <CheckCircle2 className="h-4 w-4 mr-1" />}
              {request?.status?.toLowerCase() === 'error' && <AlertTriangle className="h-4 w-4 mr-1" />}
              {!['completed', 'error'].includes(request?.status?.toLowerCase()) && <Clock className="h-4 w-4 mr-1" />}
              {translateStatus(request?.status)}
            </div>
            {request?.completed_at && (
              <p className="text-xs text-gray-500 mt-2">
                Complétée le {formatDate(request?.completed_at)}
              </p>
            )}
          </div>
        </div>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
        {/* Index Count Card */}
        <Card className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 hover:shadow-md transition-shadow duration-200">
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <CardTitle className="text-sm font-medium text-muted-foreground">
                Nombre d'index
              </CardTitle>
              <div className="p-2 rounded-lg bg-blue-50 dark:bg-blue-900/50">
                <BookOpen className="h-5 w-5 text-blue-600 dark:text-blue-400" />
              </div>
            </div>
          </CardHeader>
          <CardContent className="pt-0">
            <div className="flex items-end justify-between">
              <div>
                <div className="text-3xl font-bold text-foreground mb-1">
                  {indexEntries.length}
                </div>
                <p className="text-sm text-muted-foreground">
                  {indexEntries.length === 0 ? 'Aucun index' : indexEntries.length === 1 ? 'Index trouvé' : 'Index trouvés'}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Actes Count Card */}
        <Card className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 hover:shadow-md transition-shadow duration-200">
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <CardTitle className="text-sm font-medium text-muted-foreground">
                Nombre d'actes
              </CardTitle>
              <div className="p-2 rounded-lg bg-purple-50 dark:bg-purple-900/50">
                <Gavel className="h-5 w-5 text-purple-600 dark:text-purple-400" />
              </div>
            </div>
          </CardHeader>
          <CardContent className="pt-0">
            <div className="flex items-end justify-between">
              <div>
                <div className="text-3xl font-bold text-foreground mb-1">
                  {Object.values(actesByIndex).flat().length}
                </div>
                <p className="text-sm text-muted-foreground">
                  {Object.values(actesByIndex).flat().length === 0 ? 'Aucun acte' : Object.values(actesByIndex).flat().length === 1 ? 'Acte trouvé' : 'Actes trouvés'}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Current Step Card */}
        <Card className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 hover:shadow-md transition-shadow duration-200">
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <CardTitle className="text-sm font-medium text-muted-foreground">
                Étape actuelle
              </CardTitle>
              <div className="p-2 rounded-lg bg-green-50 dark:bg-green-900/50">
                <TrendingUp className="h-5 w-5 text-green-600 dark:text-green-400" />
              </div>
            </div>
          </CardHeader>
          <CardContent className="pt-0">
            <div className="space-y-3">
              <div className={`inline-flex items-center px-3 py-1.5 rounded-full text-sm font-medium ${
                request?.status?.toLowerCase() === 'completed'
                  ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                  : request?.status?.toLowerCase() === 'error'
                  ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
                  : 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
              }`}>
                {request?.status?.toLowerCase() === 'completed' && <CheckCircle2 className="h-4 w-4 mr-1.5" />}
                {request?.status?.toLowerCase() === 'error' && <AlertTriangle className="h-4 w-4 mr-1.5" />}
                {!['completed', 'error'].includes(request?.status?.toLowerCase()) && <Clock className="h-4 w-4 mr-1.5" />}
                {translateStatus(request?.status)}
              </div>
              
              <p className="text-sm text-muted-foreground">
                {request?.status?.toLowerCase() === 'completed' ? 'Analyse terminée avec succès' : 
                 request?.status?.toLowerCase() === 'error' ? 'Attention requise' :
                 request?.status?.toLowerCase().includes('phase') ? 'Analyse en cours...' :
                 'Traitement en cours...'}
              </p>
              
              {/* Progress indicator */}
              <div className="w-full bg-muted rounded-full h-2">
                <div className={`h-2 rounded-full transition-all duration-500 ${
                  request?.status?.toLowerCase() === 'completed' ? 'w-full bg-green-500' :
                  request?.status?.toLowerCase().includes('phase_4') ? 'w-4/5 bg-purple-500' :
                  request?.status?.toLowerCase().includes('phase_3') ? 'w-3/5 bg-blue-500' :
                  request?.status?.toLowerCase().includes('phase_2') ? 'w-2/5 bg-indigo-500' :
                  request?.status?.toLowerCase().includes('phase_1') ? 'w-1/5 bg-cyan-500' :
                  request?.status?.toLowerCase() === 'inprogress' || request?.status?.toLowerCase() === 'in progress' ? 'w-1/6 bg-blue-400' :
                  'w-1/12 bg-muted-foreground'
                }`}></div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Request Information Cards */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6">
        {/* Client Information */}
        <div className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-4 sm:p-6">
          <div className="flex items-center mb-4">
            <User className="h-5 w-5 text-primary mr-2" />
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Informations client</h3>
          </div>
          <div className="space-y-3">
            <div>
              <label className="text-sm font-medium text-gray-500 dark:text-gray-400">Nom du vendeur</label>
              <p className="text-gray-900 dark:text-white font-medium">{request?.seller_name || 'Non spécifié'}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500 dark:text-gray-400">Adresse de la propriété</label>
              <p className="text-gray-900 dark:text-white">{request?.seller_address || 'Non spécifiée'}</p>
            </div>
            {request?.property_details && (
              <div>
                <label className="text-sm font-medium text-gray-500 dark:text-gray-400">Détails de la propriété</label>
                <p className="text-gray-900 dark:text-white">{request.property_details}</p>
              </div>
            )}
          </div>
        </div>

        {/* Search Criteria */}
        <div className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-4 sm:p-6">
          <div className="flex items-center mb-4">
            <Search className="h-5 w-5 text-primary mr-2" />
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Critères de recherche</h3>
          </div>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Inclure les actes radiés</span>
              <span className={`inline-flex px-2 py-1 rounded-full text-xs font-medium ${
                request?.inclure_actes_radies
                  ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                  : 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'
              }`}>
                {request?.inclure_actes_radies ? 'Oui' : 'Non'}
              </span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Ventes</span>
              <span className="text-sm text-gray-600 dark:text-gray-400">
                {request?.sales_years ? `${request.sales_years} ${request.sales_years === 1 ? 'an' : 'ans'}` : 'Non spécifié'}
              </span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Hypothèques</span>
              <span className="text-sm text-gray-600 dark:text-gray-400">
                {request?.hypotheques_years ? `${request.hypotheques_years} ${request.hypotheques_years === 1 ? 'an' : 'ans'}` : 'Non spécifié'}
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Detailed Analysis Status */}
      <div className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-4 sm:p-6">
        <div className="flex items-center mb-6">
          <BarChart className="h-5 w-5 text-primary mr-2" />
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">État détaillé des analyses</h3>
        </div>
        <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-5 gap-3 sm:gap-4">
          <div className="border border-gray-200 dark:border-gray-600 rounded-lg p-4 text-center">
            <div className="text-2xl font-bold text-blue-600 dark:text-blue-400 mb-1">
              {servitudesCount}
            </div>
            <div className="text-sm font-medium text-gray-700 dark:text-gray-300">
              Servitudes
            </div>
          </div>

          <div className="border border-gray-200 dark:border-gray-600 rounded-lg p-4 text-center">
            <div className="text-2xl font-bold text-purple-600 dark:text-purple-400 mb-1">
              {matrimonialRegimesCount}
            </div>
            <div className="text-sm font-medium text-gray-700 dark:text-gray-300">
              Régimes matrimoniaux
            </div>
          </div>

          <div className="border border-gray-200 dark:border-gray-600 rounded-lg p-4 text-center">
            <div className="text-2xl font-bold text-orange-600 dark:text-orange-400 mb-1">
              {chargesMortgagesCount}
            </div>
            <div className="text-sm font-medium text-gray-700 dark:text-gray-300">
              Charges et hypothèques
            </div>
          </div>

          <div className="border border-gray-200 dark:border-gray-600 rounded-lg p-4 text-center">
            <div className="text-2xl font-bold text-green-600 dark:text-green-400 mb-1">
              {otherConsiderationsCount}
            </div>
            <div className="text-sm font-medium text-gray-700 dark:text-gray-300">
              Autres considérations
            </div>
          </div>

          <div className="border border-gray-200 dark:border-gray-600 rounded-lg p-4 text-center">
            <div className={`text-2xl font-bold mb-1 ${
              errorsStats.total === 0 
                ? 'text-green-600 dark:text-green-400'
                : errorsStats.resolved === errorsStats.total
                ? 'text-green-600 dark:text-green-400' 
                : 'text-red-600 dark:text-red-400'
            }`}>
              {errorsStats.resolved}/{errorsStats.total}
            </div>
            <div className="text-sm font-medium text-gray-700 dark:text-gray-300">
              Erreurs résolues
            </div>
          </div>
        </div>
      </div>

      {/* Relations & Graphique Section */}
      {mermaidDefinition && (
        <div className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-4 sm:p-6">
          <div className="flex items-center mb-6">
            <Network className="h-5 w-5 text-primary mr-2" />
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Relations & Graphique</h3>
          </div>
          
          <div className="bg-gray-50 dark:bg-gray-800 rounded-lg border border-dashed p-6">
            <MermaidGraph
              definition={mermaidDefinition}
              onNodeClick={onNodeClick}
            />
          </div>

          {/* Enhanced Legend Section */}
          <div className="mt-6">
            <h4 className="text-md font-semibold mb-4">
              Légende du graphique
            </h4>
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4">
              <div className="flex items-center space-x-3 p-2 rounded-lg bg-gray-50 dark:bg-gray-700">
                <span className="w-4 h-4 rounded-lg bg-[#F59E0B] border-2 border-[#D97706]"></span>
                <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Demande</span>
              </div>
              <div className="flex items-center space-x-3 p-2 rounded-lg bg-gray-50 dark:bg-gray-700">
                <span className="w-4 h-4 rounded-lg bg-[#3B82F6] border-2 border-[#2563EB]"></span>
                <span className="text-sm font-medium text-gray-700 dark:text-gray-300">En cours</span>
              </div>
              <div className="flex items-center space-x-3 p-2 rounded-lg bg-gray-50 dark:bg-gray-700">
                <span className="w-4 h-4 rounded-lg bg-[#10B981] border-2 border-[#059669]"></span>
                <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Complété</span>
              </div>
              <div className="flex items-center space-x-3 p-2 rounded-lg bg-gray-50 dark:bg-gray-700">
                <span className="w-4 h-4 rounded-lg bg-[#EF4444] border-2 border-[#DC2626]"></span>
                <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Erreur</span>
              </div>
              <div className="flex items-center space-x-3 p-2 rounded-lg bg-gray-50 dark:bg-gray-700">
                <span className="w-4 h-4 border-2 border-dashed border-[#F87171] rounded-lg bg-[#F87171]/20"></span>
                <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Radié</span>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default RequestDetailsOverview;
