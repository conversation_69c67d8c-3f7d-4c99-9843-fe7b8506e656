import { useState } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuSeparator, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { Sheet, SheetContent, SheetHeader, SheetTitle } from "@/components/ui/sheet";
import { Form, FormControl, FormField, FormItem, FormLabel } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Plus, MoreVertical, Edit, CheckCircle, Trash } from "lucide-react";
import { useForm } from "react-hook-form";
import { cn } from "@/lib/utils";
import { supabase } from "@/lib/supabase";
import { toast } from "sonner";
import { Tables } from "@/integrations/supabase/types";

type RequestError = Tables<"request_errors">;

type ErrorFormData = {
  request_id: string;
  error_type: 'temporelle' | 'documentaire' | 'chaine_de_titres' | 'conformite' | 'evaluation' | 'geographique' | 'limitation' | 'autre';
  title: string;
  description: string;
  severity: 'critique' | 'majeure' | 'mineure' | 'informationnelle';
  resolution_status: 'en_attente' | 'resolu' | 'reconnu';
  sort_order?: number | null;
};

// Mapping functions for enum conversion
const mapErrorTypeToDb = (type: 'temporelle' | 'documentaire' | 'chaine_de_titres' | 'conformite' | 'evaluation' | 'geographique' | 'limitation' | 'autre'): 'temporal' | 'documentary' | 'chain_of_title' | 'compliance' | 'evaluation' | 'geographic' | 'limitation' | 'other' => {
  const mapping = {
    'temporelle': 'temporal' as const,
    'documentaire': 'documentary' as const,
    'chaine_de_titres': 'chain_of_title' as const,
    'conformite': 'compliance' as const,
    'evaluation': 'evaluation' as const,
    'geographique': 'geographic' as const,
    'limitation': 'limitation' as const,
    'autre': 'other' as const,
  };
  return mapping[type];
};

const mapErrorTypeFromDb = (type: 'temporal' | 'documentary' | 'chain_of_title' | 'compliance' | 'evaluation' | 'geographic' | 'limitation' | 'other'): 'temporelle' | 'documentaire' | 'chaine_de_titres' | 'conformite' | 'evaluation' | 'geographique' | 'limitation' | 'autre' => {
  const mapping = {
    'temporal': 'temporelle' as const,
    'documentary': 'documentaire' as const,
    'chain_of_title': 'chaine_de_titres' as const,
    'compliance': 'conformite' as const,
    'evaluation': 'evaluation' as const,
    'geographic': 'geographique' as const,
    'limitation': 'limitation' as const,
    'other': 'autre' as const,
  };
  return mapping[type];
};

const mapSeverityToDb = (severity: 'critique' | 'majeure' | 'mineure' | 'informationnelle'): 'critical' | 'major' | 'minor' | 'informational' => {
  const mapping = {
    'critique': 'critical' as const,
    'majeure': 'major' as const,
    'mineure': 'minor' as const,
    'informationnelle': 'informational' as const,
  };
  return mapping[severity];
};

const mapSeverityFromDb = (severity: 'critical' | 'major' | 'minor' | 'informational'): 'critique' | 'majeure' | 'mineure' | 'informationnelle' => {
  const mapping = {
    'critical': 'critique' as const,
    'major': 'majeure' as const,
    'minor': 'mineure' as const,
    'informational': 'informationnelle' as const,
  };
  return mapping[severity];
};

const mapResolutionToDb = (status: 'en_attente' | 'resolu' | 'reconnu'): 'pending' | 'resolved' | 'acknowledged' => {
  const mapping = {
    'en_attente': 'pending' as const,
    'resolu': 'resolved' as const,
    'reconnu': 'acknowledged' as const,
  };
  return mapping[status];
};

const mapResolutionFromDb = (status: 'pending' | 'resolved' | 'acknowledged'): 'en_attente' | 'resolu' | 'reconnu' => {
  const mapping = {
    'pending': 'en_attente' as const,
    'resolved': 'resolu' as const,
    'acknowledged': 'reconnu' as const,
  };
  return mapping[status];
};

interface ErrorsListProps {
  requestId: string;
}

const ERROR_TYPES = [
  { value: 'temporelle', label: 'Temporelles' },
  { value: 'documentaire', label: 'Documentaires' },
  { value: 'chaine_de_titres', label: 'Chaîne de titres' },
  { value: 'conformite', label: 'Conformité' },
  { value: 'evaluation', label: 'Évaluation' },
  { value: 'geographique', label: 'Géographiques' },
  { value: 'limitation', label: 'Limitations' },
  { value: 'autre', label: 'Autres' },
];

const SEVERITY_OPTIONS = [
  { value: 'critique', label: 'Critique', variant: 'destructive' as const },
  { value: 'majeure', label: 'Majeure', variant: 'default' as const },
  { value: 'mineure', label: 'Mineure', variant: 'secondary' as const },
  { value: 'informationnelle', label: 'Information', variant: 'outline' as const },
];

export function ErrorsList({ requestId }: ErrorsListProps) {
  const [showAddForm, setShowAddForm] = useState(false);
  const [editingError, setEditingError] = useState<RequestError | null>(null);
  const [errorTypeFilter, setErrorTypeFilter] = useState<string>('all');
  const [severityFilter, setSeverityFilter] = useState<string>('all');
  
  const queryClient = useQueryClient();

  const { data: errors, isLoading } = useQuery({
    queryKey: ['request-errors', requestId],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('request_errors')
        .select('*')
        .eq('request_id', requestId);
      
      if (error) throw error;
      
      // Sort by severity in JavaScript (critical -> major -> minor -> informational)
      const sortedData = data.sort((a, b) => {
        const severityOrder = { 'critical': 0, 'major': 1, 'minor': 2, 'informational': 3 };
        const aOrder = severityOrder[a.severity] ?? 4;
        const bOrder = severityOrder[b.severity] ?? 4;
        
        if (aOrder !== bOrder) {
          return aOrder - bOrder;
        }
        
        // Secondary sort by sort_order if available
        if (a.sort_order !== null && b.sort_order !== null) {
          return a.sort_order - b.sort_order;
        }
        
        // Tertiary sort by created_at (newest first)
        return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
      });
      
      return sortedData as RequestError[];
    },
  });

  const addErrorMutation = useMutation({
    mutationFn: async (errorData: ErrorFormData) => {
      const dbData = {
        ...errorData,
        error_type: mapErrorTypeToDb(errorData.error_type),
        severity: mapSeverityToDb(errorData.severity),
        resolution_status: mapResolutionToDb(errorData.resolution_status),
      };
      const { data, error } = await supabase
        .from('request_errors')
        .insert([dbData])
        .select()
        .single();
      
      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['request-errors', requestId] });
      setShowAddForm(false);
      toast.success('Erreur ajoutée avec succès');
    },
    onError: (error) => {
      toast.error(`Erreur lors de l'ajout: ${error.message}`);
    },
  });

  const updateErrorMutation = useMutation({
    mutationFn: async ({ id, ...updateData }: ErrorFormData & { id: string }) => {
      const dbData = {
        ...updateData,
        error_type: mapErrorTypeToDb(updateData.error_type),
        severity: mapSeverityToDb(updateData.severity),
        resolution_status: mapResolutionToDb(updateData.resolution_status),
      };
      const { data, error } = await supabase
        .from('request_errors')
        .update(dbData)
        .eq('id', id)
        .select()
        .single();
      
      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['request-errors', requestId] });
      setEditingError(null);
      toast.success('Erreur mise à jour avec succès');
    },
    onError: (error) => {
      toast.error(`Erreur lors de la mise à jour: ${error.message}`);
    },
  });

  const deleteErrorMutation = useMutation({
    mutationFn: async (id: string) => {
      const { error } = await supabase
        .from('request_errors')
        .delete()
        .eq('id', id);
      
      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['request-errors', requestId] });
      toast.success('Erreur supprimée avec succès');
    },
    onError: (error) => {
      toast.error(`Erreur lors de la suppression: ${error.message}`);
    },
  });

  const form = useForm<ErrorFormData>({
    defaultValues: {
      request_id: requestId,
      error_type: 'autre',
      title: '',
      description: '',
      severity: 'mineure',
      resolution_status: 'en_attente',
    },
  });

  const filteredErrors = errors?.filter(error => {
    if (errorTypeFilter !== 'all' && error.error_type !== errorTypeFilter) return false;
    if (severityFilter !== 'all' && error.severity !== severityFilter) return false;
    return true;
  }) || [];

  const getSeverityVariant = (severity: string) => {
    return SEVERITY_OPTIONS.find(s => s.value === severity)?.variant || 'outline';
  };

  const getErrorTypeLabel = (type: string) => {
    return ERROR_TYPES.find(t => t.value === type)?.label || type;
  };

  const handleSubmit = (data: ErrorFormData) => {
    if (editingError) {
      updateErrorMutation.mutate({ ...data, id: editingError.id });
    } else {
      addErrorMutation.mutate(data);
    }
  };

  const handleEdit = (error: RequestError) => {
    setEditingError(error);
    form.reset({
      request_id: error.request_id,
      error_type: mapErrorTypeFromDb(error.error_type),
      title: error.title,
      description: error.description,
      severity: mapSeverityFromDb(error.severity),
      resolution_status: mapResolutionFromDb(error.resolution_status),
      sort_order: error.sort_order,
    });
  };

  const handleResolve = async (id: string) => {
    try {
      const { error } = await supabase
        .from('request_errors')
        .update({ resolution_status: 'resolved' })
        .eq('id', id);
      
      if (error) throw error;
      
      queryClient.invalidateQueries({ queryKey: ['request-errors', requestId] });
      toast.success('Erreur marquée comme résolue');
    } catch (err: any) {
      toast.error(`Erreur lors de la mise à jour: ${err.message}`);
    }
  };

  const handleDelete = (id: string) => {
    if (confirm('Êtes-vous sûr de vouloir supprimer cette erreur ?')) {
      deleteErrorMutation.mutate(id);
    }
  };

  if (isLoading) {
    return <div>Chargement...</div>;
  }

  return (
    <div className="space-y-4">
      {/* Filter bar */}
      <div className="flex gap-4 mb-4">
        <Select value={errorTypeFilter} onValueChange={setErrorTypeFilter}>
          <SelectTrigger className="w-48">
            <SelectValue placeholder="Type d'erreur" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">Toutes</SelectItem>
            {ERROR_TYPES.map(type => (
              <SelectItem key={type.value} value={type.value}>
                {type.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        
        <Select value={severityFilter} onValueChange={setSeverityFilter}>
          <SelectTrigger className="w-48">
            <SelectValue placeholder="Sévérité" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">Toutes</SelectItem>
            {SEVERITY_OPTIONS.map(severity => (
              <SelectItem key={severity.value} value={severity.value}>
                {severity.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        
        <Button onClick={() => setShowAddForm(true)}>
          <Plus className="w-4 h-4 mr-2" />
          Ajouter une erreur
        </Button>
      </div>
      
      {/* Error cards */}
      {filteredErrors.map((error) => (
        <Card key={error.id} className={cn(
          "p-4",
          error.severity === 'critical' && "border-red-500",
          error.resolution_status === 'resolved' && "opacity-60"
        )}>
          <div className="flex justify-between items-start">
            <div className="flex-1">
              <div className="flex items-center gap-2 mb-2">
                <Badge variant={getSeverityVariant(error.severity)}>
                  {SEVERITY_OPTIONS.find(s => s.value === error.severity)?.label}
                </Badge>
                <Badge variant="outline">
                  {getErrorTypeLabel(error.error_type)}
                </Badge>
                {error.resolution_status === 'resolved' && (
                  <Badge className="bg-green-100 text-green-800">Résolu</Badge>
                )}
              </div>
              
              <h4 className="font-semibold mb-1">{error.title}</h4>
              <p className="text-sm text-muted-foreground">
                {error.description}
              </p>
            </div>
            
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="sm">
                  <MoreVertical className="w-4 h-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent>
                <DropdownMenuItem onClick={() => handleEdit(error)}>
                  <Edit className="w-4 h-4 mr-2" />
                  Modifier
                </DropdownMenuItem>
                {error.resolution_status !== 'resolved' && (
                  <DropdownMenuItem onClick={() => handleResolve(error.id)}>
                    <CheckCircle className="w-4 h-4 mr-2" />
                    Marquer comme résolu
                  </DropdownMenuItem>
                )}
                <DropdownMenuSeparator />
                <DropdownMenuItem 
                  onClick={() => handleDelete(error.id)}
                  className="text-red-600"
                >
                  <Trash className="w-4 h-4 mr-2" />
                  Supprimer
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </Card>
      ))}

      {/* Add/Edit Form */}
      <Sheet open={showAddForm || !!editingError} onOpenChange={(open) => {
        if (!open) {
          setShowAddForm(false);
          setEditingError(null);
          form.reset();
        }
      }}>
        <SheetContent className="w-[600px]">
          <SheetHeader>
            <SheetTitle>
              {editingError ? 'Modifier l\'erreur' : 'Ajouter une erreur'}
            </SheetTitle>
          </SheetHeader>
          
          <Form {...form}>
            <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4 mt-4">
              <FormField
                control={form.control}
                name="error_type"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Type d'erreur</FormLabel>
                    <Select value={field.value} onValueChange={field.onChange}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Sélectionner un type" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {ERROR_TYPES.map(type => (
                          <SelectItem key={type.value} value={type.value}>
                            {type.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </FormItem>
                )}
              />
              
              <FormField
                control={form.control}
                name="severity"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Sévérité</FormLabel>
                    <Select value={field.value} onValueChange={field.onChange}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Sélectionner une sévérité" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {SEVERITY_OPTIONS.map(severity => (
                          <SelectItem key={severity.value} value={severity.value}>
                            {severity.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </FormItem>
                )}
              />
              
              <FormField
                control={form.control}
                name="title"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Titre</FormLabel>
                    <FormControl>
                      <Input {...field} placeholder="Titre de l'erreur" />
                    </FormControl>
                  </FormItem>
                )}
              />
              
              <FormField
                control={form.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Description</FormLabel>
                    <FormControl>
                      <Textarea {...field} placeholder="Description détaillée de l'erreur" rows={4} />
                    </FormControl>
                  </FormItem>
                )}
              />
              
              <div className="flex justify-end gap-2 pt-4">
                <Button type="button" variant="outline" onClick={() => {
                  setShowAddForm(false);
                  setEditingError(null);
                  form.reset();
                }}>
                  Annuler
                </Button>
                <Button type="submit" disabled={addErrorMutation.isPending || updateErrorMutation.isPending}>
                  {editingError ? 'Mettre à jour' : 'Ajouter'}
                </Button>
              </div>
            </form>
          </Form>
        </SheetContent>
      </Sheet>
    </div>
  );
}
