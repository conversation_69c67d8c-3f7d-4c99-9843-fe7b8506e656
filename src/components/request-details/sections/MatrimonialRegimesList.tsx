import { useState } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuSeparator, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { Sheet, SheetContent, SheetHeader, SheetTitle } from "@/components/ui/sheet";
import { Form, FormControl, FormField, FormItem, FormLabel } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Plus, MoreVertical, Edit, Trash } from "lucide-react";
import { useForm } from "react-hook-form";
import { cn } from "@/lib/utils";
import { supabase } from "@/lib/supabase";
import { toast } from "sonner";
import { Tables } from "@/integrations/supabase/types";

type MatrimonialRegime = Tables<"matrimonial_regimes">;

interface MatrimonialRegimesListProps {
  requestId: string;
}

const REGIME_TYPES = [
  { value: 'societe_acquets', label: 'Société d\'acquêts' },
  { value: 'communaute_biens', label: 'Communauté de biens' },
  { value: 'separation_biens', label: 'Séparation de biens' },
  { value: 'celibataire', label: 'Célibataire' },
  { value: 'divorce', label: 'Divorcé(e)' },
  { value: 'veuf', label: 'Veuf/Veuve' },
  { value: 'autre', label: 'Autre' },
];

export function MatrimonialRegimesList({ requestId }: MatrimonialRegimesListProps) {
  const [showAddForm, setShowAddForm] = useState(false);
  const [editingRegime, setEditingRegime] = useState<MatrimonialRegime | null>(null);
  const [regimeTypeFilter, setRegimeTypeFilter] = useState<string>('all');
  
  const queryClient = useQueryClient();

  const { data: regimes, isLoading } = useQuery({
    queryKey: ['matrimonial-regimes', requestId],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('matrimonial_regimes')
        .select('*')
        .eq('request_id', requestId)
        .order('sort_order', { ascending: true, nullsFirst: false })
        .order('created_at', { ascending: false });
      
      if (error) throw error;
      return data as MatrimonialRegime[];
    },
  });

  const addRegimeMutation = useMutation({
    mutationFn: async (regimeData: Omit<MatrimonialRegime, 'id' | 'created_at' | 'updated_at'>) => {
      const { data, error } = await supabase
        .from('matrimonial_regimes')
        .insert([regimeData])
        .select()
        .single();
      
      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['matrimonial-regimes', requestId] });
      setShowAddForm(false);
      toast.success('Régime matrimonial ajouté avec succès');
    },
    onError: (error) => {
      toast.error(`Erreur lors de l'ajout: ${error.message}`);
    },
  });

  const updateRegimeMutation = useMutation({
    mutationFn: async ({ id, ...updateData }: Partial<MatrimonialRegime> & { id: string }) => {
      const { data, error } = await supabase
        .from('matrimonial_regimes')
        .update(updateData)
        .eq('id', id)
        .select()
        .single();
      
      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['matrimonial-regimes', requestId] });
      setEditingRegime(null);
      toast.success('Régime matrimonial mis à jour avec succès');
    },
    onError: (error) => {
      toast.error(`Erreur lors de la mise à jour: ${error.message}`);
    },
  });

  const deleteRegimeMutation = useMutation({
    mutationFn: async (id: string) => {
      const { error } = await supabase
        .from('matrimonial_regimes')
        .delete()
        .eq('id', id);
      
      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['matrimonial-regimes', requestId] });
      toast.success('Régime matrimonial supprimé avec succès');
    },
    onError: (error) => {
      toast.error(`Erreur lors de la suppression: ${error.message}`);
    },
  });

  const form = useForm<Omit<MatrimonialRegime, 'id' | 'created_at' | 'updated_at'>>({
    defaultValues: {
      request_id: requestId,
      person_name: '',
      spouse_name: '',
      regime_type: 'societe_acquets',
      marriage_date: '',
      divorce_date: '',
      contract_details: '',
      impact_on_title: '',
    },
  });

  const filteredRegimes = regimes?.filter(regime => {
    if (regimeTypeFilter !== 'all' && regime.regime_type !== regimeTypeFilter) return false;
    return true;
  }) || [];

  const getRegimeTypeLabel = (type: string) => {
    return REGIME_TYPES.find(t => t.value === type)?.label || type;
  };

  const handleSubmit = (data: Omit<MatrimonialRegime, 'id' | 'created_at' | 'updated_at'>) => {
    if (editingRegime) {
      updateRegimeMutation.mutate({ ...data, id: editingRegime.id });
    } else {
      addRegimeMutation.mutate(data);
    }
  };

  const handleEdit = (regime: MatrimonialRegime) => {
    setEditingRegime(regime);
    form.reset({
      request_id: regime.request_id,
      person_name: regime.person_name,
      spouse_name: regime.spouse_name || '',
      regime_type: regime.regime_type,
      marriage_date: regime.marriage_date || '',
      divorce_date: regime.divorce_date || '',
      contract_details: regime.contract_details || '',
      impact_on_title: regime.impact_on_title,
      sort_order: regime.sort_order,
    });
  };

  const handleDelete = (id: string) => {
    if (confirm('Êtes-vous sûr de vouloir supprimer ce régime matrimonial ?')) {
      deleteRegimeMutation.mutate(id);
    }
  };

  if (isLoading) {
    return <div>Chargement...</div>;
  }

  return (
    <div className="space-y-4">
      {/* Filter bar */}
      <div className="flex gap-4 mb-4">
        <Select value={regimeTypeFilter} onValueChange={setRegimeTypeFilter}>
          <SelectTrigger className="w-48">
            <SelectValue placeholder="Type de régime" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">Tous</SelectItem>
            {REGIME_TYPES.map(type => (
              <SelectItem key={type.value} value={type.value}>
                {type.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        
        <Button onClick={() => setShowAddForm(true)}>
          <Plus className="w-4 h-4 mr-2" />
          Ajouter un régime
        </Button>
      </div>
      
      {/* Regime cards */}
      {filteredRegimes.map((regime) => (
        <Card key={regime.id} className="p-4">
          <div className="flex justify-between items-start">
            <div className="flex-1">
              <div className="flex items-center gap-2 mb-2">
                <Badge variant="outline">
                  {getRegimeTypeLabel(regime.regime_type)}
                </Badge>
              </div>
              
              <h4 className="font-semibold mb-1">{regime.person_name}</h4>
              {regime.spouse_name && (
                <p className="text-sm text-muted-foreground mb-2">
                  Conjoint(e): {regime.spouse_name}
                </p>
              )}
              {regime.marriage_date && (
                <p className="text-sm text-muted-foreground mb-2">
                  Date de mariage: {regime.marriage_date}
                </p>
              )}
              {regime.divorce_date && (
                <p className="text-sm text-muted-foreground mb-2">
                  Date de divorce: {regime.divorce_date}
                </p>
              )}
              <p className="text-sm mb-2">
                <strong>Impact sur le titre:</strong> {regime.impact_on_title}
              </p>
              {regime.contract_details && (
                <p className="text-sm text-muted-foreground">
                  <strong>Détails du contrat:</strong> {regime.contract_details}
                </p>
              )}
            </div>
            
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="sm">
                  <MoreVertical className="w-4 h-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent>
                <DropdownMenuItem onClick={() => handleEdit(regime)}>
                  <Edit className="w-4 h-4 mr-2" />
                  Modifier
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem 
                  onClick={() => handleDelete(regime.id)}
                  className="text-red-600"
                >
                  <Trash className="w-4 h-4 mr-2" />
                  Supprimer
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </Card>
      ))}

      {/* Add/Edit Form */}
      <Sheet open={showAddForm || !!editingRegime} onOpenChange={(open) => {
        if (!open) {
          setShowAddForm(false);
          setEditingRegime(null);
          form.reset();
        }
      }}>
        <SheetContent className="w-[600px]">
          <SheetHeader>
            <SheetTitle>
              {editingRegime ? 'Modifier le régime matrimonial' : 'Ajouter un régime matrimonial'}
            </SheetTitle>
          </SheetHeader>
          
          <Form {...form}>
            <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4 mt-4">
              <FormField
                control={form.control}
                name="person_name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Nom de la personne</FormLabel>
                    <FormControl>
                      <Input {...field} placeholder="Nom complet" />
                    </FormControl>
                  </FormItem>
                )}
              />
              
              <FormField
                control={form.control}
                name="spouse_name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Nom du/de la conjoint(e)</FormLabel>
                    <FormControl>
                      <Input {...field} placeholder="Nom du conjoint (optionnel)" />
                    </FormControl>
                  </FormItem>
                )}
              />
              
              <FormField
                control={form.control}
                name="regime_type"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Type de régime</FormLabel>
                    <Select value={field.value} onValueChange={field.onChange}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Sélectionner un type" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {REGIME_TYPES.map(type => (
                          <SelectItem key={type.value} value={type.value}>
                            {type.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </FormItem>
                )}
              />
              
              <FormField
                control={form.control}
                name="marriage_date"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Date de mariage</FormLabel>
                    <FormControl>
                      <Input {...field} type="date" />
                    </FormControl>
                  </FormItem>
                )}
              />
              
              <FormField
                control={form.control}
                name="divorce_date"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Date de divorce</FormLabel>
                    <FormControl>
                      <Input {...field} type="date" />
                    </FormControl>
                  </FormItem>
                )}
              />
              
              <FormField
                control={form.control}
                name="impact_on_title"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Impact sur le titre</FormLabel>
                    <FormControl>
                      <Textarea {...field} placeholder="Expliquer l'impact de ce régime sur le titre de propriété" rows={3} />
                    </FormControl>
                  </FormItem>
                )}
              />
              
              <FormField
                control={form.control}
                name="contract_details"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Détails du contrat</FormLabel>
                    <FormControl>
                      <Textarea {...field} placeholder="Détails du contrat de mariage (optionnel)" rows={3} />
                    </FormControl>
                  </FormItem>
                )}
              />
              
              <div className="flex justify-end gap-2 pt-4">
                <Button type="button" variant="outline" onClick={() => {
                  setShowAddForm(false);
                  setEditingRegime(null);
                  form.reset();
                }}>
                  Annuler
                </Button>
                <Button type="submit" disabled={addRegimeMutation.isPending || updateRegimeMutation.isPending}>
                  {editingRegime ? 'Mettre à jour' : 'Ajouter'}
                </Button>
              </div>
            </form>
          </Form>
        </SheetContent>
      </Sheet>
    </div>
  );
}
