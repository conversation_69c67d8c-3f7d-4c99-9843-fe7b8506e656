import { useState } from "react";
import { useQ<PERSON>y, useMutation, useQueryClient } from "@tanstack/react-query";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuSeparator, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { Sheet, SheetContent, SheetHeader, SheetTitle } from "@/components/ui/sheet";
import { Form, FormControl, FormField, FormItem, FormLabel } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Checkbox } from "@/components/ui/checkbox";
import { Plus, MoreVertical, Edit, Trash, Alert<PERSON>riangle, CheckCircle, Info } from "lucide-react";
import { useForm } from "react-hook-form";
import { cn } from "@/lib/utils";
import { supabase } from "@/lib/supabase";
import { toast } from "sonner";
import { Tables } from "@/integrations/supabase/types";

type OtherConsideration = Tables<"other_considerations">;

type ConsiderationFormData = {
  request_id: string;
  category: 'cadastral' | 'financier' | 'physique' | 'juridique' | 'municipal' | 'environnemental' | 'autre';
  title: string;
  description: string;
  importance: 'haute' | 'moyenne' | 'basse';
  requires_action: boolean;
  action_taken: string;
  sort_order?: number | null;
};

// Mapping functions for enum conversion
const mapCategoryToDb = (category: 'cadastral' | 'financier' | 'physique' | 'juridique' | 'municipal' | 'environnemental' | 'autre'): 'cadastral' | 'financial' | 'physical' | 'legal' | 'municipal' | 'environmental' | 'other' => {
  const mapping = {
    'cadastral': 'cadastral' as const,
    'financier': 'financial' as const,
    'physique': 'physical' as const,
    'juridique': 'legal' as const,
    'municipal': 'municipal' as const,
    'environnemental': 'environmental' as const,
    'autre': 'other' as const,
  };
  return mapping[category];
};

const mapCategoryFromDb = (category: 'cadastral' | 'financial' | 'physical' | 'legal' | 'municipal' | 'environmental' | 'other'): 'cadastral' | 'financier' | 'physique' | 'juridique' | 'municipal' | 'environnemental' | 'autre' => {
  const mapping = {
    'cadastral': 'cadastral' as const,
    'financial': 'financier' as const,
    'physical': 'physique' as const,
    'legal': 'juridique' as const,
    'municipal': 'municipal' as const,
    'environmental': 'environnemental' as const,
    'other': 'autre' as const,
  };
  return mapping[category];
};

const mapImportanceToDb = (importance: 'haute' | 'moyenne' | 'basse'): 'high' | 'medium' | 'low' => {
  const mapping = {
    'haute': 'high' as const,
    'moyenne': 'medium' as const,
    'basse': 'low' as const,
  };
  return mapping[importance];
};

const mapImportanceFromDb = (importance: 'high' | 'medium' | 'low'): 'haute' | 'moyenne' | 'basse' => {
  const mapping = {
    'high': 'haute' as const,
    'medium': 'moyenne' as const,
    'low': 'basse' as const,
  };
  return mapping[importance];
};

interface OtherConsiderationsListProps {
  requestId: string;
}

const CATEGORIES = [
  { value: 'cadastral', label: 'Cadastral', icon: '🗺️' },
  { value: 'financier', label: 'Financier', icon: '💰' },
  { value: 'physique', label: 'Physique', icon: '🏗️' },
  { value: 'juridique', label: 'Juridique', icon: '⚖️' },
  { value: 'municipal', label: 'Municipal', icon: '🏛️' },
  { value: 'environnemental', label: 'Environnemental', icon: '🌿' },
  { value: 'autre', label: 'Autre', icon: '📝' },
];

const IMPORTANCE_OPTIONS = [
  { value: 'haute', label: 'Haute', variant: 'destructive' as const, icon: AlertTriangle },
  { value: 'moyenne', label: 'Moyenne', variant: 'default' as const, icon: Info },
  { value: 'basse', label: 'Basse', variant: 'secondary' as const, icon: CheckCircle },
];

export function OtherConsiderationsList({ requestId }: OtherConsiderationsListProps) {
  const [showAddForm, setShowAddForm] = useState(false);
  const [editingConsideration, setEditingConsideration] = useState<OtherConsideration | null>(null);
  const [categoryFilter, setCategoryFilter] = useState<string>('all');
  const [importanceFilter, setImportanceFilter] = useState<string>('all');
  const [requiresActionFilter, setRequiresActionFilter] = useState<string>('all');
  
  const queryClient = useQueryClient();

  const { data: considerations, isLoading } = useQuery({
    queryKey: ['other-considerations', requestId],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('other_considerations')
        .select('*')
        .eq('request_id', requestId);
      
      if (error) throw error;
      
      // Sort by importance in JavaScript (high -> medium -> low)
      const sortedData = data.sort((a, b) => {
        const importanceOrder = { 'high': 0, 'medium': 1, 'low': 2 };
        const aOrder = importanceOrder[a.importance] ?? 3;
        const bOrder = importanceOrder[b.importance] ?? 3;
        
        if (aOrder !== bOrder) {
          return aOrder - bOrder;
        }
        
        // Secondary sort by sort_order if available
        if (a.sort_order !== null && b.sort_order !== null) {
          return a.sort_order - b.sort_order;
        }
        
        // Tertiary sort by created_at (newest first)
        return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
      });
      
      return sortedData as OtherConsideration[];
    },
  });

  const addConsiderationMutation = useMutation({
    mutationFn: async (considerationData: ConsiderationFormData) => {
      const dbData = {
        ...considerationData,
        category: mapCategoryToDb(considerationData.category),
        importance: mapImportanceToDb(considerationData.importance),
      };
      const { data, error } = await supabase
        .from('other_considerations')
        .insert([dbData])
        .select()
        .single();
      
      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['other-considerations', requestId] });
      setShowAddForm(false);
      toast.success('Considération ajoutée avec succès');
    },
    onError: (error) => {
      toast.error(`Erreur lors de l'ajout: ${error.message}`);
    },
  });

  const updateConsiderationMutation = useMutation({
    mutationFn: async ({ id, ...updateData }: ConsiderationFormData & { id: string }) => {
      const dbData = {
        ...updateData,
        category: mapCategoryToDb(updateData.category),
        importance: mapImportanceToDb(updateData.importance),
      };
      const { data, error } = await supabase
        .from('other_considerations')
        .update(dbData)
        .eq('id', id)
        .select()
        .single();
      
      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['other-considerations', requestId] });
      setEditingConsideration(null);
      toast.success('Considération mise à jour avec succès');
    },
    onError: (error) => {
      toast.error(`Erreur lors de la mise à jour: ${error.message}`);
    },
  });

  const deleteConsiderationMutation = useMutation({
    mutationFn: async (id: string) => {
      const { error } = await supabase
        .from('other_considerations')
        .delete()
        .eq('id', id);
      
      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['other-considerations', requestId] });
      toast.success('Considération supprimée avec succès');
    },
    onError: (error) => {
      toast.error(`Erreur lors de la suppression: ${error.message}`);
    },
  });

  const form = useForm<ConsiderationFormData>({
    defaultValues: {
      request_id: requestId,
      category: 'juridique',
      title: '',
      description: '',
      importance: 'moyenne',
      requires_action: false,
      action_taken: '',
    },
  });

  const filteredConsiderations = considerations?.filter(consideration => {
    if (categoryFilter !== 'all' && consideration.category !== categoryFilter) return false;
    if (importanceFilter !== 'all' && consideration.importance !== importanceFilter) return false;
    if (requiresActionFilter === 'true' && !consideration.requires_action) return false;
    if (requiresActionFilter === 'false' && consideration.requires_action) return false;
    return true;
  }) || [];

  const getCategoryInfo = (category: string) => {
    return CATEGORIES.find(c => c.value === category) || { label: category, icon: '📝' };
  };

  const getImportanceInfo = (importance: string) => {
    return IMPORTANCE_OPTIONS.find(i => i.value === importance) || IMPORTANCE_OPTIONS[1];
  };

  const handleSubmit = (data: ConsiderationFormData) => {
    if (editingConsideration) {
      updateConsiderationMutation.mutate({ ...data, id: editingConsideration.id });
    } else {
      addConsiderationMutation.mutate(data);
    }
  };

  const handleEdit = (consideration: OtherConsideration) => {
    setEditingConsideration(consideration);
    form.reset({
      request_id: consideration.request_id,
      category: mapCategoryFromDb(consideration.category),
      title: consideration.title,
      description: consideration.description,
      importance: mapImportanceFromDb(consideration.importance),
      requires_action: consideration.requires_action,
      action_taken: consideration.action_taken || '',
      sort_order: consideration.sort_order,
    });
  };

  const handleDelete = (id: string) => {
    if (confirm('Êtes-vous sûr de vouloir supprimer cette considération ?')) {
      deleteConsiderationMutation.mutate(id);
    }
  };

  if (isLoading) {
    return <div>Chargement...</div>;
  }

  return (
    <div className="space-y-4">
      {/* Filter bar */}
      <div className="flex gap-4 mb-4 flex-wrap">
        <Select value={categoryFilter} onValueChange={setCategoryFilter}>
          <SelectTrigger className="w-48">
            <SelectValue placeholder="Catégorie" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">Toutes</SelectItem>
            {CATEGORIES.map(category => (
              <SelectItem key={category.value} value={category.value}>
                {category.icon} {category.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        
        <Select value={importanceFilter} onValueChange={setImportanceFilter}>
          <SelectTrigger className="w-48">
            <SelectValue placeholder="Importance" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">Toutes</SelectItem>
            {IMPORTANCE_OPTIONS.map(importance => (
              <SelectItem key={importance.value} value={importance.value}>
                {importance.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        
        <Select value={requiresActionFilter} onValueChange={setRequiresActionFilter}>
          <SelectTrigger className="w-48">
            <SelectValue placeholder="Action requise" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">Toutes</SelectItem>
            <SelectItem value="true">Action requise</SelectItem>
            <SelectItem value="false">Aucune action</SelectItem>
          </SelectContent>
        </Select>
        
        <Button onClick={() => setShowAddForm(true)}>
          <Plus className="w-4 h-4 mr-2" />
          Ajouter une considération
        </Button>
      </div>
      
      {/* Consideration cards */}
      {filteredConsiderations.map((consideration) => {
        const categoryInfo = getCategoryInfo(consideration.category);
        const importanceInfo = getImportanceInfo(consideration.importance);
        const IconComponent = importanceInfo.icon;
        
        return (
          <Card key={consideration.id} className="p-4">
            <div className="flex justify-between items-start">
              <div className="flex-1">
                <div className="flex items-center gap-2 mb-2">
                  <Badge variant="outline">
                    {categoryInfo.icon} {categoryInfo.label}
                  </Badge>
                  <Badge variant={importanceInfo.variant}>
                    <IconComponent className="w-3 h-3 mr-1" />
                    {importanceInfo.label}
                  </Badge>
                  {consideration.requires_action && (
                    <Badge variant="secondary">
                      Action requise
                    </Badge>
                  )}
                </div>
                
                <h4 className="font-semibold mb-2">{consideration.title}</h4>
                
                <p className="text-sm text-muted-foreground mb-3">
                  {consideration.description}
                </p>
                
                {consideration.requires_action && consideration.action_taken && (
                  <div className="text-sm">
                    <span className="font-medium text-green-600">Action prise:</span>
                    <p className="text-muted-foreground mt-1">{consideration.action_taken}</p>
                  </div>
                )}
              </div>
              
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="sm">
                    <MoreVertical className="w-4 h-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent>
                  <DropdownMenuItem onClick={() => handleEdit(consideration)}>
                    <Edit className="w-4 h-4 mr-2" />
                    Modifier
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem 
                    onClick={() => handleDelete(consideration.id)}
                    className="text-red-600"
                  >
                    <Trash className="w-4 h-4 mr-2" />
                    Supprimer
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </Card>
        );
      })}

      {/* Add/Edit Form */}
      <Sheet open={showAddForm || !!editingConsideration} onOpenChange={(open) => {
        if (!open) {
          setShowAddForm(false);
          setEditingConsideration(null);
          form.reset();
        }
      }}>
        <SheetContent className="w-[600px]">
          <SheetHeader>
            <SheetTitle>
              {editingConsideration ? 'Modifier la considération' : 'Ajouter une considération'}
            </SheetTitle>
          </SheetHeader>
          
          <Form {...form}>
            <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4 mt-4">
              <FormField
                control={form.control}
                name="category"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Catégorie</FormLabel>
                    <Select value={field.value} onValueChange={field.onChange}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Sélectionner une catégorie" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {CATEGORIES.map(category => (
                          <SelectItem key={category.value} value={category.value}>
                            {category.icon} {category.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </FormItem>
                )}
              />
              
              <FormField
                control={form.control}
                name="title"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Titre</FormLabel>
                    <FormControl>
                      <Input {...field} placeholder="Titre de la considération" />
                    </FormControl>
                  </FormItem>
                )}
              />
              
              <FormField
                control={form.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Description</FormLabel>
                    <FormControl>
                      <Textarea {...field} placeholder="Description détaillée" rows={3} />
                    </FormControl>
                  </FormItem>
                )}
              />
              
              <FormField
                control={form.control}
                name="importance"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Niveau d'importance</FormLabel>
                    <Select value={field.value} onValueChange={field.onChange}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Sélectionner un niveau" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {IMPORTANCE_OPTIONS.map(importance => (
                          <SelectItem key={importance.value} value={importance.value}>
                            {importance.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </FormItem>
                )}
              />
              
              <FormField
                control={form.control}
                name="requires_action"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                    <FormControl>
                      <Checkbox
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                    <div className="space-y-1 leading-none">
                      <FormLabel>
                        Action requise
                      </FormLabel>
                      <p className="text-sm text-muted-foreground">
                        Cette considération nécessite-t-elle une action de suivi ?
                      </p>
                    </div>
                  </FormItem>
                )}
              />
              
              <FormField
                control={form.control}
                name="action_taken"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Action prise</FormLabel>
                    <FormControl>
                      <Textarea {...field} placeholder="Décrivez l'action prise (si applicable)" rows={3} />
                    </FormControl>
                  </FormItem>
                )}
              />
              
              <div className="flex justify-end gap-2 pt-4">
                <Button type="button" variant="outline" onClick={() => {
                  setShowAddForm(false);
                  setEditingConsideration(null);
                  form.reset();
                }}>
                  Annuler
                </Button>
                <Button type="submit" disabled={addConsiderationMutation.isPending || updateConsiderationMutation.isPending}>
                  {editingConsideration ? 'Mettre à jour' : 'Ajouter'}
                </Button>
              </div>
            </form>
          </Form>
        </SheetContent>
      </Sheet>
    </div>
  );
}
