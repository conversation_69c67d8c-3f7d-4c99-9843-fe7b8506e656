import { useState } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuSeparator, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { Sheet, SheetContent, SheetHeader, SheetTitle } from "@/components/ui/sheet";
import { Form, FormControl, FormField, FormItem, FormLabel } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Plus, MoreVertical, Edit, Trash, Calendar, Clock } from "lucide-react";
import { useForm } from "react-hook-form";
import { cn } from "@/lib/utils";
import { supabase } from "@/lib/supabase";
import { toast } from "sonner";
import { Tables } from "@/integrations/supabase/types";

type Servitude = Tables<"servitudes">;

type ServitudeFormData = {
  request_id: string;
  servitude_type: 'passage' | 'vue' | 'utilite_publique' | 'aqueduc_egout' | 'nuisance' | 'hydro_quebec' | 'bell' | 'autre';
  beneficiary: string;
  burden_party: string;
  registration_number: string;
  registration_date: string;
  duration: 'perpetuelle' | 'temporaire' | 'conditionnelle';
  expiry_date: string;
  description: string;
  status: 'actif' | 'expire' | 'radie';
  sort_order?: number | null;
};

// Mapping functions for enum conversion
const mapDurationToDb = (duration: 'perpetuelle' | 'temporaire' | 'conditionnelle'): 'perpetual' | 'temporary' | 'conditional' => {
  const mapping = {
    'perpetuelle': 'perpetual' as const,
    'temporaire': 'temporary' as const,
    'conditionnelle': 'conditional' as const,
  };
  return mapping[duration];
};

const mapDurationFromDb = (duration: 'perpetual' | 'temporary' | 'conditional'): 'perpetuelle' | 'temporaire' | 'conditionnelle' => {
  const mapping = {
    'perpetual': 'perpetuelle' as const,
    'temporary': 'temporaire' as const,
    'conditional': 'conditionnelle' as const,
  };
  return mapping[duration];
};

const mapStatusToDb = (status: 'actif' | 'expire' | 'radie'): 'active' | 'expired' | 'radiated' => {
  const mapping = {
    'actif': 'active' as const,
    'expire': 'expired' as const,
    'radie': 'radiated' as const,
  };
  return mapping[status];
};

const mapStatusFromDb = (status: 'active' | 'expired' | 'radiated'): 'actif' | 'expire' | 'radie' => {
  const mapping = {
    'active': 'actif' as const,
    'expired': 'expire' as const,
    'radiated': 'radie' as const,
  };
  return mapping[status];
};

interface ServitudesListProps {
  requestId: string;
}

const SERVITUDE_TYPES = [
  { value: 'passage', label: 'Passage' },
  { value: 'vue', label: 'Vue' },
  { value: 'utilite_publique', label: 'Utilité publique' },
  { value: 'aqueduc_egout', label: 'Aqueduc/Égout' },
  { value: 'nuisance', label: 'Nuisance' },
  { value: 'hydro_quebec', label: 'Hydro-Québec' },
  { value: 'bell', label: 'Bell' },
  { value: 'autre', label: 'Autre' },
];

const DURATION_OPTIONS = [
  { value: 'perpetuelle', label: 'Perpétuelle' },
  { value: 'temporaire', label: 'Temporaire' },
  { value: 'conditionnelle', label: 'Conditionnelle' },
] as const;

const STATUS_OPTIONS = [
  { value: 'actif', label: 'Actif', variant: 'default' as const },
  { value: 'expire', label: 'Expiré', variant: 'secondary' as const },
  { value: 'radie', label: 'Radié', variant: 'outline' as const },
] as const;

export function ServitudesList({ requestId }: ServitudesListProps) {
  const [showAddForm, setShowAddForm] = useState(false);
  const [editingServitude, setEditingServitude] = useState<Servitude | null>(null);
  const [typeFilter, setTypeFilter] = useState<string>('all');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  
  const queryClient = useQueryClient();

  const { data: servitudes, isLoading } = useQuery({
    queryKey: ['servitudes', requestId],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('servitudes')
        .select('*')
        .eq('request_id', requestId)
        .order('sort_order', { ascending: true, nullsFirst: false })
        .order('registration_date', { ascending: false });
      
      if (error) throw error;
      return data as Servitude[];
    },
  });

  const addServitudeMutation = useMutation({
    mutationFn: async (servitudeData: ServitudeFormData) => {
      const dbData = {
        ...servitudeData,
        duration: mapDurationToDb(servitudeData.duration),
        status: mapStatusToDb(servitudeData.status),
      };
      const { data, error } = await supabase
        .from('servitudes')
        .insert([dbData])
        .select()
        .single();
      
      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['servitudes', requestId] });
      setShowAddForm(false);
      toast.success('Servitude ajoutée avec succès');
    },
    onError: (error) => {
      toast.error(`Erreur lors de l'ajout: ${error.message}`);
    },
  });

  const updateServitudeMutation = useMutation({
    mutationFn: async ({ id, ...updateData }: ServitudeFormData & { id: string }) => {
      const dbData = {
        ...updateData,
        duration: mapDurationToDb(updateData.duration),
        status: mapStatusToDb(updateData.status),
      };
      const { data, error } = await supabase
        .from('servitudes')
        .update(dbData)
        .eq('id', id)
        .select()
        .single();
      
      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['servitudes', requestId] });
      setEditingServitude(null);
      toast.success('Servitude mise à jour avec succès');
    },
    onError: (error) => {
      toast.error(`Erreur lors de la mise à jour: ${error.message}`);
    },
  });

  const deleteServitudeMutation = useMutation({
    mutationFn: async (id: string) => {
      const { error } = await supabase
        .from('servitudes')
        .delete()
        .eq('id', id);
      
      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['servitudes', requestId] });
      toast.success('Servitude supprimée avec succès');
    },
    onError: (error) => {
      toast.error(`Erreur lors de la suppression: ${error.message}`);
    },
  });

  const form = useForm<ServitudeFormData>({
    defaultValues: {
      request_id: requestId,
      servitude_type: 'passage',
      beneficiary: '',
      burden_party: '',
      registration_number: '',
      registration_date: '',
      duration: 'perpetuelle',
      expiry_date: '',
      description: '',
      status: 'actif',
    },
  });

  const filteredServitudes = servitudes?.filter(servitude => {
    if (typeFilter !== 'all' && servitude.servitude_type !== typeFilter) return false;
    if (statusFilter !== 'all' && servitude.status !== statusFilter) return false;
    return true;
  }) || [];

  const getTypeLabel = (type: string) => {
    return SERVITUDE_TYPES.find(t => t.value === type)?.label || type;
  };

  const getDurationLabel = (duration: string) => {
    return DURATION_OPTIONS.find(d => d.value === duration)?.label || duration;
  };

  const getStatusVariant = (status: string) => {
    return STATUS_OPTIONS.find(s => s.value === status)?.variant || 'outline';
  };

  const getStatusLabel = (status: string) => {
    return STATUS_OPTIONS.find(s => s.value === status)?.label || status;
  };

  const handleSubmit = (data: ServitudeFormData) => {
    if (editingServitude) {
      updateServitudeMutation.mutate({ ...data, id: editingServitude.id });
    } else {
      addServitudeMutation.mutate(data);
    }
  };

  const handleEdit = (servitude: Servitude) => {
    setEditingServitude(servitude);
    form.reset({
      request_id: servitude.request_id,
      servitude_type: servitude.servitude_type,
      beneficiary: servitude.beneficiary,
      burden_party: servitude.burden_party || '',
      registration_number: servitude.registration_number,
      registration_date: servitude.registration_date,
      duration: mapDurationFromDb(servitude.duration),
      expiry_date: servitude.expiry_date || '',
      description: servitude.description,
      status: mapStatusFromDb(servitude.status),
      sort_order: servitude.sort_order,
    });
  };

  const handleDelete = (id: string) => {
    if (confirm('Êtes-vous sûr de vouloir supprimer cette servitude ?')) {
      deleteServitudeMutation.mutate(id);
    }
  };

  if (isLoading) {
    return <div>Chargement...</div>;
  }

  return (
    <div className="space-y-4">
      {/* Filter bar */}
      <div className="flex gap-4 mb-4">
        <Select value={typeFilter} onValueChange={setTypeFilter}>
          <SelectTrigger className="w-48">
            <SelectValue placeholder="Type de servitude" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">Toutes</SelectItem>
            {SERVITUDE_TYPES.map(type => (
              <SelectItem key={type.value} value={type.value}>
                {type.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        
        <Select value={statusFilter} onValueChange={setStatusFilter}>
          <SelectTrigger className="w-48">
            <SelectValue placeholder="Statut" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">Tous</SelectItem>
            {STATUS_OPTIONS.map(status => (
              <SelectItem key={status.value} value={status.value}>
                {status.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        
        <Button onClick={() => setShowAddForm(true)}>
          <Plus className="w-4 h-4 mr-2" />
          Ajouter une servitude
        </Button>
      </div>
      
      {/* Servitude cards */}
      {filteredServitudes.map((servitude) => (
        <Card key={servitude.id} className={cn(
          "p-4",
          servitude.status === 'expired' && "opacity-60"
        )}>
          <div className="flex justify-between items-start">
            <div className="flex-1">
              <div className="flex items-center gap-2 mb-2">
                <Badge variant="outline">
                  {getTypeLabel(servitude.servitude_type)}
                </Badge>
                <Badge variant={getStatusVariant(servitude.status)}>
                  {getStatusLabel(servitude.status)}
                </Badge>
                <div className="flex items-center text-sm text-muted-foreground">
                  <Calendar className="w-4 h-4 mr-1" />
                  {new Date(servitude.registration_date).toLocaleDateString('fr-CA')}
                </div>
              </div>
              
              <h4 className="font-semibold mb-2">
                No. {servitude.registration_number}
              </h4>
              
              <div className="space-y-1 text-sm">
                <div>
                  <span className="font-medium">Bénéficiaire:</span> {servitude.beneficiary}
                </div>
                {servitude.burden_party && (
                  <div>
                    <span className="font-medium">Partie grevée:</span> {servitude.burden_party}
                  </div>
                )}
                <div className="flex items-center gap-4">
                  <div className="flex items-center">
                    <Clock className="w-4 h-4 mr-1" />
                    <span className="font-medium">{getDurationLabel(servitude.duration)}</span>
                  </div>
                  {servitude.expiry_date && (
                    <div>
                      <span className="font-medium">Expire:</span> {new Date(servitude.expiry_date).toLocaleDateString('fr-CA')}
                    </div>
                  )}
                </div>
                <p className="text-muted-foreground mt-2">
                  {servitude.description}
                </p>
              </div>
            </div>
            
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="sm">
                  <MoreVertical className="w-4 h-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent>
                <DropdownMenuItem onClick={() => handleEdit(servitude)}>
                  <Edit className="w-4 h-4 mr-2" />
                  Modifier
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem 
                  onClick={() => handleDelete(servitude.id)}
                  className="text-red-600"
                >
                  <Trash className="w-4 h-4 mr-2" />
                  Supprimer
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </Card>
      ))}

      {/* Add/Edit Form */}
      <Sheet open={showAddForm || !!editingServitude} onOpenChange={(open) => {
        if (!open) {
          setShowAddForm(false);
          setEditingServitude(null);
          form.reset();
        }
      }}>
        <SheetContent className="w-[600px]">
          <SheetHeader>
            <SheetTitle>
              {editingServitude ? 'Modifier la servitude' : 'Ajouter une servitude'}
            </SheetTitle>
          </SheetHeader>
          
          <Form {...form}>
            <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4 mt-4">
              <FormField
                control={form.control}
                name="servitude_type"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Type de servitude</FormLabel>
                    <Select value={field.value} onValueChange={field.onChange}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Sélectionner un type" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {SERVITUDE_TYPES.map(type => (
                          <SelectItem key={type.value} value={type.value}>
                            {type.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </FormItem>
                )}
              />
              
              <FormField
                control={form.control}
                name="beneficiary"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Bénéficiaire</FormLabel>
                    <FormControl>
                      <Input {...field} placeholder="Nom du bénéficiaire" />
                    </FormControl>
                  </FormItem>
                )}
              />
              
              <FormField
                control={form.control}
                name="burden_party"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Partie grevée</FormLabel>
                    <FormControl>
                      <Input {...field} placeholder="Nom de la partie grevée (optionnel)" />
                    </FormControl>
                  </FormItem>
                )}
              />
              
              <FormField
                control={form.control}
                name="registration_number"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Numéro d'enregistrement</FormLabel>
                    <FormControl>
                      <Input {...field} placeholder="Numéro d'enregistrement" />
                    </FormControl>
                  </FormItem>
                )}
              />
              
              <FormField
                control={form.control}
                name="registration_date"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Date d'enregistrement</FormLabel>
                    <FormControl>
                      <Input {...field} type="date" />
                    </FormControl>
                  </FormItem>
                )}
              />
              
              <FormField
                control={form.control}
                name="duration"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Durée</FormLabel>
                    <Select value={field.value} onValueChange={field.onChange}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Sélectionner une durée" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {DURATION_OPTIONS.map(duration => (
                          <SelectItem key={duration.value} value={duration.value}>
                            {duration.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </FormItem>
                )}
              />
              
              <FormField
                control={form.control}
                name="expiry_date"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Date d'expiration</FormLabel>
                    <FormControl>
                      <Input {...field} type="date" />
                    </FormControl>
                  </FormItem>
                )}
              />
              
              <FormField
                control={form.control}
                name="status"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Statut</FormLabel>
                    <Select value={field.value} onValueChange={field.onChange}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Sélectionner un statut" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {STATUS_OPTIONS.map(status => (
                          <SelectItem key={status.value} value={status.value}>
                            {status.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </FormItem>
                )}
              />
              
              <FormField
                control={form.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Description</FormLabel>
                    <FormControl>
                      <Textarea {...field} placeholder="Description de la servitude" rows={3} />
                    </FormControl>
                  </FormItem>
                )}
              />
              
              <div className="flex justify-end gap-2 pt-4">
                <Button type="button" variant="outline" onClick={() => {
                  setShowAddForm(false);
                  setEditingServitude(null);
                  form.reset();
                }}>
                  Annuler
                </Button>
                <Button type="submit" disabled={addServitudeMutation.isPending || updateServitudeMutation.isPending}>
                  {editingServitude ? 'Mettre à jour' : 'Ajouter'}
                </Button>
              </div>
            </form>
          </Form>
        </SheetContent>
      </Sheet>
    </div>
  );
}
