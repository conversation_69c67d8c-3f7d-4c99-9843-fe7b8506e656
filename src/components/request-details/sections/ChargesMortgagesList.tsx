import { useState } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuSeparator, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { Sheet, SheetContent, SheetHeader, SheetTitle } from "@/components/ui/sheet";
import { Form, FormControl, FormField, FormItem, FormLabel } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Plus, MoreVertical, Edit, Trash, Calendar, DollarSign, Building, User } from "lucide-react";
import { useForm } from "react-hook-form";
import { cn } from "@/lib/utils";
import { supabase } from "@/lib/supabase";
import { toast } from "sonner";
import { Tables } from "@/integrations/supabase/types";

type ChargesMortgage = Tables<"charges_mortgages">;

type ChargeFormData = {
  request_id: string;
  charge_type: 'hypotheque' | 'privilege' | 'clause_resolutoire' | 'droit_preemption' | 'autre';
  creditor: string;
  debtor: string;
  amount: string;
  interest_rate: string;
  registration_number: string;
  registration_date: string;
  status: 'actif' | 'a_radier' | 'radie';
  radiation_number: string;
  radiation_date: string;
  notes: string;
  sort_order?: number | null;
};

// Mapping functions for enum conversion
const mapStatusToDb = (status: 'actif' | 'a_radier' | 'radie'): 'active' | 'to_be_radiated' | 'radiated' => {
  const mapping = {
    'actif': 'active' as const,
    'a_radier': 'to_be_radiated' as const,
    'radie': 'radiated' as const,
  };
  return mapping[status];
};

const mapStatusFromDb = (status: 'active' | 'to_be_radiated' | 'radiated'): 'actif' | 'a_radier' | 'radie' => {
  const mapping = {
    'active': 'actif' as const,
    'to_be_radiated': 'a_radier' as const,
    'radiated': 'radie' as const,
  };
  return mapping[status];
};

interface ChargesMortgagesListProps {
  requestId: string;
}

const CHARGE_TYPES = [
  { value: 'hypotheque', label: 'Hypothèque' },
  { value: 'privilege', label: 'Privilège' },
  { value: 'clause_resolutoire', label: 'Clause résolutoire' },
  { value: 'droit_preemption', label: 'Droit de préemption' },
  { value: 'autre', label: 'Autre' },
];

const STATUS_OPTIONS = [
  { value: 'actif', label: 'Actif', variant: 'default' as const },
  { value: 'a_radier', label: 'À radier', variant: 'secondary' as const },
  { value: 'radie', label: 'Radié', variant: 'outline' as const },
];

export function ChargesMortgagesList({ requestId }: ChargesMortgagesListProps) {
  const [showAddForm, setShowAddForm] = useState(false);
  const [editingCharge, setEditingCharge] = useState<ChargesMortgage | null>(null);
  const [typeFilter, setTypeFilter] = useState<string>('all');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  
  const queryClient = useQueryClient();

  const { data: charges, isLoading } = useQuery({
    queryKey: ['charges-mortgages', requestId],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('charges_mortgages')
        .select('*')
        .eq('request_id', requestId)
        .order('sort_order', { ascending: true, nullsFirst: false })
        .order('registration_date', { ascending: false });
      
      if (error) throw error;
      return data as ChargesMortgage[];
    },
  });

  const addChargeMutation = useMutation({
    mutationFn: async (chargeData: ChargeFormData) => {
      const submitData = {
        ...chargeData,
        amount: chargeData.amount ? parseFloat(chargeData.amount) : 0,
        interest_rate: chargeData.interest_rate ? parseFloat(chargeData.interest_rate) : null,
        status: mapStatusToDb(chargeData.status),
      };
      const { data, error } = await supabase
        .from('charges_mortgages')
        .insert([submitData])
        .select()
        .single();
      
      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['charges-mortgages', requestId] });
      setShowAddForm(false);
      toast.success('Charge ajoutée avec succès');
    },
    onError: (error) => {
      toast.error(`Erreur lors de l'ajout: ${error.message}`);
    },
  });

  const updateChargeMutation = useMutation({
    mutationFn: async ({ id, ...updateData }: ChargeFormData & { id: string }) => {
      const submitData = {
        ...updateData,
        amount: updateData.amount ? parseFloat(updateData.amount) : 0,
        interest_rate: updateData.interest_rate ? parseFloat(updateData.interest_rate) : null,
        status: mapStatusToDb(updateData.status),
      };
      const { data, error } = await supabase
        .from('charges_mortgages')
        .update(submitData)
        .eq('id', id)
        .select()
        .single();
      
      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['charges-mortgages', requestId] });
      setEditingCharge(null);
      toast.success('Charge mise à jour avec succès');
    },
    onError: (error) => {
      toast.error(`Erreur lors de la mise à jour: ${error.message}`);
    },
  });

  const deleteChargeMutation = useMutation({
    mutationFn: async (id: string) => {
      const { error } = await supabase
        .from('charges_mortgages')
        .delete()
        .eq('id', id);
      
      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['charges-mortgages', requestId] });
      toast.success('Charge supprimée avec succès');
    },
    onError: (error) => {
      toast.error(`Erreur lors de la suppression: ${error.message}`);
    },
  });

  const form = useForm<ChargeFormData>({
    defaultValues: {
      request_id: requestId,
      charge_type: 'hypotheque',
      creditor: '',
      debtor: '',
      amount: '',
      interest_rate: '',
      registration_number: '',
      registration_date: '',
      status: 'actif',
      radiation_number: '',
      radiation_date: '',
      notes: '',
    },
  });

  const filteredCharges = charges?.filter(charge => {
    if (typeFilter !== 'all' && charge.charge_type !== typeFilter) return false;
    if (statusFilter !== 'all' && charge.status !== statusFilter) return false;
    return true;
  }) || [];

  const getTypeLabel = (type: string) => {
    return CHARGE_TYPES.find(t => t.value === type)?.label || type;
  };

  const getStatusVariant = (status: string) => {
    return STATUS_OPTIONS.find(s => s.value === status)?.variant || 'outline';
  };

  const getStatusLabel = (status: string) => {
    return STATUS_OPTIONS.find(s => s.value === status)?.label || status;
  };

  const formatCurrency = (amount: number | null) => {
    if (!amount) return '0 $';
    return new Intl.NumberFormat('fr-CA', {
      style: 'currency',
      currency: 'CAD'
    }).format(amount);
  };

  const handleSubmit = (data: ChargeFormData) => {
    if (editingCharge) {
      updateChargeMutation.mutate({ ...data, id: editingCharge.id });
    } else {
      addChargeMutation.mutate(data);
    }
  };

  const handleEdit = (charge: ChargesMortgage) => {
    setEditingCharge(charge);
    form.reset({
      request_id: charge.request_id,
      charge_type: charge.charge_type,
      creditor: charge.creditor,
      debtor: charge.debtor,
      amount: charge.amount?.toString() || '',
      interest_rate: charge.interest_rate?.toString() || '',
      registration_number: charge.registration_number,
      registration_date: charge.registration_date,
      status: mapStatusFromDb(charge.status),
      radiation_number: charge.radiation_number || '',
      radiation_date: charge.radiation_date || '',
      notes: charge.notes || '',
      sort_order: charge.sort_order,
    });
  };

  const handleDelete = (id: string) => {
    if (confirm('Êtes-vous sûr de vouloir supprimer cette charge ?')) {
      deleteChargeMutation.mutate(id);
    }
  };

  if (isLoading) {
    return <div>Chargement...</div>;
  }

  return (
    <div className="space-y-4">
      {/* Filter bar */}
      <div className="flex gap-4 mb-4">
        <Select value={typeFilter} onValueChange={setTypeFilter}>
          <SelectTrigger className="w-48">
            <SelectValue placeholder="Type de charge" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">Toutes</SelectItem>
            {CHARGE_TYPES.map(type => (
              <SelectItem key={type.value} value={type.value}>
                {type.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        
        <Select value={statusFilter} onValueChange={setStatusFilter}>
          <SelectTrigger className="w-48">
            <SelectValue placeholder="Statut" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">Tous</SelectItem>
            {STATUS_OPTIONS.map(status => (
              <SelectItem key={status.value} value={status.value}>
                {status.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        
        <Button onClick={() => setShowAddForm(true)}>
          <Plus className="w-4 h-4 mr-2" />
          Ajouter une charge
        </Button>
      </div>
      
      {/* Charge cards */}
      {filteredCharges.map((charge) => (
        <Card key={charge.id} className={cn(
          "p-4",
          charge.status === 'radiated' && "opacity-60"
        )}>
          <div className="flex justify-between items-start">
            <div className="flex-1">
              <div className="flex items-center gap-2 mb-2">
                <Badge variant="outline">
                  {getTypeLabel(charge.charge_type)}
                </Badge>
                <Badge variant={getStatusVariant(charge.status)}>
                  {getStatusLabel(charge.status)}
                </Badge>
                <div className="flex items-center text-sm text-muted-foreground">
                  <Calendar className="w-4 h-4 mr-1" />
                  {new Date(charge.registration_date).toLocaleDateString('fr-CA')}
                </div>
              </div>
              
              <h4 className="font-semibold mb-2">
                No. {charge.registration_number}
              </h4>
              
              <div className="space-y-2 text-sm">
                <div className="flex items-center">
                  <Building className="w-4 h-4 mr-1" />
                  <span className="font-medium">Créancier:</span> 
                  <span className="ml-1">{charge.creditor}</span>
                </div>
                <div className="flex items-center">
                  <User className="w-4 h-4 mr-1" />
                  <span className="font-medium">Débiteur:</span> 
                  <span className="ml-1">{charge.debtor}</span>
                </div>
                <div className="flex items-center">
                  <DollarSign className="w-4 h-4 mr-1" />
                  <span className="font-medium">{formatCurrency(charge.amount)}</span>
                  {charge.interest_rate && (
                    <span className="ml-2 text-muted-foreground">
                      ({charge.interest_rate}% d'intérêt)
                    </span>
                  )}
                </div>
                
                {charge.status === 'radiated' && charge.radiation_number && (
                  <div className="text-muted-foreground">
                    <span className="font-medium">Radiation:</span> {charge.radiation_number}
                    {charge.radiation_date && (
                      <span className="ml-2">
                        le {new Date(charge.radiation_date).toLocaleDateString('fr-CA')}
                      </span>
                    )}
                  </div>
                )}
                
                {charge.notes && (
                  <p className="text-muted-foreground mt-2">
                    <span className="font-medium">Notes:</span> {charge.notes}
                  </p>
                )}
              </div>
            </div>
            
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="sm">
                  <MoreVertical className="w-4 h-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent>
                <DropdownMenuItem onClick={() => handleEdit(charge)}>
                  <Edit className="w-4 h-4 mr-2" />
                  Modifier
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem 
                  onClick={() => handleDelete(charge.id)}
                  className="text-red-600"
                >
                  <Trash className="w-4 h-4 mr-2" />
                  Supprimer
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </Card>
      ))}

      {/* Add/Edit Form */}
      <Sheet open={showAddForm || !!editingCharge} onOpenChange={(open) => {
        if (!open) {
          setShowAddForm(false);
          setEditingCharge(null);
          form.reset();
        }
      }}>
        <SheetContent className="w-[600px]">
          <SheetHeader>
            <SheetTitle>
              {editingCharge ? 'Modifier la charge' : 'Ajouter une charge'}
            </SheetTitle>
          </SheetHeader>
          
          <Form {...form}>
            <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4 mt-4">
              <FormField
                control={form.control}
                name="charge_type"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Type de charge</FormLabel>
                    <Select value={field.value} onValueChange={field.onChange}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Sélectionner un type" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {CHARGE_TYPES.map(type => (
                          <SelectItem key={type.value} value={type.value}>
                            {type.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </FormItem>
                )}
              />
              
              <FormField
                control={form.control}
                name="creditor"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Créancier</FormLabel>
                    <FormControl>
                      <Input {...field} placeholder="Nom du créancier" />
                    </FormControl>
                  </FormItem>
                )}
              />
              
              <FormField
                control={form.control}
                name="debtor"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Débiteur</FormLabel>
                    <FormControl>
                      <Input {...field} placeholder="Nom du débiteur" />
                    </FormControl>
                  </FormItem>
                )}
              />
              
              <FormField
                control={form.control}
                name="amount"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Montant</FormLabel>
                    <FormControl>
                      <Input {...field} type="number" step="0.01" placeholder="0.00" />
                    </FormControl>
                  </FormItem>
                )}
              />
              
              <FormField
                control={form.control}
                name="interest_rate"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Taux d'intérêt (%)</FormLabel>
                    <FormControl>
                      <Input {...field} type="number" step="0.01" placeholder="0.00" />
                    </FormControl>
                  </FormItem>
                )}
              />
              
              <FormField
                control={form.control}
                name="registration_number"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Numéro d'enregistrement</FormLabel>
                    <FormControl>
                      <Input {...field} placeholder="Numéro d'enregistrement" />
                    </FormControl>
                  </FormItem>
                )}
              />
              
              <FormField
                control={form.control}
                name="registration_date"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Date d'enregistrement</FormLabel>
                    <FormControl>
                      <Input {...field} type="date" />
                    </FormControl>
                  </FormItem>
                )}
              />
              
              <FormField
                control={form.control}
                name="status"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Statut</FormLabel>
                    <Select value={field.value} onValueChange={field.onChange}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Sélectionner un statut" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {STATUS_OPTIONS.map(status => (
                          <SelectItem key={status.value} value={status.value}>
                            {status.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </FormItem>
                )}
              />
              
              <FormField
                control={form.control}
                name="radiation_number"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Numéro de radiation</FormLabel>
                    <FormControl>
                      <Input {...field} placeholder="Numéro de radiation (optionnel)" />
                    </FormControl>
                  </FormItem>
                )}
              />
              
              <FormField
                control={form.control}
                name="radiation_date"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Date de radiation</FormLabel>
                    <FormControl>
                      <Input {...field} type="date" />
                    </FormControl>
                  </FormItem>
                )}
              />
              
              <FormField
                control={form.control}
                name="notes"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Notes</FormLabel>
                    <FormControl>
                      <Textarea {...field} placeholder="Notes supplémentaires" rows={3} />
                    </FormControl>
                  </FormItem>
                )}
              />
              
              <div className="flex justify-end gap-2 pt-4">
                <Button type="button" variant="outline" onClick={() => {
                  setShowAddForm(false);
                  setEditingCharge(null);
                  form.reset();
                }}>
                  Annuler
                </Button>
                <Button type="submit" disabled={addChargeMutation.isPending || updateChargeMutation.isPending}>
                  {editingCharge ? 'Mettre à jour' : 'Ajouter'}
                </Button>
              </div>
            </form>
          </Form>
        </SheetContent>
      </Sheet>
    </div>
  );
}
