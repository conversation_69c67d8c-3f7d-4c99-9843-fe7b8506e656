import { useState } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuSeparator, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { Sheet, SheetContent, SheetHeader, SheetTitle } from "@/components/ui/sheet";
import { Form, FormControl, FormField, FormItem, FormLabel } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Plus, MoreVertical, Edit, Trash, Calendar, DollarSign } from "lucide-react";
import { useForm } from "react-hook-form";
import { cn } from "@/lib/utils";
import { supabase } from "@/lib/supabase";
import { toast } from "sonner";
import { Tables } from "@/integrations/supabase/types";

type TitleChain = Tables<"title_chain">;

type TitleChainFormData = {
  request_id: string;
  transaction_date: string;
  transaction_type: 'vente' | 'donation' | 'succession' | 'saisie' | 'cession' | 'transmission' | 'autre';
  from_party: string;
  to_party: string;
  price: string;
  registration_number: string;
  notary: string;
  notes: string;
  sort_order?: number | null;
};

interface TitleChainListProps {
  requestId: string;
}

const TRANSACTION_TYPES = [
  { value: 'vente', label: 'Vente' },
  { value: 'donation', label: 'Donation' },
  { value: 'succession', label: 'Succession' },
  { value: 'saisie', label: 'Saisie' },
  { value: 'cession', label: 'Cession' },
  { value: 'transmission', label: 'Transmission' },
  { value: 'autre', label: 'Autre' },
];

export function TitleChainList({ requestId }: TitleChainListProps) {
  const [showAddForm, setShowAddForm] = useState(false);
  const [editingChain, setEditingChain] = useState<TitleChain | null>(null);
  const [transactionTypeFilter, setTransactionTypeFilter] = useState<string>('all');
  
  const queryClient = useQueryClient();

  const { data: titleChain, isLoading } = useQuery({
    queryKey: ['title-chain', requestId],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('title_chain')
        .select('*')
        .eq('request_id', requestId)
        .order('transaction_date', { ascending: false }) // Most recent first
        .order('sort_order', { ascending: true, nullsFirst: false });
      
      if (error) throw error;
      return data as TitleChain[];
    },
  });

  const addChainMutation = useMutation({
    mutationFn: async (chainData: TitleChainFormData) => {
      const submitData = {
        ...chainData,
        price: chainData.price ? parseFloat(chainData.price) : null,
      };
      const { data, error } = await supabase
        .from('title_chain')
        .insert([submitData])
        .select()
        .single();
      
      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['title-chain', requestId] });
      setShowAddForm(false);
      toast.success('Transaction ajoutée avec succès');
    },
    onError: (error) => {
      toast.error(`Erreur lors de l'ajout: ${error.message}`);
    },
  });

  const updateChainMutation = useMutation({
    mutationFn: async ({ id, ...updateData }: TitleChainFormData & { id: string }) => {
      const submitData = {
        ...updateData,
        price: updateData.price ? parseFloat(updateData.price) : null,
      };
      const { data, error } = await supabase
        .from('title_chain')
        .update(submitData)
        .eq('id', id)
        .select()
        .single();
      
      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['title-chain', requestId] });
      setEditingChain(null);
      toast.success('Transaction mise à jour avec succès');
    },
    onError: (error) => {
      toast.error(`Erreur lors de la mise à jour: ${error.message}`);
    },
  });

  const deleteChainMutation = useMutation({
    mutationFn: async (id: string) => {
      const { error } = await supabase
        .from('title_chain')
        .delete()
        .eq('id', id);
      
      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['title-chain', requestId] });
      toast.success('Transaction supprimée avec succès');
    },
    onError: (error) => {
      toast.error(`Erreur lors de la suppression: ${error.message}`);
    },
  });

  const form = useForm<TitleChainFormData>({
    defaultValues: {
      request_id: requestId,
      transaction_date: '',
      transaction_type: 'vente',
      from_party: '',
      to_party: '',
      price: '',
      registration_number: '',
      notary: '',
      notes: '',
    },
  });

  const filteredChain = titleChain?.filter(chain => {
    if (transactionTypeFilter !== 'all' && chain.transaction_type !== transactionTypeFilter) return false;
    return true;
  }) || [];

  const getTransactionTypeLabel = (type: string) => {
    return TRANSACTION_TYPES.find(t => t.value === type)?.label || type;
  };

  const formatPrice = (price: string | number | null) => {
    if (!price) return null;
    const numPrice = typeof price === 'string' ? parseFloat(price) : price;
    if (isNaN(numPrice)) return null;
    return new Intl.NumberFormat('fr-CA', {
      style: 'currency',
      currency: 'CAD'
    }).format(numPrice);
  };

  const handleSubmit = (data: TitleChainFormData) => {
    if (editingChain) {
      updateChainMutation.mutate({ ...data, id: editingChain.id });
    } else {
      addChainMutation.mutate(data);
    }
  };

  const handleEdit = (chain: TitleChain) => {
    setEditingChain(chain);
    form.reset({
      request_id: chain.request_id,
      transaction_date: chain.transaction_date,
      transaction_type: chain.transaction_type,
      from_party: chain.from_party,
      to_party: chain.to_party,
      price: chain.price?.toString() || '',
      registration_number: chain.registration_number,
      notary: chain.notary || '',
      notes: chain.notes || '',
      sort_order: chain.sort_order,
    });
  };

  const handleDelete = (id: string) => {
    if (confirm('Êtes-vous sûr de vouloir supprimer cette transaction ?')) {
      deleteChainMutation.mutate(id);
    }
  };

  if (isLoading) {
    return <div>Chargement...</div>;
  }

  return (
    <div className="space-y-4">
      {/* Filter bar */}
      <div className="flex gap-4 mb-4">
        <Select value={transactionTypeFilter} onValueChange={setTransactionTypeFilter}>
          <SelectTrigger className="w-48">
            <SelectValue placeholder="Type de transaction" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">Toutes</SelectItem>
            {TRANSACTION_TYPES.map(type => (
              <SelectItem key={type.value} value={type.value}>
                {type.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        
        <Button onClick={() => setShowAddForm(true)}>
          <Plus className="w-4 h-4 mr-2" />
          Ajouter une transaction
        </Button>
      </div>
      
      {/* Chain cards */}
      <div className="space-y-3">
        {filteredChain.map((chain, index) => (
          <Card key={chain.id} className="p-4 relative">
            {/* Timeline indicator */}
            <div className="absolute left-0 top-1/2 w-3 h-3 bg-primary rounded-full -translate-x-1/2 -translate-y-1/2 border-2 border-background"></div>
            {index < filteredChain.length - 1 && (
              <div className="absolute left-0 top-1/2 w-px h-full bg-border translate-y-1/2 -translate-x-px"></div>
            )}
            
            <div className="ml-6">
              <div className="flex justify-between items-start">
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-2">
                    <Badge variant="outline">
                      {getTransactionTypeLabel(chain.transaction_type)}
                    </Badge>
                    <div className="flex items-center text-sm text-muted-foreground">
                      <Calendar className="w-4 h-4 mr-1" />
                      {new Date(chain.transaction_date).toLocaleDateString('fr-CA')}
                    </div>
                  </div>
                  
                  <div className="space-y-2">
                    <div>
                      <span className="font-medium">De:</span> {chain.from_party}
                    </div>
                    <div>
                      <span className="font-medium">À:</span> {chain.to_party}
                    </div>
                    <div>
                      <span className="font-medium">No. d'enregistrement:</span> {chain.registration_number}
                    </div>
                    {chain.price && (
                      <div className="flex items-center">
                        <DollarSign className="w-4 h-4 mr-1" />
                        <span className="font-medium">{formatPrice(chain.price)}</span>
                      </div>
                    )}
                    {chain.notary && (
                      <div>
                        <span className="font-medium">Notaire:</span> {chain.notary}
                      </div>
                    )}
                    {chain.notes && (
                      <div className="text-sm text-muted-foreground">
                        <span className="font-medium">Notes:</span> {chain.notes}
                      </div>
                    )}
                  </div>
                </div>
                
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" size="sm">
                      <MoreVertical className="w-4 h-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent>
                    <DropdownMenuItem onClick={() => handleEdit(chain)}>
                      <Edit className="w-4 h-4 mr-2" />
                      Modifier
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem 
                      onClick={() => handleDelete(chain.id)}
                      className="text-red-600"
                    >
                      <Trash className="w-4 h-4 mr-2" />
                      Supprimer
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </div>
          </Card>
        ))}
      </div>

      {/* Add/Edit Form */}
      <Sheet open={showAddForm || !!editingChain} onOpenChange={(open) => {
        if (!open) {
          setShowAddForm(false);
          setEditingChain(null);
          form.reset();
        }
      }}>
        <SheetContent className="w-[600px]">
          <SheetHeader>
            <SheetTitle>
              {editingChain ? 'Modifier la transaction' : 'Ajouter une transaction'}
            </SheetTitle>
          </SheetHeader>
          
          <Form {...form}>
            <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4 mt-4">
              <FormField
                control={form.control}
                name="transaction_date"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Date de transaction</FormLabel>
                    <FormControl>
                      <Input {...field} type="date" />
                    </FormControl>
                  </FormItem>
                )}
              />
              
              <FormField
                control={form.control}
                name="transaction_type"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Type de transaction</FormLabel>
                    <Select value={field.value} onValueChange={field.onChange}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Sélectionner un type" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {TRANSACTION_TYPES.map(type => (
                          <SelectItem key={type.value} value={type.value}>
                            {type.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </FormItem>
                )}
              />
              
              <FormField
                control={form.control}
                name="from_party"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Cédant (De)</FormLabel>
                    <FormControl>
                      <Input {...field} placeholder="Nom du vendeur/cédant" />
                    </FormControl>
                  </FormItem>
                )}
              />
              
              <FormField
                control={form.control}
                name="to_party"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Cessionnaire (À)</FormLabel>
                    <FormControl>
                      <Input {...field} placeholder="Nom de l'acheteur/cessionnaire" />
                    </FormControl>
                  </FormItem>
                )}
              />
              
              <FormField
                control={form.control}
                name="registration_number"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Numéro d'enregistrement</FormLabel>
                    <FormControl>
                      <Input {...field} placeholder="Numéro d'enregistrement" />
                    </FormControl>
                  </FormItem>
                )}
              />
              
              <FormField
                control={form.control}
                name="price"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Prix</FormLabel>
                    <FormControl>
                      <Input {...field} type="number" step="0.01" placeholder="0.00" />
                    </FormControl>
                  </FormItem>
                )}
              />
              
              <FormField
                control={form.control}
                name="notary"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Notaire</FormLabel>
                    <FormControl>
                      <Input {...field} placeholder="Nom du notaire" />
                    </FormControl>
                  </FormItem>
                )}
              />
              
              <FormField
                control={form.control}
                name="notes"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Notes</FormLabel>
                    <FormControl>
                      <Textarea {...field} placeholder="Notes supplémentaires" rows={3} />
                    </FormControl>
                  </FormItem>
                )}
              />
              
              <div className="flex justify-end gap-2 pt-4">
                <Button type="button" variant="outline" onClick={() => {
                  setShowAddForm(false);
                  setEditingChain(null);
                  form.reset();
                }}>
                  Annuler
                </Button>
                <Button type="submit" disabled={addChainMutation.isPending || updateChainMutation.isPending}>
                  {editingChain ? 'Mettre à jour' : 'Ajouter'}
                </Button>
              </div>
            </form>
          </Form>
        </SheetContent>
      </Sheet>
    </div>
  );
}
