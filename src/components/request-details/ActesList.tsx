
import React, { useState } from 'react';
import { format } from 'date-fns';
import { fr } from 'date-fns/locale';
import { ArrowDown, FileText, AlertTriangle, Eye, Download } from 'lucide-react';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Button } from '@/components/ui/button';
import ActeDetailModal from './ActeDetailModal';
import PdfViewerModal from '@/components/ui/pdf-viewer-modal';
import { downloadSingleDocument } from '@/lib/document-utils';

interface ActesListProps {
  actes: any[];
}

const ActesList: React.FC<ActesListProps> = ({ actes }) => {
  const [selectedActe, setSelectedActe] = useState<any>(null);
  const [isActeModalOpen, setIsActeModalOpen] = useState(false);
  const [isPdfModalOpen, setIsPdfModalOpen] = useState(false);
  const [selectedPdfActe, setSelectedPdfActe] = useState<any>(null);
  
  const handleActeClick = (acte: any) => {
    setSelectedActe(acte);
    setIsActeModalOpen(true);
  };

  const handleViewPdf = (e: React.MouseEvent, acte: any) => {
    e.stopPropagation();
    setSelectedPdfActe(acte);
    setIsPdfModalOpen(true);
  };

  const handleDownloadPdf = (e: React.MouseEvent, acte: any) => {
    e.stopPropagation();
    if (acte.doc_url) {
      downloadSingleDocument(acte.doc_url, `Acte_${acte.acte_publication_number || acte.id}`);
    }
  };

  const formatDate = (dateString: string) => {
    try {
      return format(new Date(dateString), 'PPP', { locale: fr });
    } catch (error) {
      return 'Date invalide';
    }
  };

  // Helper function to determine status badge styling
  const getStatusBadgeClass = (status: string) => {
    const lowerStatus = status?.toLowerCase() || '';
    
    if (lowerStatus.includes('completed') || lowerStatus.includes('analysis completed') || lowerStatus.includes('analyse_terminee')) {
      return "bg-green-100 text-green-800 border-green-200";
    } else if (lowerStatus.includes('progress') || lowerStatus.includes('analysis in progress')) {
      return "bg-blue-100 text-blue-800 border-blue-200";
    } else if (lowerStatus.includes('error') || lowerStatus.includes('not available')) {
      return "bg-red-100 text-red-800 border-red-200";
    } else if (lowerStatus.includes('pending') || lowerStatus.includes('ready for analysis')) {
      return "bg-yellow-100 text-yellow-800 border-yellow-200";
    } else {
      return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };

  // Function to translate status to French
  const translateStatusToFrench = (status: string): string => {
    if (!status) return '';
    
    const statusMap: {[key: string]: string} = {
      'Pending': 'En attente',
      'Ready for Analysis': 'Prêt à l\'analyse',
      'Analysis in Progress': 'Analyse en cours',
      'Analysis Completed': 'Complété',
      'Analyse_terminee': 'Complété',
      'Document not Available': 'Document non trouvé'
    };
    
    return statusMap[status] || status;
  };

  if (!actes || actes.length === 0) {
    return (
      <div className="py-4 text-center text-muted-foreground text-sm">
        Aucune inscription trouvée pour cet index.
      </div>
    );
  }

  return (
    <>
      <ScrollArea className="h-[400px] w-full">
        <div className="space-y-3 py-2">
          {actes.map((acte, acteIdx) => (
            <div 
              key={acte.id} 
              className="border rounded-md p-4 relative overflow-hidden hover:shadow-md transition-shadow cursor-pointer"
              onClick={(e) => {
                e.stopPropagation();
                handleActeClick(acte);
              }}
            >
              <div className="absolute left-0 top-0 h-full w-1 bg-primary/50"></div>
              
              <div className="flex justify-between items-start">
                <div>
                  <div className="flex items-center gap-2 mb-2">
                    <span className="font-medium">
                      Inscription {acteIdx + 1}{acte.acte_publication_number ? ` - ${acte.acte_publication_number}` : ''}
                    </span>
                    <span className="text-xs px-2 py-0.5 rounded-full bg-muted text-muted-foreground">
                      {acte.acte_nature || 'Autre'}
                    </span>
                    {acte.doc_url && (
                      <span className="text-primary" title="Document disponible">
                        <FileText className="h-3.5 w-3.5" />
                      </span>
                    )}
                    {acte.radiation_number && (
                      <span className="flex items-center gap-1 text-xs text-destructive">
                        <AlertTriangle className="h-3.5 w-3.5 text-red-500" />
                        <span>#{acte.radiation_number}</span>
                      </span>
                    )}
                  </div>
                  
                  <p className="text-sm mb-2">{acte.relevance_explanation || 'Aucune explication de pertinence'}</p>
                  
                  {acte.acte_publication_date && (
                    <p className="text-xs text-muted-foreground">
                      Date: {formatDate(acte.acte_publication_date)}
                    </p>
                  )}
                </div>
                
                <div className="flex flex-col items-end gap-2">
                  <div className="flex items-center gap-2">
                    {acte.status && (
                      <span className={`text-xs px-2.5 py-0.5 rounded-full border ${getStatusBadgeClass(acte.status)} whitespace-nowrap`}>
                        {translateStatusToFrench(acte.status)}
                      </span>
                    )}
                    
                    {/* View and Download buttons */}
                    {acte.doc_url && (
                      <div className="flex items-center gap-1">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={(e) => handleViewPdf(e, acte)}
                          className="h-6 px-2 text-xs"
                        >
                          <Eye className="h-3 w-3 mr-1" />
                          Voir
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={(e) => handleDownloadPdf(e, acte)}
                          className="h-6 px-2 text-xs"
                        >
                          <Download className="h-3 w-3 mr-1" />
                          Télécharger
                        </Button>
                      </div>
                    )}
                  </div>
                </div>
                
                {acteIdx !== actes.length - 1 && (
                  <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 translate-y-1/2">
                    <ArrowDown className="h-4 w-4 text-muted-foreground" />
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
      </ScrollArea>
      
      <ActeDetailModal 
        acte={selectedActe} 
        open={isActeModalOpen} 
        onOpenChange={setIsActeModalOpen} 
      />
      
      {selectedPdfActe && selectedPdfActe.doc_url && (
        <PdfViewerModal
          open={isPdfModalOpen}
          onOpenChange={setIsPdfModalOpen}
          pdfUrl={selectedPdfActe.doc_url}
          title={`Acte ${selectedPdfActe.acte_publication_number || selectedPdfActe.id}`}
          onDownload={() => handleDownloadPdf({ stopPropagation: () => {} } as React.MouseEvent, selectedPdfActe)}
        />
      )}
    </>
  );
};

export default ActesList;
