import * as React from "react"
import { cva, type VariantProps } from "class-variance-authority"
import { cn } from "@/lib/utils"

const badgeVariants = cva(
  "inline-flex items-center rounded-md border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",
  {
    variants: {
      variant: {
        default:
          "border-transparent bg-[#e9d8ff] text-[#6941c6] shadow hover:bg-[#e0c6ff]",
        secondary:
          "border-transparent bg-[#f2f4f7] text-[#667085] hover:bg-[#e4e7ec]",
        destructive:
          "border-transparent bg-[#fee4e2] text-[#b42318] shadow hover:bg-[#fecdca]",
        outline: "text-foreground",
        success: 
          "border-transparent bg-[#e6f9eb] text-[#027a48] shadow hover:bg-[#d1f7db]",
        warning:
          "border-transparent bg-[#fef0c7] text-[#b54708] shadow hover:bg-[#fedf89]",
        info:
          "border-transparent bg-[#d1e9ff] text-[#175cd3] shadow hover:bg-[#b2ddff]",
        // Status-specific variants with distinct, beautiful colors
        "pending":
          "border-transparent bg-[#f2f4f7] text-[#667085] shadow hover:bg-[#e4e7ec]",
        "in-progress":
          "border-transparent bg-[#d1e9ff] text-[#175cd3] shadow hover:bg-[#b2ddff]",
        "phase1":
          "border-transparent bg-[#ede9fe] text-[#7e69ab] shadow hover:bg-[#e4dcfd]",
        "phase2":
          "border-transparent bg-[#e2d9fb] text-[#6e59a5] shadow hover:bg-[#d7cbfa]",
        "phase3":
          "border-transparent bg-[#d8d1f8] text-[#534687] shadow hover:bg-[#cbc2f6]",
        "phase4":
          "border-transparent bg-[#c9bef5] text-[#463973] shadow hover:bg-[#beb3f3]",
        "completed":
          "border-transparent bg-[#e6f9eb] text-[#027a48] shadow hover:bg-[#d1f7db]",
        "finalizing":
          "border-transparent bg-[#fef0c7] text-[#b54708] shadow hover:bg-[#fedf89]",
        "error":
          "border-transparent bg-[#fee4e2] text-[#b42318] shadow hover:bg-[#fecdca]",
        // Keep existing inscription type variants
        "inscription-emphyteose":
          "border-transparent bg-[#e5efed] text-[#3e6859] shadow hover:bg-[#d8e6e2]",
        "inscription-bail":
          "border-transparent bg-[#f0e6d3] text-[#8b6e3d] shadow hover:bg-[#e8dbc3]",
        "inscription-cession":
          "border-transparent bg-[#e8e5f0] text-[#5d567b] shadow hover:bg-[#dbd7e8]",
        "inscription-radiation":
          "border-transparent bg-[#f9e5e5] text-[#b45252] shadow hover:bg-[#f3d9d9]",
      },
    },
    defaultVariants: {
      variant: "default",
    },
  }
)

export interface BadgeProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof badgeVariants> {}

function Badge({ className, variant, ...props }: BadgeProps) {
  return (
    <div className={cn(badgeVariants({ variant }), className)} {...props} />
  )
}

export { Badge, badgeVariants }
