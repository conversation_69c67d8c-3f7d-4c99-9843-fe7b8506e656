import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Download, X } from 'lucide-react';
import { processGoogleDriveUrl, downloadSingleDocument } from '@/lib/document-utils';

interface PdfViewerModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  pdfUrl: string;
  title?: string;
  onDownload?: () => void;
}

const PdfViewerModal: React.FC<PdfViewerModalProps> = ({
  open,
  onOpenChange,
  pdfUrl,
  title = 'Visionneuse PDF',
  onDownload
}) => {
  const [processedUrl, setProcessedUrl] = useState<string>('');

  useEffect(() => {
    if (pdfUrl) {
      // Process the URL to handle Google Drive URLs properly
      const processed = processGoogleDriveUrl(pdfUrl);
      setProcessedUrl(processed);
    }
  }, [pdfUrl]);

  const handleDownload = () => {
    if (onDownload) {
      onDownload();
    } else {
      // Use the proven download function
      downloadSingleDocument(pdfUrl, title || 'document');
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-5xl max-h-[90vh] p-0">
        <DialogHeader className="p-6 pb-4 border-b">
          <div className="flex items-center justify-between">
            <DialogTitle className="text-xl font-semibold">
              {title}
            </DialogTitle>
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={handleDownload}
                className="flex items-center space-x-2"
              >
                <Download className="h-4 w-4" />
                <span>Télécharger</span>
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => onOpenChange(false)}
                className="h-8 w-8 p-0"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </DialogHeader>
        
        <div className="flex-1 p-6 pt-4">
          <div className="w-full h-[calc(90vh-140px)] border rounded-lg overflow-hidden">
            <iframe
              src={processedUrl}
              className="w-full h-full border-0"
              title={title}
              style={{ minHeight: '600px' }}
            />
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default PdfViewerModal;
