import { useMemo } from 'react';

interface UseMermaidGraphProps {
  request: any;
  indexEntries: any[];
  actesByIndex: { [key: string]: any[] };
}

export const useMermaidGraph = ({ request, indexEntries, actesByIndex }: UseMermaidGraphProps) => {
  // Helper to get status class for Mermaid nodes
  const getMermaidStatusClass = (status: string) => {
    switch (status?.toLowerCase()) {
      case 'pending':
      case 'in progress':
      case 'inprogress': return 'inprogress';
      case 'completed': return 'completed';
      case 'error':
      case 'not available': return 'error';
      default: return 'inprogress'; // Default to in progress if status is unknown
    }
  };

  // Function to generate the Mermaid graph definition
  const generateMermaidGraph = (request: any, indexEntries: any[], actesByIndex: { [key: string]: any[] }) => {
    let graph = 'graph TD\n';

    // Enhanced CSS classes for better visual styling
    graph += `classDef demand fill:#F59E0B,stroke:#D97706,color:#FFFFFF,stroke-width:3px,rx:10,ry:10\n`; // Amber for demand
    graph += `classDef inprogress fill:#3B82F6,stroke:#2563EB,color:#FFFFFF,stroke-width:2px,rx:8,ry:8\n`; // Blue for in progress
    graph += `classDef completed fill:#10B981,stroke:#059669,color:#FFFFFF,stroke-width:2px,rx:8,ry:8\n`; // Emerald for completed
    graph += `classDef error fill:#EF4444,stroke:#DC2626,color:#FFFFFF,stroke-width:2px,rx:8,ry:8\n`; // Red for error
    graph += `classDef radiatedNode fill:#F87171,stroke:#EF4444,color:#FFFFFF,stroke-width:3px,stroke-dasharray:8 4\n`; // Light red dashed for radiated

    // 1. User Input / Demande Node with professional styling
    graph += `Request([Demande]):::demand\n`;

    // 2. Index Nodes - first create all nodes
    indexEntries.forEach(index => {
      // Derive status from available phase completion fields
      const indexStatus = (index.phase_1_completed && index.phase_3_completed) ? 'completed' : 
                         index.phase_1_completed ? 'inprogress' : 
                         index.phase_1_status === 'En_attente' ? 'pending' : 'inprogress';
      const indexStatusText = indexStatus === 'completed' ? 'Complété' : (indexStatus === 'inprogress' ? 'En cours' : indexStatus);
      const indexStatusClass = getMermaidStatusClass(indexStatus);

      // Professional node content without emojis and proper line breaks
      const nodeLabel = `Index ${index.lot_number || index.doc_number || index.id.substring(0, 8)}`;
      const nodeContent = `${nodeLabel}<br/>${indexStatusText}`;

      graph += `I${index.id}((${nodeContent})):::${indexStatusClass}\n`;
    });

    // 3. Index connections based on source_id
    indexEntries.forEach(index => {
      if (index.source_id && index.source_id.trim() !== '') {
        // Connect to parent index if source_id exists
        graph += `I${index.source_id} --> I${index.id}\n`;
      } else {
        // Connect to request if no source_id (null/empty)
        graph += `Request --> I${index.id}\n`;
      }
    });

    // 4. Acte Nodes and their connections to Index
    Object.values(actesByIndex).flat().forEach(acte => {
      const acteStatus = acte.status || 'completed';
      // Fix status mapping for Acte nodes - ensure "Analysis Completed" and "Analyse_terminee" map to "Complété"
      let acteStatusText = acteStatus;
      if (acteStatus === 'completed' || acteStatus === 'Analysis Completed' || acteStatus === 'Analyse_terminee') {
        acteStatusText = 'Complété';
      } else if (acteStatus === 'inprogress' || acteStatus === 'in progress' || acteStatus === 'Analysis In Progress') {
        acteStatusText = 'En cours';
      }
      const acteStatusClass = getMermaidStatusClass(acteStatus === 'Analysis Completed' || acteStatus === 'Analyse_terminee' ? 'completed' : acteStatus);

      // Professional node content without emojis and proper line breaks
      const nodeLabel = `${acte.acte_type || 'Acte'} ${acte.acte_publication_number || acte.doc_number || acte.id.substring(0, 8)}`;
      const nodeContent = `${nodeLabel}<br/>${acteStatusText}`;

      graph += `A${acte.id}(${nodeContent}):::${acteStatusClass}\n`;

      // Connection from parent Index to Acte without labels
      if (acte.index_id) {
        graph += `I${acte.index_id} --> A${acte.id}\n`;
      }

      // 5. Radiated Actes (if applicable) - Fixed syntax for diamond shape
      if (acte.is_radiated) {
        graph += `Rad${acte.id}{Radié}:::radiatedNode\n`;
        graph += `A${acte.id} -.-> Rad${acte.id}\n`;
      }
    });

    return graph;
  };

  const mermaidDefinition = useMemo(() => {
    if (request && indexEntries && actesByIndex) {
      return generateMermaidGraph(request, indexEntries, actesByIndex);
    }
    return '';
  }, [request, indexEntries, actesByIndex]);

  // Handle Mermaid node clicks
  const handleMermaidNodeClick = (nodeId: string, nodeType: 'request' | 'index' | 'acte' | 'radiated') => {
    if (nodeType === 'index') {
      // Find the index by ID
      const index = indexEntries.find(idx => `I${idx.id}` === nodeId);
      return { type: 'index', data: index };
    } else if (nodeType === 'acte') {
      // Find the acte by ID
      const allActes = Object.values(actesByIndex).flat();
      const acte = allActes.find(act => `A${act.id}` === nodeId);
      return { type: 'acte', data: acte };
    }
    // Note: request and radiated nodes don't have specific modals yet
    return null;
  };

  return {
    mermaidDefinition,
    handleMermaidNodeClick
  };
};
