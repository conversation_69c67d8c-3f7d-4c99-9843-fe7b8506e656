export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  public: {
    Tables: {
      acte_types: {
        Row: {
          acte_type: string
          category: string
          edge_cases: string | null
          extraction_analysis: string | null
          id: number
        }
        Insert: {
          acte_type: string
          category: string
          edge_cases?: string | null
          extraction_analysis?: string | null
          id?: number
        }
        Update: {
          acte_type?: string
          category?: string
          edge_cases?: string | null
          extraction_analysis?: string | null
          id?: number
        }
        Relationships: []
      }
      actes: {
        Row: {
          acte_details: string | null
          acte_nature: string | null
          acte_notary_minute: string | null
          acte_parties: string | null
          acte_publication_date: string | null
          acte_publication_number: string | null
          acte_summary: string | null
          circonscription_fonciere: string | null
          created_at: string | null
          doc_id: string | null
          doc_number: number | null
          doc_url: string | null
          document_ready: boolean
          file_content: string | null
          is_completed: boolean | null
          id: string
          index_id: string | null
          is_radiated: boolean | null
          matrimonie: string | null
          other_details: Json | null
          radiation_number: string | null
          relevance_explanation: string | null
          relevance_rating: number | null
          request_id: string
          source_id: string | null
          source_type: string | null
          status: Database["public"]["Enums"]["acte_status_enum"]
          updated_at: string | null
          writting: string | null
        }
        Insert: {
          acte_details?: string | null
          acte_nature?: string | null
          acte_notary_minute?: string | null
          acte_parties?: string | null
          acte_publication_date?: string | null
          acte_publication_number?: string | null
          acte_summary?: string | null
          circonscription_fonciere?: string | null
          created_at?: string | null
          doc_id?: string | null
          doc_number?: number | null
          doc_url?: string | null
          document_ready?: boolean
          file_content?: string | null
          id?: string
          index_id?: string | null
          is_completed?: boolean | null
          is_radiated?: boolean | null
          matrimonie?: string | null
          other_details?: Json | null
          radiation_number?: string | null
          relevance_explanation?: string | null
          relevance_rating?: number | null
          request_id: string
          source_id?: string | null
          source_type?: string | null
          status?: Database["public"]["Enums"]["acte_status_enum"]
          updated_at?: string | null
          writting?: string | null
        }
        Update: {
          acte_details?: string | null
          acte_nature?: string | null
          acte_notary_minute?: string | null
          acte_parties?: string | null
          acte_publication_date?: string | null
          acte_publication_number?: string | null
          acte_summary?: string | null
          circonscription_fonciere?: string | null
          created_at?: string | null
          doc_id?: string | null
          doc_number?: number | null
          doc_url?: string | null
          document_ready?: boolean
          file_content?: string | null
          is_completed?: boolean | null
          id?: string
          index_id?: string | null
          is_radiated?: boolean | null
          matrimonie?: string | null
          other_details?: Json | null
          radiation_number?: string | null
          relevance_explanation?: string | null
          relevance_rating?: number | null
          request_id?: string
          source_id?: string | null
          source_type?: string | null
          status?: Database["public"]["Enums"]["acte_status_enum"]
          updated_at?: string | null
          writting?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "actes_index_id_fkey"
            columns: ["index_id"]
            isOneToOne: false
            referencedRelation: "index"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "actes_request_id_fkey"
            columns: ["request_id"]
            isOneToOne: false
            referencedRelation: "requests"
            referencedColumns: ["id"]
          },
        ]
      }
      actes_documents: {
        Row: {
          acte_publication_number: string
          acte_type: number | null
          alternative_names: Json | null
          created_at: string
          doc_summary: string | null
          document_id: string | null
          document_url: string | null
          file_content: string | null
          publication_date: string | null
        }
        Insert: {
          acte_publication_number: string
          acte_type?: number | null
          alternative_names?: Json | null
          created_at?: string
          doc_summary?: string | null
          document_id?: string | null
          document_url?: string | null
          file_content?: string | null
          publication_date?: string | null
        }
        Update: {
          acte_publication_number?: string
          acte_type?: number | null
          alternative_names?: Json | null
          created_at?: string
          doc_summary?: string | null
          document_id?: string | null
          document_url?: string | null
          file_content?: string | null
          publication_date?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "actes_documents_acte_type_fkey"
            columns: ["acte_type"]
            isOneToOne: false
            referencedRelation: "acte_types"
            referencedColumns: ["id"]
          },
        ]
      }
      charges_mortgages: {
        Row: {
          amount: number | null
          charge_type: Database["public"]["Enums"]["charge_type_enum"]
          created_at: string | null
          creditor: string
          debtor: string
          id: string
          interest_rate: number | null
          notes: string | null
          radiation_date: string | null
          radiation_number: string | null
          registration_date: string
          registration_number: string
          request_id: string
          sort_order: number | null
          status: Database["public"]["Enums"]["charge_status_enum"]
          updated_at: string | null
        }
        Insert: {
          amount?: number | null
          charge_type: Database["public"]["Enums"]["charge_type_enum"]
          created_at?: string | null
          creditor: string
          debtor: string
          id?: string
          interest_rate?: number | null
          notes?: string | null
          radiation_date?: string | null
          radiation_number?: string | null
          registration_date: string
          registration_number: string
          request_id: string
          sort_order?: number | null
          status?: Database["public"]["Enums"]["charge_status_enum"]
          updated_at?: string | null
        }
        Update: {
          amount?: number | null
          charge_type?: Database["public"]["Enums"]["charge_type_enum"]
          created_at?: string | null
          creditor?: string
          debtor?: string
          id?: string
          interest_rate?: number | null
          notes?: string | null
          radiation_date?: string | null
          radiation_number?: string | null
          registration_date?: string
          registration_number?: string
          request_id?: string
          sort_order?: number | null
          status?: Database["public"]["Enums"]["charge_status_enum"]
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "charges_mortgages_request_id_fkey"
            columns: ["request_id"]
            isOneToOne: false
            referencedRelation: "requests"
            referencedColumns: ["id"]
          },
        ]
      }
      chat_sessions: {
        Row: {
          created_at: string
          id: string
          metadata: Json | null
          updated_at: string
          user_id: string | null
        }
        Insert: {
          created_at?: string
          id?: string
          metadata?: Json | null
          updated_at?: string
          user_id?: string | null
        }
        Update: {
          created_at?: string
          id?: string
          metadata?: Json | null
          updated_at?: string
          user_id?: string | null
        }
        Relationships: []
      }
      error_log: {
        Row: {
          created_at: string
          error: string | null
          flow_name: string | null
          id: number
        }
        Insert: {
          created_at?: string
          error?: string | null
          flow_name?: string | null
          id?: number
        }
        Update: {
          created_at?: string
          error?: string | null
          flow_name?: string | null
          id?: number
        }
        Relationships: []
      }
      extraction_queue: {
        Row: {
          acte_id: string | null
          acte_type: Database["public"]["Enums"]["acte_type_enum"] | null
          cadastre: string | null
          circonscription_fonciere: string | null
          created_at: string
          designation_secondaire: string | null
          doc_id: string | null
          doc_url: string | null
          document_number: string
          document_source: Database["public"]["Enums"]["document_source_enum"]
          error_message: string | null
          file_content: string | null
          id: string
          index_id: string | null
          local_file_path: string | null
          logged: boolean
          processing_started_at: string | null
          request_id: string | null
          retry_count: number
          status: Database["public"]["Enums"]["extraction_status_enum"]
          updated_at: string
        }
        Insert: {
          acte_id?: string | null
          acte_type?: Database["public"]["Enums"]["acte_type_enum"] | null
          cadastre?: string | null
          circonscription_fonciere?: string | null
          created_at?: string
          designation_secondaire?: string | null
          doc_id?: string | null
          doc_url?: string | null
          document_number: string
          document_source: Database["public"]["Enums"]["document_source_enum"]
          error_message?: string | null
          file_content?: string | null
          id?: string
          index_id?: string | null
          local_file_path?: string | null
          logged?: boolean
          processing_started_at?: string | null
          request_id?: string | null
          retry_count?: number
          status?: Database["public"]["Enums"]["extraction_status_enum"]
          updated_at?: string
        }
        Update: {
          acte_id?: string | null
          acte_type?: Database["public"]["Enums"]["acte_type_enum"] | null
          cadastre?: string | null
          circonscription_fonciere?: string | null
          created_at?: string
          designation_secondaire?: string | null
          doc_id?: string | null
          doc_url?: string | null
          document_number?: string
          document_source?: Database["public"]["Enums"]["document_source_enum"]
          error_message?: string | null
          file_content?: string | null
          id?: string
          index_id?: string | null
          local_file_path?: string | null
          logged?: boolean
          processing_started_at?: string | null
          request_id?: string | null
          retry_count?: number
          status?: Database["public"]["Enums"]["extraction_status_enum"]
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "extraction_queue_acte_id_fkey"
            columns: ["acte_id"]
            isOneToOne: false
            referencedRelation: "actes"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "extraction_queue_index_id_fkey"
            columns: ["index_id"]
            isOneToOne: false
            referencedRelation: "index"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "extraction_queue_request_id_fkey"
            columns: ["request_id"]
            isOneToOne: false
            referencedRelation: "requests"
            referencedColumns: ["id"]
          },
        ]
      }
      index: {
        Row: {
          cadastre: string | null
          circonscription: string | null
          created_at: string | null
          designation_secondaire: string | null
          doc_id: string | null
          doc_number: number | null
          doc_url: string | null
          document_ready: boolean
          file_content: string | null
          id: string
          index_initial: boolean
          index_summary: string | null
          lot_number: string | null
          phase_1_completed: boolean
          phase_1_status: Database["public"]["Enums"]["index_phase_status_enum"]
          phase_3_completed: boolean
          related_actes: number
          relevance_explanation: string | null
          relevance_rating: number | null
          request_id: string
          relevant: boolean | null
          source_id: string | null
          updated_at: string | null
        }
        Insert: {
          cadastre?: string | null
          circonscription?: string | null
          created_at?: string | null
          designation_secondaire?: string | null
          doc_id?: string | null
          doc_number?: number | null
          doc_url?: string | null
          document_ready?: boolean
          file_content?: string | null
          id?: string
          index_initial?: boolean
          index_summary?: string | null
          lot_number?: string | null
          phase_1_completed?: boolean
          phase_1_status?: Database["public"]["Enums"]["index_phase_status_enum"]
          phase_3_completed?: boolean
          related_actes?: number
          relevance_explanation?: string | null
          relevance_rating?: number | null
          request_id: string
          relevant?: boolean | null
          source_id?: string | null
          updated_at?: string | null
        }
        Update: {
          cadastre?: string | null
          circonscription?: string | null
          created_at?: string | null
          designation_secondaire?: string | null
          doc_id?: string | null
          doc_number?: number | null
          doc_url?: string | null
          document_ready?: boolean
          file_content?: string | null
          id?: string
          index_initial?: boolean
          index_summary?: string | null
          lot_number?: string | null
          phase_1_completed?: boolean
          phase_1_status?: Database["public"]["Enums"]["index_phase_status_enum"]
          phase_3_completed?: boolean
          related_actes?: number
          relevance_explanation?: string | null
          relevance_rating?: number | null
          request_id?: string
          relevant?: boolean | null
          source_id?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "index_request_id_fkey"
            columns: ["request_id"]
            isOneToOne: false
            referencedRelation: "requests"
            referencedColumns: ["id"]
          },
        ]
      }
      matrimonial_regimes: {
        Row: {
          contract_details: string | null
          created_at: string | null
          divorce_date: string | null
          id: string
          impact_on_title: string
          marriage_date: string | null
          person_name: string
          regime_type: Database["public"]["Enums"]["regime_type_enum"]
          request_id: string
          sort_order: number | null
          spouse_name: string | null
          updated_at: string | null
        }
        Insert: {
          contract_details?: string | null
          created_at?: string | null
          divorce_date?: string | null
          id?: string
          impact_on_title: string
          marriage_date?: string | null
          person_name: string
          regime_type: Database["public"]["Enums"]["regime_type_enum"]
          request_id: string
          sort_order?: number | null
          spouse_name?: string | null
          updated_at?: string | null
        }
        Update: {
          contract_details?: string | null
          created_at?: string | null
          divorce_date?: string | null
          id?: string
          impact_on_title?: string
          marriage_date?: string | null
          person_name?: string
          regime_type?: Database["public"]["Enums"]["regime_type_enum"]
          request_id?: string
          sort_order?: number | null
          spouse_name?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "matrimonial_regimes_request_id_fkey"
            columns: ["request_id"]
            isOneToOne: false
            referencedRelation: "requests"
            referencedColumns: ["id"]
          },
        ]
      }
      messages: {
        Row: {
          chat_session_id: string
          content: string
          created_at: string
          id: string
          metadata: Json | null
          request_id: string | null
          sender: string
          user_id: string | null
        }
        Insert: {
          chat_session_id: string
          content: string
          created_at?: string
          id?: string
          metadata?: Json | null
          request_id?: string | null
          sender: string
          user_id?: string | null
        }
        Update: {
          chat_session_id?: string
          content?: string
          created_at?: string
          id?: string
          metadata?: Json | null
          request_id?: string | null
          sender?: string
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "messages_chat_session_id_fkey"
            columns: ["chat_session_id"]
            isOneToOne: false
            referencedRelation: "chat_sessions"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "messages_request_id_fkey"
            columns: ["request_id"]
            isOneToOne: false
            referencedRelation: "requests"
            referencedColumns: ["id"]
          },
        ]
      }
      notifications: {
        Row: {
          content: string
          created_at: string | null
          id: string
          is_read: boolean | null
          request_id: string | null
          title: string
          updated_at: string | null
          user_id: string
        }
        Insert: {
          content: string
          created_at?: string | null
          id?: string
          is_read?: boolean | null
          request_id?: string | null
          title: string
          updated_at?: string | null
          user_id: string
        }
        Update: {
          content?: string
          created_at?: string | null
          id?: string
          is_read?: boolean | null
          request_id?: string | null
          title?: string
          updated_at?: string | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "notifications_request_id_fkey"
            columns: ["request_id"]
            isOneToOne: false
            referencedRelation: "requests"
            referencedColumns: ["id"]
          },
        ]
      }
      other_considerations: {
        Row: {
          action_taken: string | null
          category: Database["public"]["Enums"]["consideration_category_enum"]
          created_at: string | null
          description: string
          id: string
          importance: Database["public"]["Enums"]["importance_enum"]
          request_id: string
          requires_action: boolean
          sort_order: number | null
          title: string
          updated_at: string | null
        }
        Insert: {
          action_taken?: string | null
          category: Database["public"]["Enums"]["consideration_category_enum"]
          created_at?: string | null
          description: string
          id?: string
          importance: Database["public"]["Enums"]["importance_enum"]
          request_id: string
          requires_action?: boolean
          sort_order?: number | null
          title: string
          updated_at?: string | null
        }
        Update: {
          action_taken?: string | null
          category?: Database["public"]["Enums"]["consideration_category_enum"]
          created_at?: string | null
          description?: string
          id?: string
          importance?: Database["public"]["Enums"]["importance_enum"]
          request_id?: string
          requires_action?: boolean
          sort_order?: number | null
          title?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "other_considerations_request_id_fkey"
            columns: ["request_id"]
            isOneToOne: false
            referencedRelation: "requests"
            referencedColumns: ["id"]
          },
        ]
      }
      parties: {
        Row: {
          address: string | null
          authorized_representative: string | null
          id: number
          legal_document_id: number | null
          legal_form: string | null
          legal_identifiers: string | null
          name: string
          role: string | null
          type: string
        }
        Insert: {
          address?: string | null
          authorized_representative?: string | null
          id?: never
          legal_document_id?: number | null
          legal_form?: string | null
          legal_identifiers?: string | null
          name: string
          role?: string | null
          type: string
        }
        Update: {
          address?: string | null
          authorized_representative?: string | null
          id?: never
          legal_document_id?: number | null
          legal_form?: string | null
          legal_identifiers?: string | null
          name?: string
          role?: string | null
          type?: string
        }
        Relationships: []
      }
      profiles: {
        Row: {
          avatar_url: string | null
          company: string | null
          created_at: string
          File_instructions: string | null
          Folder_url: string | null
          full_name: string | null
          id: string
          is_approved: boolean
          template_id: string | null
          updated_at: string
        }
        Insert: {
          avatar_url?: string | null
          company?: string | null
          created_at?: string
          File_instructions?: string | null
          Folder_url?: string | null
          full_name?: string | null
          id: string
          is_approved?: boolean
          template_id?: string | null
          updated_at?: string
        }
        Update: {
          avatar_url?: string | null
          company?: string | null
          created_at?: string
          File_instructions?: string | null
          Folder_url?: string | null
          full_name?: string | null
          id?: string
          is_approved?: boolean
          template_id?: string | null
          updated_at?: string
        }
        Relationships: []
      }
      request_errors: {
        Row: {
          created_at: string | null
          description: string
          error_type: Database["public"]["Enums"]["error_type_enum"]
          id: string
          request_id: string
          resolution_status: Database["public"]["Enums"]["resolution_status_enum"]
          severity: Database["public"]["Enums"]["severity_enum"]
          sort_order: number | null
          title: string
          updated_at: string | null
        }
        Insert: {
          created_at?: string | null
          description: string
          error_type: Database["public"]["Enums"]["error_type_enum"]
          id?: string
          request_id: string
          resolution_status?: Database["public"]["Enums"]["resolution_status_enum"]
          severity: Database["public"]["Enums"]["severity_enum"]
          sort_order?: number | null
          title: string
          updated_at?: string | null
        }
        Update: {
          created_at?: string | null
          description?: string
          error_type?: Database["public"]["Enums"]["error_type_enum"]
          id?: string
          request_id?: string
          resolution_status?: Database["public"]["Enums"]["resolution_status_enum"]
          severity?: Database["public"]["Enums"]["severity_enum"]
          sort_order?: number | null
          title?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "request_errors_request_id_fkey"
            columns: ["request_id"]
            isOneToOne: false
            referencedRelation: "requests"
            referencedColumns: ["id"]
          },
        ]
      }
      requests: {
        Row: {
          autres_considerations: string | null
          circonscription: string | null
          complete_summary: string[] | null
          completed_at: string | null
          created_at: string | null
          erreurs: string | null
          final_doc_id: string | null
          final_doc_link: string | null
          folder_id: string | null
          folder_link: string | null
          hypotheques_years: number | null
          id: string
          inclure_actes_radies: boolean | null
          index_completed: number
          long_term_memory_doc_id: string | null
          number_of_actes: number | null
          number_of_index: number | null
          regimes_matrimoniaux: string | null
          request_summary: string
          resume_etapes_recherche: string | null
          sales_years: number | null
          seller_address: string
          seller_name: string
          servitudes: string | null
          status: string
          updated_at: string | null
          user_id: string
        }
        Insert: {
          autres_considerations?: string | null
          circonscription?: string | null
          complete_summary?: string[] | null
          completed_at?: string | null
          created_at?: string | null
          erreurs?: string | null
          final_doc_id?: string | null
          final_doc_link?: string | null
          folder_id?: string | null
          folder_link?: string | null
          hypotheques_years?: number | null
          id?: string
          inclure_actes_radies?: boolean | null
          index_completed?: number
          long_term_memory_doc_id?: string | null
          number_of_actes?: number | null
          number_of_index?: number | null
          regimes_matrimoniaux?: string | null
          request_summary: string
          resume_etapes_recherche?: string | null
          sales_years?: number | null
          seller_address: string
          seller_name: string
          servitudes?: string | null
          status?: string
          updated_at?: string | null
          user_id: string
        }
        Update: {
          autres_considerations?: string | null
          circonscription?: string | null
          complete_summary?: string[] | null
          completed_at?: string | null
          created_at?: string | null
          erreurs?: string | null
          final_doc_id?: string | null
          final_doc_link?: string | null
          folder_id?: string | null
          folder_link?: string | null
          hypotheques_years?: number | null
          id?: string
          inclure_actes_radies?: boolean | null
          index_completed?: number
          long_term_memory_doc_id?: string | null
          number_of_actes?: number | null
          number_of_index?: number | null
          regimes_matrimoniaux?: string | null
          request_summary?: string
          resume_etapes_recherche?: string | null
          sales_years?: number | null
          seller_address?: string
          seller_name?: string
          servitudes?: string | null
          status?: string
          updated_at?: string | null
          user_id?: string
        }
        Relationships: []
      }
      servitudes: {
        Row: {
          beneficiary: string
          burden_party: string | null
          created_at: string | null
          description: string
          duration: Database["public"]["Enums"]["duration_enum"]
          expiry_date: string | null
          id: string
          registration_date: string
          registration_number: string
          request_id: string
          servitude_type: Database["public"]["Enums"]["servitude_type_enum"]
          sort_order: number | null
          status: Database["public"]["Enums"]["servitude_status_enum"]
          updated_at: string | null
        }
        Insert: {
          beneficiary: string
          burden_party?: string | null
          created_at?: string | null
          description: string
          duration: Database["public"]["Enums"]["duration_enum"]
          expiry_date?: string | null
          id?: string
          registration_date: string
          registration_number: string
          request_id: string
          servitude_type: Database["public"]["Enums"]["servitude_type_enum"]
          sort_order?: number | null
          status?: Database["public"]["Enums"]["servitude_status_enum"]
          updated_at?: string | null
        }
        Update: {
          beneficiary?: string
          burden_party?: string | null
          created_at?: string | null
          description?: string
          duration?: Database["public"]["Enums"]["duration_enum"]
          expiry_date?: string | null
          id?: string
          registration_date?: string
          registration_number?: string
          request_id?: string
          servitude_type?: Database["public"]["Enums"]["servitude_type_enum"]
          sort_order?: number | null
          status?: Database["public"]["Enums"]["servitude_status_enum"]
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "servitudes_request_id_fkey"
            columns: ["request_id"]
            isOneToOne: false
            referencedRelation: "requests"
            referencedColumns: ["id"]
          },
        ]
      }
      title_chain: {
        Row: {
          created_at: string | null
          from_party: string
          id: string
          notary: string | null
          notes: string | null
          price: number | null
          registration_number: string
          request_id: string
          sort_order: number | null
          to_party: string
          transaction_date: string
          transaction_type: Database["public"]["Enums"]["transaction_type_enum"]
          updated_at: string | null
        }
        Insert: {
          created_at?: string | null
          from_party: string
          id?: string
          notary?: string | null
          notes?: string | null
          price?: number | null
          registration_number: string
          request_id: string
          sort_order?: number | null
          to_party: string
          transaction_date: string
          transaction_type: Database["public"]["Enums"]["transaction_type_enum"]
          updated_at?: string | null
        }
        Update: {
          created_at?: string | null
          from_party?: string
          id?: string
          notary?: string | null
          notes?: string | null
          price?: number | null
          registration_number?: string
          request_id?: string
          sort_order?: number | null
          to_party?: string
          transaction_date?: string
          transaction_type?: Database["public"]["Enums"]["transaction_type_enum"]
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "title_chain_request_id_fkey"
            columns: ["request_id"]
            isOneToOne: false
            referencedRelation: "requests"
            referencedColumns: ["id"]
          },
        ]
      }
      workflow_state: {
        Row: {
          in_progress: boolean | null
          workflow: string
        }
        Insert: {
          in_progress?: boolean | null
          workflow?: string
        }
        Update: {
          in_progress?: boolean | null
          workflow?: string
        }
        Relationships: []
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      acte_status_enum:
        | "En_attente"
        | "Pret_pour_analyse"
        | "Analyse_en_cours"
        | "Analyse_terminee"
        | "Document_indisponible"
      acte_type_enum: "Acte" | "Avis d'adresse" | "Radiation" | "Acte divers"
      charge_status_enum: "actif" | "a_radier" | "radie"
      charge_type_enum:
        | "hypotheque"
        | "privilege"
        | "clause_resolutoire"
        | "droit_preemption"
        | "autre"
      consideration_category_enum:
        | "cadastral"
        | "financier"
        | "physique"
        | "juridique"
        | "municipal"
        | "environnemental"
        | "autre"
      document_source_enum: "acte" | "index"
      duration_enum: "perpetuelle" | "temporaire" | "conditionnelle"
      error_type_enum:
        | "temporelle"
        | "documentaire"
        | "chaine_de_titres"
        | "conformite"
        | "evaluation"
        | "geographique"
        | "limitation"
        | "autre"
      extraction_status_enum:
        | "En_attente"
        | "En_traitement"
        | "Telecharge"
        | "Disponible_sur_Drive"
        | "Document_introuvable"
      importance_enum: "haute" | "moyenne" | "basse"
      index_phase_status_enum:
        | "En_attente"
        | "Pret_pour_analyse"
        | "Analyse_en_cours"
        | "Analyse_terminee"
        | "Document_indisponible"
      index_status_enum:
        | "Phase_1"
        | "En_attente_Phase_2"
        | "Phase_2"
        | "Phase_3"
        | "Termine"
      phase_2_status_enum:
        | "En_attente"
        | "Analyse_actes_en_cours"
        | "Analyse_terminee"
      regime_type_enum:
        | "societe_acquets"
        | "communaute_biens"
        | "separation_biens"
        | "celibataire"
        | "divorce"
        | "veuf"
        | "autre"
      resolution_status_enum: "en_attente" | "resolu" | "reconnu"
      servitude_status_enum: "actif" | "expire" | "radie"
      servitude_type_enum:
        | "passage"
        | "vue"
        | "utilite_publique"
        | "aqueduc_egout"
        | "nuisance"
        | "hydro_quebec"
        | "bell"
        | "autre"
      severity_enum: "critique" | "majeure" | "mineure" | "informationnelle"
      transaction_type_enum:
        | "vente"
        | "donation"
        | "succession"
        | "saisie"
        | "cession"
        | "transmission"
        | "autre"
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DefaultSchema = Database[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof Database },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof Database },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends { schema: keyof Database }
  ? Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never
