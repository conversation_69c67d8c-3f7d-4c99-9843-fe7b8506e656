
import { createRequest, createIndex } from '@/lib/supabase';
import { formatLotNumber } from '@/hooks/form/request-form-utils';
import { sendIndexWebhook } from '@/hooks/webhooks/use-index-webhook';
import type { RequestFormData } from '@/hooks/form/request-form-utils';
import type { Database } from '@/integrations/supabase/types';

/**
 * Creates a new request and associated index in Supabase
 * @param formData The form data for the request
 * @param userId The authenticated user's ID
 * @returns Promise with the result of the operation
 */
export async function createRequestAndIndex(formData: RequestFormData, userId: string) {
  // Use seller address as circonscription_fonciere if not provided
  const sellerAddress = formData.seller_address || formData.circonscription_fonciere || 'Address not provided';
  
  // Create request entry with the new summary format in French
  const requestData = {
    seller_name: formData.seller_name,
    seller_address: sellerAddress,
    user_id: userId, 
    status: 'In Progress', // Use string literal instead of enum since request_status_enum doesn't exist
    request_summary: `Recherche de titre pour ${formData.seller_name}, pour la propriété situé au ${sellerAddress}`
  };
  
  // Insert the request and get the ID
  const { data: requestResult, error: requestError } = await createRequest(requestData);
  
  if (requestError) throw requestError;
  
  if (!requestResult || !requestResult.id) {
    throw new Error('Failed to create request: No ID returned');
  }
  
  console.log('Request created with ID:', requestResult.id);
  
  // Format lot number
  const formattedLotNumber = formatLotNumber(formData.numero_lot);
  
  // Create an index entry associated with this request
  const indexData = {
    request_id: requestResult.id,
    lot_number: formattedLotNumber,
    circonscription: formData.circonscription_fonciere,
    cadastre: 'Cadastre du Québec',
    index_initial: true,
    doc_number: 1
  };
  
  const { data: indexResult, error: indexError } = await createIndex(indexData);
  
  if (indexError) {
    throw indexError;
  }
  
  // Send webhook if index was created successfully
  let webhookSuccess = false;
  if (indexResult) {
    webhookSuccess = await sendIndexWebhook(indexResult);
  }
  
  return {
    requestResult,
    indexResult,
    webhookSuccess
  };
}
