import { Stagehand } from '@browserbasehq/stagehand';
import { CONFIG } from './config';
import { cleanupCacheLock } from './utils/cleanup';

async function testLogin() {
  console.log('Testing login process...');
  
  // Clean up cache
  await cleanupCacheLock();
  
  const stagehand = new Stagehand({
    env: 'LOCAL',
    modelName: 'gpt-4o' as any,
    modelClientOptions: CONFIG.MODEL_API_KEY ? { apiKey: CONFIG.MODEL_API_KEY } : undefined,
    enableCaching: false,
    selfHeal: true,
    verbose: 2 as any,
    domSettleTimeoutMs: 30000,
    localBrowserLaunchOptions: {
      headless: false,
      viewport: { width: 1280, height: 720 }
    }
  });

  try {
    await stagehand.init();
    const page = stagehand.page;
    
    console.log('Step 1: Navigate to base URL');
    await page.goto(CONFIG.BASE_URL);
    await page.waitForTimeout(2000);
    
    console.log('Step 2: Taking screenshot of main page');
    await page.screenshot({ path: 'scripts/stagehand-extraction/screenshots/1_main_page.png' });
    
    console.log('Step 3: Navigate directly to login page');
    await page.goto('https://www.registrefoncier.gouv.qc.ca/Sirf/pf_acces.asp');
    await page.waitForTimeout(2000);
    
    console.log('Step 4: Taking screenshot of login page');
    await page.screenshot({ path: 'scripts/stagehand-extraction/screenshots/2_login_page.png' });
    
    console.log('Step 5: Fill login form using Stage Hand act');
    await page.act(`type '${CONFIG.USER_CODE}' into the input field with name 'code_util'`);
    await page.act(`type '${CONFIG.PASSWORD}' into the input field with name 'mot_passe'`);
    
    console.log('Step 6: Taking screenshot after filling form');
    await page.screenshot({ path: 'scripts/stagehand-extraction/screenshots/3_form_filled.png' });
    
    console.log('Step 7: Submit form');
    await page.act("click the submit button");
    
    console.log('Step 8: Wait for login to complete');
    await page.waitForTimeout(5000);
    
    console.log('Step 9: Taking screenshot after login');
    await page.screenshot({ path: 'scripts/stagehand-extraction/screenshots/4_after_login.png' });
    
    console.log('Step 10: Navigate to Acte search page');
    await page.goto(CONFIG.ACTE_SEARCH_URL);
    await page.waitForTimeout(2000);
    
    console.log('Step 11: Taking screenshot of Acte search page');
    await page.screenshot({ path: 'scripts/stagehand-extraction/screenshots/5_acte_search.png' });
    
    console.log('Login test completed successfully!');
    
  } catch (error) {
    console.error('Login test failed:', error);
  } finally {
    await stagehand.close();
  }
}

// Run the test
testLogin().catch(console.error);
