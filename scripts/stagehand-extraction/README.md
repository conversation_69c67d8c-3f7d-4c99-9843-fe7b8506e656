# Stage Hand Document Extraction

This is a Stage Hand-based implementation for automated document extraction from the Registre Foncier du Québec website. It uses AI-powered browser automation with a three-path fallback strategy to ensure reliable document downloads.

## Features

- **Three-Path Strategy**:
  1. **Exact Match**: Uses exact field values from the database
  2. **Intelligent Match**: Employs fuzzy matching and variations
  3. **Exploratory Mode**: Discovers alternative search methods

- **Non-blocking Error Handling**: Failures are logged but don't stop the queue
- **Automatic File Management**: Downloads are renamed and organized
- **Comprehensive Logging**: All attempts and errors are tracked in Supabase
- **Batch Processing**: Configurable batch sizes with retry limits

## Prerequisites

- Node.js 18+ 
- A Supabase project with the extraction queue schema
- OpenAI API key (or Anthropic API key)
- Registre Foncier du Québec credentials

## Installation

1. Navigate to the Stage Hand extraction directory:
```bash
cd scripts/stagehand-extraction
```

2. Install dependencies:
```bash
npm install
```

3. Copy the environment template:
```bash
cp .env.example .env
```

4. Configure your `.env` file with your credentials:
```env
# Supabase Configuration
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_SERVICE_KEY=your-service-key

# Registre Foncier Credentials
RFQ_USER_CODE=your-user-code
RFQ_PASSWORD=your-password

# LLM Configuration
LLM_MODEL=gpt-4o
OPENAI_API_KEY=your-openai-api-key

# Debug Settings
DEBUG=false
HEADLESS=true
VERBOSE=1

# Batch Processing
BATCH_SIZE=10
RETRY_LIMIT=3
```

## Usage

### Basic Usage
```bash
npm start
```

### Debug Mode
```bash
npm run start:debug
```

### With Cleanup
```bash
npm start -- --cleanup
```

## How It Works

1. **Queue Processing**: Fetches items with status `En_attente` from Supabase
2. **Login**: Logs into the Registre Foncier website once per session
3. **Document Type Detection**: Routes to appropriate search page (Acte or Index)
4. **Three-Path Execution**:
   - Tries exact match first
   - Falls back to intelligent matching if needed
   - Uses exploratory mode as final attempt
5. **Download Management**: 
   - Waits for downloads to complete
   - Renames files using document numbers
   - Verifies file integrity
6. **Status Updates**: Updates Supabase with success/failure status

## Strategy Details

### Option 1: Exact Match
- Uses exact field values from the database
- Follows the precise navigation path
- Most reliable when data is accurate

### Option 2: Intelligent Match
- Implements fuzzy string matching (Levenshtein distance)
- Generates variations of input values
- Handles common abbreviations and formatting differences
- Extracts dropdown options for best match selection

### Option 3: Exploratory Mode
- Analyzes page structure to find alternative search methods
- Looks for advanced search options
- Uses contextual clues to complete searches
- Can recover from navigation errors

## Database Schema

The extraction queue table should have:
- `id`: UUID primary key
- `document_number`: The document identifier
- `document_source`: Either 'acte' or 'index'
- `status`: Enum ('En_attente', 'En_traitement', 'Telecharge', 'Echec')
- `circonscription_fonciere`: Optional circonscription
- `cadastre`: Optional cadastre (for index)
- `designation_secondaire`: Optional designation (for index)
- `acte_type`: Optional type (for acte)
- `retry_count`: Number of retry attempts
- `file_path`: Path to downloaded file

## Error Handling

- All errors are logged to the `error_log` table
- Screenshots are captured in debug mode
- Each strategy failure is tracked separately
- Non-blocking: failures don't stop the queue

## File Structure

```
stagehand-extraction/
├── config.ts                 # Configuration and types
├── supabase-client.ts       # Database integration
├── extraction-processor.ts   # Main processor
├── index.ts                 # Entry point
├── types.ts                 # TypeScript types
├── strategies/              # Search strategies
│   ├── exact-match.ts
│   ├── intelligent-match.ts
│   └── exploratory.ts
├── utils/                   # Utilities
│   ├── file-manager.ts
│   └── error-logger.ts
├── downloads/               # Downloaded files
│   └── [item-id]/          # Per-item folders
└── screenshots/             # Debug screenshots
```

## Monitoring

Monitor the extraction process through:
1. Console logs (verbose output)
2. Supabase `extraction_queue` table status
3. Supabase `error_log` table for failures
4. Downloaded files in the `downloads` directory

## Troubleshooting

### Downloads Not Working
- Ensure the download directory exists and is writable
- Check browser download settings
- Verify the print button is being clicked

### Login Failures
- Verify credentials in `.env`
- Check if the website structure has changed
- Enable debug mode for screenshots

### Search Failures
- Enable verbose logging (VERBOSE=2)
- Check error logs in Supabase
- Review screenshots in debug mode
- Verify field values in the database

## Development

### Running TypeScript directly:
```bash
npx tsx index.ts
```

### Building for production:
```bash
npm run build
```

### Cleaning downloads:
```bash
npm run clean
```

## Notes

- The script maintains a single browser session for efficiency
- Downloads are organized by item ID to avoid conflicts
- Old downloads can be cleaned up automatically with the `--cleanup` flag
- The script respects rate limits with configurable delays
- Stage Hand's AI capabilities handle dynamic page content effectively
