import { StagehandPage } from '../types';

/**
 * Handle popup windows and switch context
 */
export async function handlePopupWindow(
  page: StagehandPage,
  action: () => Promise<void>
): Promise<StagehandPage | null> {
  try {
    // Get current pages before action
    const pagesBefore = page.context().pages();
    
    // Perform the action that might open a popup
    await action();
    
    // Wait a bit for popup to open
    await page.waitForTimeout(2000);
    
    // Get pages after action
    const pagesAfter = page.context().pages();
    
    // Find the new page
    const newPage = pagesAfter.find(p => !pagesBefore.includes(p));
    
    if (newPage) {
      console.log('New popup window detected, switching context');
      // Wait for the new page to load
      await newPage.waitForLoadState('domcontentloaded');
      return newPage as unknown as StagehandPage;
    }
    
    return null;
  } catch (error) {
    console.error('Error handling popup window:', error);
    return null;
  }
}

/**
 * Close all popup windows except the main one
 */
export async function closePopupWindows(page: StagehandPage): Promise<void> {
  try {
    const pages = page.context().pages();
    for (const p of pages) {
      if (p !== page && !p.isClosed()) {
        await p.close();
      }
    }
  } catch (error) {
    console.error('Error closing popup windows:', error);
  }
}
