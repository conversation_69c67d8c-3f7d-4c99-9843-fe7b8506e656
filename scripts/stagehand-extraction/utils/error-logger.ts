import { promises as fs } from 'fs';
import * as path from 'path';
import { CONFIG, ErrorLog } from '../config';
import { FileManager } from './file-manager';
import { SupabaseService } from '../supabase-client';
import { StagehandPage } from '../types';

export class ErrorLogger {
  private fileManager: FileManager;
  private supabaseService: SupabaseService;

  constructor(supabaseService: SupabaseService) {
    this.fileManager = new FileManager();
    this.supabaseService = supabaseService;
  }

  /**
   * Log an error with optional screenshot
   */
  async logError(
    error: ErrorLog,
    page?: StagehandPage
  ): Promise<void> {
    console.error(`[Option ${error.option}] ${error.step}: ${error.error}`);

    // Take screenshot if page is available and in debug mode
    if (page && CONFIG.DEBUG_MODE) {
      try {
        const screenshotPath = this.fileManager.getScreenshotPath(
          error.itemId,
          error.option,
          error.timestamp
        );
        
        // Ensure screenshot directory exists
        await fs.mkdir(path.dirname(screenshotPath), { recursive: true });
        
        // Take screenshot
        await page.screenshot({
          path: screenshotPath,
          fullPage: true
        });
        
        error.screenshot = screenshotPath;
        console.log(`Screenshot saved to: ${screenshotPath}`);
      } catch (screenshotError) {
        console.error('Failed to take screenshot:', screenshotError);
      }
    }

    // Log to Supabase error_log table
    await this.supabaseService.logError(
      'stagehand_extraction',
      JSON.stringify(error)
    );
  }

  /**
   * Create an error log entry
   */
  createErrorLog(
    itemId: string,
    documentNumber: string,
    option: 1 | 2 | 3,
    step: string,
    error: Error | string
  ): ErrorLog {
    return {
      itemId,
      documentNumber,
      option,
      step,
      error: error instanceof Error ? error.message : error,
      timestamp: new Date().toISOString()
    };
  }

  /**
   * Log extraction attempt details
   */
  logAttempt(
    documentNumber: string,
    option: number,
    step: string,
    success: boolean,
    details?: any
  ): void {
    const status = success ? 'SUCCESS' : 'FAILED';
    const timestamp = new Date().toISOString();
    
    console.log(`[${timestamp}] [Option ${option}] [${status}] ${step} for document ${documentNumber}`);
    
    if (details && CONFIG.VERBOSE >= 2) {
      console.log('Details:', JSON.stringify(details, null, 2));
    }
  }

  /**
   * Generate a summary report of errors
   */
  async generateErrorSummary(errors: ErrorLog[]): Promise<string> {
    const summary = {
      totalErrors: errors.length,
      byOption: {
        option1: errors.filter(e => e.option === 1).length,
        option2: errors.filter(e => e.option === 2).length,
        option3: errors.filter(e => e.option === 3).length
      },
      byStep: errors.reduce((acc, error) => {
        acc[error.step] = (acc[error.step] || 0) + 1;
        return acc;
      }, {} as Record<string, number>),
      documents: [...new Set(errors.map(e => e.documentNumber))]
    };

    return JSON.stringify(summary, null, 2);
  }
}
