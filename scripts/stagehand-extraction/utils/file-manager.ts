import { promises as fs } from 'fs';
import * as path from 'path';
import { CONFIG } from '../config';

export class FileManager {
  /**
   * Ensure the download directory exists
   */
  async ensureDownloadDirectory(subDir?: string): Promise<string> {
    const fullPath = subDir 
      ? path.join(CONFIG.BASE_DOWNLOAD_DIR, subDir)
      : CONFIG.BASE_DOWNLOAD_DIR;
    
    await fs.mkdir(fullPath, { recursive: true });
    return fullPath;
  }

  /**
   * Find the newest PDF file in a directory
   */
  async findNewestPdfFile(directory: string): Promise<string | null> {
    try {
      const files = await fs.readdir(directory);
      const pdfFiles = files.filter(f => f.endsWith('.pdf') && !f.startsWith('.'));
      
      if (pdfFiles.length === 0) {
        return null;
      }

      // Get file stats and sort by creation time
      const fileStats = await Promise.all(
        pdfFiles.map(async (file) => {
          const filePath = path.join(directory, file);
          const stats = await fs.stat(filePath);
          return { file, mtime: stats.mtime.getTime() };
        })
      );

      fileStats.sort((a, b) => b.mtime - a.mtime);
      return fileStats[0].file;
    } catch (error) {
      console.error('Error finding newest PDF:', error);
      return null;
    }
  }

  /**
   * Generate a unique filename if the target already exists
   */
  async getUniqueFileName(directory: string, fileName: string): Promise<string> {
    const baseName = path.basename(fileName, '.pdf');
    const extension = '.pdf';
    let counter = 0;
    let targetPath = path.join(directory, fileName);

    while (true) {
      try {
        await fs.access(targetPath);
        // File exists, generate new name
        counter++;
        const newFileName = `${baseName}_${counter}${extension}`;
        targetPath = path.join(directory, newFileName);
      } catch {
        // File doesn't exist, we can use this name
        return targetPath;
      }
    }
  }

  /**
   * Rename a file with duplicate handling
   */
  async renameFileWithDuplicateHandling(
    sourceDir: string,
    sourceFile: string,
    targetName: string
  ): Promise<string> {
    const sourcePath = path.join(sourceDir, sourceFile);
    const targetPath = await this.getUniqueFileName(sourceDir, targetName);
    
    await fs.rename(sourcePath, targetPath);
    return targetPath;
  }

  /**
   * Wait for a file to be completely downloaded
   */
  async waitForDownloadComplete(
    directory: string,
    timeout: number = CONFIG.DOWNLOAD_TIMEOUT
  ): Promise<string | null> {
    const startTime = Date.now();
    let lastFileSize = -1;
    let stableCount = 0;
    const requiredStableChecks = 3;
    
    while (Date.now() - startTime < timeout) {
      const newestFile = await this.findNewestPdfFile(directory);
      
      if (newestFile) {
        const filePath = path.join(directory, newestFile);
        const stats = await fs.stat(filePath);
        
        // Check if file size is stable
        if (stats.size === lastFileSize && stats.size > 0) {
          stableCount++;
          if (stableCount >= requiredStableChecks) {
            return newestFile;
          }
        } else {
          stableCount = 0;
          lastFileSize = stats.size;
        }
      }
      
      // Wait before checking again
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
    
    return null;
  }

  /**
   * Clean up old downloads
   */
  async cleanupOldDownloads(directory: string, daysToKeep: number = 7): Promise<void> {
    try {
      const files = await fs.readdir(directory);
      const now = Date.now();
      const maxAge = daysToKeep * 24 * 60 * 60 * 1000; // Convert days to milliseconds

      for (const file of files) {
        if (file.endsWith('.pdf')) {
          const filePath = path.join(directory, file);
          const stats = await fs.stat(filePath);
          
          if (now - stats.mtime.getTime() > maxAge) {
            await fs.unlink(filePath);
            console.log(`Cleaned up old file: ${file}`);
          }
        }
      }
    } catch (error) {
      console.error('Error cleaning up old downloads:', error);
    }
  }

  /**
   * Check if a file exists and has content
   */
  async verifyFileContent(filePath: string, minSize: number = 1024): Promise<boolean> {
    try {
      const stats = await fs.stat(filePath);
      return stats.size >= minSize;
    } catch {
      return false;
    }
  }

  /**
   * Create a screenshot path
   */
  getScreenshotPath(itemId: string, option: number, timestamp?: string): string {
    const ts = timestamp || new Date().toISOString().replace(/:/g, '-');
    return path.join(CONFIG.BASE_DOWNLOAD_DIR, 'screenshots', `${itemId}_option${option}_${ts}.png`);
  }
}
