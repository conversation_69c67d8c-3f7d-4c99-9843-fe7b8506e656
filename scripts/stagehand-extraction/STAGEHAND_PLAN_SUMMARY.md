# Stage Hand Document Extraction - Implementation Plan Summary

## Overview

This Stage Hand implementation provides an AI-powered, fault-tolerant document extraction system for the Registre Foncier du Québec. It replaces the previous Playwright implementation with Stage Hand's natural language browser automation capabilities.

## Key Improvements Over Playwright Version

1. **AI-Powered Element Selection**: Stage Hand uses natural language to find and interact with elements, making it more resilient to UI changes
2. **Self-Healing**: Built-in recovery mechanisms when elements aren't found
3. **Intelligent Context Understanding**: Can interpret page content and adapt to different layouts
4. **Simplified Code**: Natural language commands replace complex selectors

## Architecture

### Core Components

1. **ExtractionProcessor** (`extraction-processor.ts`)
   - Main orchestrator
   - Manages Stage Hand lifecycle
   - Coordinates the three-path strategy
   - Handles queue processing

2. **Three Strategy Classes**
   - **ExactMatchStrategy**: Direct field matching
   - **IntelligentMatchStrategy**: Fuzzy matching with variations
   - **ExploratoryStrategy**: AI-powered page exploration

3. **Support Utilities**
   - **FileManager**: Download handling and organization
   - **ErrorLogger**: Comprehensive error tracking
   - **SupabaseService**: Database integration

### Execution Flow

```mermaid
graph TD
    A[Start] --> B[Initialize Stage Hand]
    B --> C[Login to RFQ]
    C --> D[Fetch Queue Item]
    D --> E{Item Available?}
    E -->|No| Z[End]
    E -->|Yes| F[Update Status: En_traitement]
    F --> G{Document Type?}
    G -->|Acte| H[Navigate to Acte Search]
    G -->|Index| I[Navigate to Index Search]
    H --> J[Try Option 1: Exact Match]
    I --> J
    J --> K{Success?}
    K -->|Yes| R[Download & Rename]
    K -->|No| L[Try Option 2: Intelligent Match]
    L --> M{Success?}
    M -->|Yes| R
    M -->|No| N[Try Option 3: Exploratory Mode]
    N --> O{Success?}
    O -->|Yes| R
    O -->|No| P[Log Failure]
    R --> S[Update Status: Telecharge]
    P --> T[Update Status: Echec]
    S --> U[Next Item]
    T --> U
    U --> D
```

### Stage Hand Integration Points

1. **Natural Language Commands**
   ```typescript
   // Instead of complex selectors:
   await page.act("click on 'Entrée du site' link");
   await page.act(`fill the 'Code d'utilisateur' field with '${username}'`);
   ```

2. **Intelligent Data Extraction**
   ```typescript
   const options = await page.extract({
     instruction: "Extract all dropdown options",
     schema: z.object({ options: z.array(z.string()) })
   });
   ```

3. **Context-Aware Navigation**
   ```typescript
   await page.act("look for and click on any menu items related to document search");
   ```

## Three-Path Strategy Details

### Option 1: Exact Match (Primary)
- Uses exact database values
- Most reliable when data is accurate
- Example: `select 'Joliette' from the 'Circonscription foncière' dropdown`

### Option 2: Intelligent Match (Fallback 1)
- Implements Levenshtein distance for fuzzy matching
- Generates input variations:
  - "Rang t5" → ["Rang t5", "Rang 5", "Rangt5", "R5"]
  - "St-Joseph" → ["St-Joseph", "Saint-Joseph"]
- Extracts and matches dropdown options dynamically

### Option 3: Exploratory Mode (Fallback 2)
- AI analyzes page structure
- Looks for alternative search methods
- Uses contextual clues to complete searches
- Can recover from navigation errors

## Error Handling Strategy

1. **Non-Blocking**: Failures don't stop the queue
2. **Comprehensive Logging**: Each attempt is tracked
3. **Screenshots**: Captured in debug mode
4. **Retry Logic**: Configurable retry limits
5. **Status Tracking**: 
   - En_attente → En_traitement → Telecharge/Echec

## File Management

```
downloads/
├── [item-id-1]/
│   └── 12345.pdf
├── [item-id-2]/
│   └── 67890.pdf
└── screenshots/
    └── error_option1_2024-06-04T10-15-30.png
```

## Configuration Options

- **LLM_MODEL**: Choose between GPT-4o or Claude
- **HEADLESS**: Run with or without browser UI
- **DEBUG**: Enable screenshots and verbose logging
- **BATCH_SIZE**: Control processing limits
- **RETRY_LIMIT**: Maximum retry attempts

## Deployment Considerations

1. **Environment Setup**
   - Install Node.js 18+
   - Configure .env with credentials
   - Ensure download directory permissions

2. **Monitoring**
   - Check Supabase queue status
   - Review error_log table
   - Monitor console output

3. **Scaling**
   - Can run multiple instances with different queues
   - Batch size controls resource usage
   - Rate limiting prevents overload

## Usage Examples

```bash
# Basic execution
npm start

# Debug mode with visible browser
npm run start:debug

# With old file cleanup
npm start -- --cleanup

# Production with specific batch
BATCH_SIZE=50 npm start
```

## Future Enhancements

1. **Parallel Processing**: Multiple browser instances
2. **Queue Priority**: Process urgent items first
3. **OCR Integration**: For scanned documents
4. **Webhook Notifications**: Real-time status updates
5. **Performance Metrics**: Success rates, timing data

## Summary

This Stage Hand implementation provides a robust, AI-powered solution for document extraction that:
- Handles website changes gracefully
- Recovers from errors automatically
- Provides detailed logging and monitoring
- Scales efficiently with queue-based processing
- Maintains high success rates through fallback strategies

The natural language capabilities of Stage Hand make this solution more maintainable and resilient compared to traditional selector-based automation.
