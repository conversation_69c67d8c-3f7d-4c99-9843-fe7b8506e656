import { BaseService, ServiceConfig } from './base-service.js';
import { Alert } from '../types'; // Import Alert from types.ts

export interface AlertRule {
  id: string;
  name: string;
  description: string;
  category: string;
  condition: string; // JSON expression for evaluation
  severity: 'low' | 'medium' | 'high' | 'critical';
  cooldownPeriod: number; // Minutes before same alert can fire again
  enabled: boolean;
  notificationChannels: string[];
  escalationRules: EscalationRule[];
  suppressionRules: SuppressionRule[];
}

export interface EscalationRule {
  level: number;
  delayMinutes: number;
  channels: string[];
  recipients: string[];
  condition?: string; // Optional condition for escalation
}

export interface SuppressionRule {
  condition: string;
  duration: number; // Minutes
  reason: string;
}

export interface NotificationChannel {
  id: string;
  name: string;
  type: 'n8n_webhook' | 'email' | 'slack' | 'teams' | 'custom';
  config: Record<string, any>;
  enabled: boolean;
  rateLimitPerHour: number;
  currentHourCount: number;
  lastResetTime: Date;
}

export interface AlertMetrics {
  totalAlerts: number;
  alertsBySeverity: Record<string, number>;
  alertsByCategory: Record<string, number>;
  averageResolutionTime: number;
  escalationRate: number;
  falsePositiveRate: number;
  channelDeliveryRates: Record<string, number>;
  suppressionEffectiveness: number;
}

export class AlertManager extends BaseService {
  private alerts: Map<string, Alert> = new Map();
  private alertRules: Map<string, AlertRule> = new Map();
  private notificationChannels: Map<string, NotificationChannel> = new Map();
  private alertHistory: Alert[] = [];
  private correlationGroups: Map<string, string[]> = new Map(); // correlationId -> alertIds
  private suppressions: Map<string, { until: Date; reason: string }> = new Map();
  private maxAlertHistory = 10000;
  private alertMetrics: AlertMetrics = {
    totalAlerts: 0,
    alertsBySeverity: {},
    alertsByCategory: {},
    averageResolutionTime: 0,
    escalationRate: 0,
    falsePositiveRate: 0,
    channelDeliveryRates: {},
    suppressionEffectiveness: 0
  };

  constructor(config: ServiceConfig) {
    super(config);
  }

  async initialize(): Promise<void> {
    console.log('Initializing Alert Manager...');
    await this.loadConfiguration();
    this.initializeDefaultRules();
    this.setupDefaultChannels();
    this.startEscalationMonitor();
    this.startMetricsCalculation();
    console.log('Alert Manager initialized successfully');
  }

  async shutdown(): Promise<void> {
    console.log('Shutting down Alert Manager...');
    await this.saveConfiguration();
    console.log('Alert Manager shutdown complete');
  }

  async healthCheck(): Promise<boolean> {
    // Check if notification channels are responsive
    const enabledChannels = Array.from(this.notificationChannels.values()).filter(c => c.enabled);
    return enabledChannels.length > 0;
  }

  /**
   * Create a new alert
   */
  async createAlert(alertData: Omit<Alert, 'id' | 'timestamp' | 'status' | 'escalationLevel' | 'type' | 'acknowledged' | 'acknowledgedBy' | 'acknowledgedAt' | 'resolved' | 'resolvedAt' | 'suppressUntil'>): Promise<string> {
    const alertId = this.generateAlertId();
    
    const alert: Alert = {
      id: alertId,
      timestamp: new Date(),
      status: 'active',
      escalationLevel: 0,
      type: 'manual', // Default type for manually created alerts
      acknowledged: false,
      resolved: false,
      ...alertData
    };

    // Check for suppression
    if (this.isAlertSuppressed(alert)) {
      console.log(`Alert suppressed: ${alert.title}`);
      return alertId;
    }

    // Check for deduplication
    const existingAlert = this.findSimilarAlert(alert);
    if (existingAlert) {
      console.log(`Duplicate alert detected, updating existing: ${existingAlert.id}`);
      return this.updateAlertMetadata(existingAlert.id, alert.metadata);
    }

    // Store alert
    this.alerts.set(alertId, alert);
    this.alertHistory.push({ ...alert });

    // Maintain history size
    if (this.alertHistory.length > this.maxAlertHistory) {
      this.alertHistory = this.alertHistory.slice(-this.maxAlertHistory);
    }

    // Handle correlation
    if (alert.correlationId) {
      this.addToCorrelationGroup(alert.correlationId, alertId);
    }

    // Send notifications
    await this.processAlertNotifications(alert);

    // Update metrics
    this.updateAlertMetrics(alert);

    this.emit('alert:created', alert);
    return alertId;
  }

  /**
   * Acknowledge an alert
   */
  async acknowledgeAlert(alertId: string, acknowledgedBy: string): Promise<boolean> {
    const alert = this.alerts.get(alertId);
    if (!alert || alert.status !== 'active') {
      return false;
    }

    alert.status = 'acknowledged';
    alert.acknowledgedBy = acknowledgedBy;
    alert.acknowledgedAt = new Date();

    this.emit('alert:acknowledged', { alert, acknowledgedBy });
    return true;
  }

  /**
   * Resolve an alert
   */
  async resolveAlert(alertId: string, resolvedBy?: string): Promise<boolean> {
    const alert = this.alerts.get(alertId);
    if (!alert || alert.status === 'resolved') {
      return false;
    }

    alert.status = 'resolved';
    alert.resolvedAt = new Date();

    // Calculate resolution time
    const resolutionTime = alert.resolvedAt.getTime() - alert.timestamp.getTime();
    
    this.emit('alert:resolved', { alert, resolutionTime, resolvedBy });
    return true;
  }

  /**
   * Suppress alerts matching criteria
   */
  async suppressAlerts(condition: string, durationMinutes: number, reason: string): Promise<void> {
    const until = new Date(Date.now() + durationMinutes * 60 * 1000);
    this.suppressions.set(condition, { until, reason });

    // Suppress existing active alerts that match
    for (const alert of this.alerts.values()) {
      if (alert.status === 'active' && this.evaluateCondition(condition, alert)) {
        alert.status = 'suppressed';
      }
    }

    this.emit('alerts:suppressed', { condition, durationMinutes, reason });
  }

  /**
   * Get active alerts with filtering
   */
  getActiveAlerts(filters: {
    severity?: string[];
    category?: string[];
    status?: string[];
    limit?: number;
  } = {}): Alert[] {
    let filteredAlerts = Array.from(this.alerts.values());

    if (filters.severity) {
      filteredAlerts = filteredAlerts.filter(a => filters.severity!.includes(a.severity));
    }

    if (filters.category) {
      filteredAlerts = filteredAlerts.filter(a => filters.category!.includes(a.category));
    }

    if (filters.status) {
      filteredAlerts = filteredAlerts.filter(a => filters.status!.includes(a.status));
    }

    // Sort by severity and timestamp
    filteredAlerts.sort((a, b) => {
      const severityOrder = { critical: 4, high: 3, medium: 2, low: 1 };
      const severityDiff = severityOrder[b.severity] - severityOrder[a.severity];
      if (severityDiff !== 0) return severityDiff;
      return b.timestamp.getTime() - a.timestamp.getTime();
    });

    if (filters.limit) {
      filteredAlerts = filteredAlerts.slice(0, filters.limit);
    }

    return filteredAlerts;
  }

  /**
   * Get alert statistics and metrics
   */
  getAlertMetrics(): AlertMetrics {
    return { ...this.alertMetrics };
  }

  /**
   * Get correlated alerts
   */
  getCorrelatedAlerts(alertId: string): Alert[] {
    const alert = this.alerts.get(alertId);
    if (!alert || !alert.correlationId) {
      return [];
    }

    const correlatedIds = this.correlationGroups.get(alert.correlationId) || [];
    return correlatedIds
      .filter(id => id !== alertId)
      .map(id => this.alerts.get(id))
      .filter(Boolean) as Alert[];
  }

  /**
   * Add or update an alert rule
   */
  async addAlertRule(rule: AlertRule): Promise<void> {
    this.alertRules.set(rule.id, rule);
    this.emit('alert_rule:added', rule);
  }

  /**
   * Add or update a notification channel
   */
  async addNotificationChannel(channel: NotificationChannel): Promise<void> {
    this.notificationChannels.set(channel.id, channel);
    this.emit('notification_channel:added', channel);
  }

  /**
   * Process incoming metrics for alert evaluation
   */
  async processMetrics(metrics: Record<string, any>): Promise<void> {
    for (const rule of this.alertRules.values()) {
      if (!rule.enabled) continue;

      try {
        if (this.evaluateAlertCondition(rule.condition, metrics)) {
          // Check cooldown
          if (!this.isRuleCooldownExpired(rule)) {
            continue;
          }

          // Create alert from rule
          await this.createAlert({
            severity: rule.severity,
            category: rule.category as any,
            title: rule.name,
            description: `Alert triggered by rule: ${rule.description}`,
            source: 'alert_rule',
            tags: ['automated', `rule:${rule.id}`],
            metadata: { rule: rule.id, triggerMetrics: metrics },
            message: `Alert triggered by rule: ${rule.name}`, // Added message
            context: { ruleId: rule.id, triggerMetrics: metrics } // Added context
          });

          // Update rule cooldown
          this.updateRuleCooldown(rule);
        }
      } catch (error) {
        this.handleError(error, `alert_rule_evaluation:${rule.id}`);
      }
    }
  }

  /**
   * Generate a unique alert ID
   */
  private generateAlertId(): string {
    return `alert_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Check if an alert should be suppressed
   */
  private isAlertSuppressed(alert: Alert): boolean {
    const now = new Date();
    
    for (const [condition, suppression] of this.suppressions.entries()) {
      if (now > suppression.until) {
        this.suppressions.delete(condition);
        continue;
      }

      if (this.evaluateCondition(condition, alert)) {
        return true;
      }
    }

    return false;
  }

  /**
   * Find similar existing alert for deduplication
   */
  private findSimilarAlert(newAlert: Alert): Alert | null {
    const similarityThreshold = 0.8;
    
    for (const alert of this.alerts.values()) {
      if (alert.status === 'resolved') continue;

      const similarity = this.calculateAlertSimilarity(alert, newAlert);
      if (similarity > similarityThreshold) {
        return alert;
      }
    }

    return null;
  }

  /**
   * Calculate similarity between two alerts
   */
  private calculateAlertSimilarity(alert1: Alert, alert2: Alert): number {
    let score = 0;
    let factors = 0;

    // Title similarity
    if (alert1.title === alert2.title) score += 0.4;
    factors++;

    // Category similarity
    if (alert1.category === alert2.category) score += 0.2;
    factors++;

    // Source similarity
    if (alert1.source === alert2.source) score += 0.2;
    factors++;

    // Tag overlap
    const commonTags = alert1.tags.filter(tag => alert2.tags.includes(tag));
    const tagSimilarity = commonTags.length / Math.max(alert1.tags.length, alert2.tags.length, 1);
    score += tagSimilarity * 0.2;
    factors++;

    return score / factors;
  }

  /**
   * Update alert metadata
   */
  private updateAlertMetadata(alertId: string, newMetadata: Record<string, any>): string {
    const alert = this.alerts.get(alertId);
    if (alert) {
      alert.metadata = { ...alert.metadata, ...newMetadata };
      alert.timestamp = new Date(); // Update timestamp for frequency tracking
    }
    return alertId;
  }

  /**
   * Add alert to correlation group
   */
  private addToCorrelationGroup(correlationId: string, alertId: string): void {
    const group = this.correlationGroups.get(correlationId) || [];
    group.push(alertId);
    this.correlationGroups.set(correlationId, group);
  }

  /**
   * Process alert notifications
   */
  private async processAlertNotifications(alert: Alert): Promise<void> {
    // Find applicable rules for this alert
    const applicableRules = Array.from(this.alertRules.values()).filter(rule =>
      rule.category === alert.category && rule.severity === alert.severity
    );

    // Send immediate notifications
    for (const rule of applicableRules) {
      for (const channelId of rule.notificationChannels) {
        await this.sendNotification(channelId, alert, 0);
      }
    }

    // Schedule escalations
    this.scheduleEscalations(alert, applicableRules);
  }

  /**
   * Send notification to specific channel
   */
  private async sendNotification(channelId: string, alert: Alert, escalationLevel: number): Promise<boolean> {
    const channel = this.notificationChannels.get(channelId);
    if (!channel || !channel.enabled) {
      return false;
    }

    // Check rate limiting
    if (!this.checkChannelRateLimit(channel)) {
      console.warn(`Rate limit exceeded for channel: ${channelId}`);
      return false;
    }

    try {
      const success = await this.deliverNotification(channel, alert, escalationLevel);
      if (success) {
        this.updateChannelUsage(channel);
      }
      return success;
    } catch (error) {
      this.handleError(error, `notification_delivery:${channelId}`);
      return false;
    }
  }

  /**
   * Deliver notification based on channel type
   */
  private async deliverNotification(channel: NotificationChannel, alert: Alert, escalationLevel: number): Promise<boolean> {
    const notificationData = {
      alert: {
        id: alert.id,
        severity: alert.severity,
        category: alert.category,
        title: alert.title,
        description: alert.description,
        timestamp: alert.timestamp.toISOString(),
        escalationLevel,
        tags: alert.tags,
        metadata: alert.metadata
      },
      channel: channel.name,
      escalationLevel
    };

    switch (channel.type) {
      case 'n8n_webhook':
        return await this.sendN8nWebhook(channel.config.webhookUrl, notificationData);
      
      case 'email':
        return await this.sendEmail(channel.config, notificationData);
      
      case 'slack':
        return await this.sendSlackMessage(channel.config, notificationData);
      
      case 'teams':
        return await this.sendTeamsMessage(channel.config, notificationData);
      
      default:
        console.warn(`Unknown notification channel type: ${channel.type}`);
        return false;
    }
  }

  /**
   * Send notification via n8n webhook
   */
  private async sendN8nWebhook(webhookUrl: string, data: any): Promise<boolean> {
    try {
      const response = await fetch(webhookUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
      });
      return response.ok;
    } catch (error) {
      console.error('Failed to send n8n webhook:', error);
      return false;
    }
  }

  /**
   * Send email notification (mock implementation)
   */
  private async sendEmail(config: any, data: any): Promise<boolean> {
    console.log(`Sending email notification to ${config.recipients}:`, data.alert.title);
    // In real implementation, integrate with email service
    return true;
  }

  /**
   * Send Slack message (mock implementation)
   */
  private async sendSlackMessage(config: any, data: any): Promise<boolean> {
    console.log(`Sending Slack notification to ${config.channel}:`, data.alert.title);
    // In real implementation, use Slack API
    return true;
  }

  /**
   * Send Teams message (mock implementation)
   */
  private async sendTeamsMessage(config: any, data: any): Promise<boolean> {
    console.log(`Sending Teams notification to ${config.channel}:`, data.alert.title);
    // In real implementation, use Teams API
    return true;
  }

  /**
   * Schedule escalations for an alert
   */
  private scheduleEscalations(alert: Alert, rules: AlertRule[]): void {
    for (const rule of rules) {
      for (const escalation of rule.escalationRules) {
        setTimeout(() => {
          this.processEscalation(alert.id, escalation);
        }, escalation.delayMinutes * 60 * 1000);
      }
    }
  }

  /**
   * Process alert escalation
   */
  private async processEscalation(alertId: string, escalation: EscalationRule): Promise<void> {
    const alert = this.alerts.get(alertId);
    if (!alert || alert.status !== 'active') {
      return; // Alert no longer active
    }

    // Check escalation condition if specified
    if (escalation.condition && !this.evaluateCondition(escalation.condition, alert)) {
      return;
    }

    alert.escalationLevel = escalation.level;

    // Send escalation notifications
    for (const channelId of escalation.channels) {
      await this.sendNotification(channelId, alert, escalation.level);
    }

    this.emit('alert:escalated', { alert, escalationLevel: escalation.level });
  }

  /**
   * Check channel rate limiting
   */
  private checkChannelRateLimit(channel: NotificationChannel): boolean {
    const now = new Date();
    const hoursSinceReset = (now.getTime() - channel.lastResetTime.getTime()) / (1000 * 60 * 60);
    
    if (hoursSinceReset >= 1) {
      channel.currentHourCount = 0;
      channel.lastResetTime = now;
    }

    return channel.currentHourCount < channel.rateLimitPerHour;
  }

  /**
   * Update channel usage statistics
   */
  private updateChannelUsage(channel: NotificationChannel): void {
    channel.currentHourCount++;
  }

  /**
   * Update alert metrics
   */
  private updateAlertMetrics(alert: Alert): void {
    this.alertMetrics.totalAlerts++;
    
    // Update severity counts
    this.alertMetrics.alertsBySeverity[alert.severity] = 
      (this.alertMetrics.alertsBySeverity[alert.severity] || 0) + 1;
    
    // Update category counts
    this.alertMetrics.alertsByCategory[alert.category] = 
      (this.alertMetrics.alertsByCategory[alert.category] || 0) + 1;
  }

  /**
   * Evaluate alert condition
   */
  private evaluateAlertCondition(condition: string, metrics: Record<string, any>): boolean {
    try {
      // Simple condition evaluation - in production, use a proper expression evaluator
      return Function('metrics', `return ${condition}`)(metrics);
    } catch (error) {
      console.error('Error evaluating alert condition:', error);
      return false;
    }
  }

  /**
   * Evaluate general condition
   */
  private evaluateCondition(condition: string, context: any): boolean {
    try {
      return Function('context', `return ${condition}`)(context);
    } catch (error) {
      console.error('Error evaluating condition:', error);
      return false;
    }
  }

  /**
   * Check if rule cooldown has expired
   */
  private isRuleCooldownExpired(rule: AlertRule): boolean {
    // Simplified cooldown check - in production, track per rule
    return true;
  }

  /**
   * Update rule cooldown
   */
  private updateRuleCooldown(rule: AlertRule): void {
    // Implementation would track rule firing times
  }

  /**
   * Initialize default alert rules
   */
  private initializeDefaultRules(): void {
    const defaultRules: AlertRule[] = [
      {
        id: 'high_error_rate',
        name: 'High Error Rate',
        description: 'Error rate exceeds 15%',
        category: 'performance',
        condition: 'metrics.errorRate > 0.15',
        severity: 'high',
        cooldownPeriod: 30,
        enabled: true,
        notificationChannels: ['default_webhook'],
        escalationRules: [
          {
            level: 1,
            delayMinutes: 15,
            channels: ['default_webhook'],
            recipients: ['ops-team']
          }
        ],
        suppressionRules: []
      },
      {
        id: 'queue_overflow',
        name: 'Queue Overflow',
        description: 'Processing queue exceeds capacity',
        category: 'system',
        condition: 'metrics.queueSize > 100',
        severity: 'critical',
        cooldownPeriod: 10,
        enabled: true,
        notificationChannels: ['default_webhook'],
        escalationRules: [
          {
            level: 1,
            delayMinutes: 5,
            channels: ['default_webhook'],
            recipients: ['ops-team', 'dev-team']
          }
        ],
        suppressionRules: []
      },
      {
        id: 'memory_pressure',
        name: 'High Memory Usage',
        description: 'Memory usage exceeds 90%',
        category: 'system',
        condition: 'metrics.memoryUsage > 0.9',
        severity: 'high',
        cooldownPeriod: 20,
        enabled: true,
        notificationChannels: ['default_webhook'],
        escalationRules: [],
        suppressionRules: []
      }
    ];

    for (const rule of defaultRules) {
      this.alertRules.set(rule.id, rule);
    }
  }

  /**
   * Setup default notification channels
   */
  private setupDefaultChannels(): void {
    const defaultChannels: NotificationChannel[] = [
      {
        id: 'default_webhook',
        name: 'Default n8n Webhook',
        type: 'n8n_webhook',
        config: {
          webhookUrl: process.env.N8N_ALERT_WEBHOOK_URL || 'http://localhost:5678/webhook/alerts'
        },
        enabled: true,
        rateLimitPerHour: 100,
        currentHourCount: 0,
        lastResetTime: new Date()
      }
    ];

    for (const channel of defaultChannels) {
      this.notificationChannels.set(channel.id, channel);
    }
  }

  /**
   * Start escalation monitoring
   */
  private startEscalationMonitor(): void {
    setInterval(() => {
      // Check for alerts that need escalation
      for (const alert of this.alerts.values()) {
        if (alert.status === 'active') {
          const alertAge = Date.now() - alert.timestamp.getTime();
          const alertAgeMinutes = alertAge / (1000 * 60);
          
          // Auto-escalate critical alerts after 30 minutes if not acknowledged
          if (alert.severity === 'critical' && alertAgeMinutes > 30 && alert.escalationLevel === 0) {
            this.processEscalation(alert.id, {
              level: 1,
              delayMinutes: 0,
              channels: ['default_webhook'],
              recipients: ['escalation-team']
            });
          }
        }
      }
    }, 5 * 60 * 1000); // Check every 5 minutes
  }

  /**
   * Start metrics calculation
   */
  private startMetricsCalculation(): void {
    setInterval(() => {
      this.calculateAdvancedMetrics();
    }, 15 * 60 * 1000); // Calculate every 15 minutes
  }

  /**
   * Calculate advanced alert metrics
   */
  private calculateAdvancedMetrics(): void {
    const now = Date.now();
    const last24h = now - (24 * 60 * 60 * 1000);
    
    const recentAlerts = this.alertHistory.filter(a => a.timestamp.getTime() > last24h);
    const resolvedAlerts = recentAlerts.filter(a => a.status === 'resolved' && a.resolvedAt);
    
    // Calculate average resolution time
    if (resolvedAlerts.length > 0) {
      const totalResolutionTime = resolvedAlerts.reduce((sum, alert) => {
        const resolutionTime = alert.resolvedAt!.getTime() - alert.timestamp.getTime();
        return sum + resolutionTime;
      }, 0);
      this.alertMetrics.averageResolutionTime = totalResolutionTime / resolvedAlerts.length;
    }

    // Calculate escalation rate
    const escalatedAlerts = recentAlerts.filter(a => a.escalationLevel > 0);
    this.alertMetrics.escalationRate = recentAlerts.length > 0 ? 
      escalatedAlerts.length / recentAlerts.length : 0;

    this.emit('metrics:calculated', this.alertMetrics);
  }

  /**
   * Load configuration from storage (mock implementation)
   */
  private async loadConfiguration(): Promise<void> {
    console.log('Loading alert manager configuration...');
    // In real implementation, load from database
  }

  /**
   * Save configuration to storage (mock implementation)
   */
  private async saveConfiguration(): Promise<void> {
    console.log('Saving alert manager configuration...');
    // In real implementation, save to database
  }
}
