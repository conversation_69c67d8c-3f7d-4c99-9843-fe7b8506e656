import { BaseService } from './base-service';
import { SupabaseService } from '../supabase-client';
import { ExtractionQueueItem } from '../config';

export interface AnalyticsConfig {
  name: string;
  metricsRetentionDays: number;
  aggregationIntervalMs: number;
  enableRealTimeMetrics: boolean;
  performanceThresholds: {
    processingTime: number; // ms
    successRate: number; // percentage
    queueSize: number;
  };
}

export interface ProcessingMetrics {
  itemId: string;
  documentType: 'acte' | 'index';
  documentNumber: string;
  startTime: Date;
  endTime?: Date;
  duration?: number;
  strategy?: 'exact_match' | 'intelligent_match' | 'exploratory';
  success: boolean;
  errorType?: string;
  retryCount: number;
  fileSize?: number;
  downloadTime?: number;
  queueWaitTime?: number;
}

export interface SystemMetrics {
  timestamp: Date;
  queueSize: number;
  activeDownloads: number;
  averageProcessingTime: number;
  successRate: number;
  memoryUsage?: number;
  cpuUsage?: number;
  errorRate: number;
}

export interface PerformanceInsights {
  bottlenecks: {
    type: 'queue' | 'processing' | 'download' | 'validation';
    severity: 'low' | 'medium' | 'high';
    description: string;
    recommendation: string;
  }[];
  trends: {
    metric: string;
    direction: 'increasing' | 'decreasing' | 'stable';
    changePercent: number;
    period: string;
  }[];
  predictions: {
    metric: string;
    predictedValue: number;
    confidence: number;
    timeframe: string;
  }[];
}

export class AnalyticsService extends BaseService {
  private supabaseService: SupabaseService;
  private analyticsConfig: AnalyticsConfig;
  private processingMetrics: Map<string, ProcessingMetrics> = new Map();
  private systemMetrics: SystemMetrics[] = [];
  private performanceHistory: Map<string, number[]> = new Map();

  constructor(config: AnalyticsConfig, supabaseService: SupabaseService) {
    super(config);
    this.analyticsConfig = config;
    this.supabaseService = supabaseService;
  }

  async initialize(): Promise<void> {
    console.log('Initializing Analytics Service...');
    
    // Load historical metrics if available
    await this.loadHistoricalMetrics();
    
    // Setup periodic metrics collection
    this.setupMetricsCollection();
    
    // Setup performance monitoring
    this.setupPerformanceMonitoring();
    
    console.log('Analytics Service initialized successfully');
  }

  async shutdown(): Promise<void> {
    console.log('Shutting down Analytics Service...');
    
    // Save current metrics
    await this.saveMetrics();
    
    console.log('Analytics Service shutdown complete');
  }

  async healthCheck(): Promise<boolean> {
    try {
      // Check if metrics collection is working
      const recentMetrics = this.getRecentSystemMetrics(5);
      
      // Health is good if we have recent metrics and no critical issues
      return recentMetrics.length > 0 && this.getSystemHealth() !== 'critical';
    } catch (error) {
      return false;
    }
  }

  /**
   * Track processing start for an item
   */
  trackProcessingStart(item: ExtractionQueueItem, queueWaitTime?: number): void {
    const metrics: ProcessingMetrics = {
      itemId: item.id,
      documentType: item.document_source,
      documentNumber: item.document_number,
      startTime: new Date(),
      success: false,
      retryCount: item.retry_count || 0,
      queueWaitTime
    };

    this.processingMetrics.set(item.id, metrics);
    
    this.emit('metrics:processing_started', {
      itemId: item.id,
      documentType: item.document_source,
      queueWaitTime
    });
  }

  /**
   * Track processing completion for an item
   */
  trackProcessingComplete(
    itemId: string, 
    success: boolean, 
    strategy?: string,
    errorType?: string,
    fileSize?: number,
    downloadTime?: number
  ): void {
    const metrics = this.processingMetrics.get(itemId);
    if (!metrics) {
      console.warn(`No processing metrics found for item ${itemId}`);
      return;
    }

    const endTime = new Date();
    metrics.endTime = endTime;
    metrics.duration = endTime.getTime() - metrics.startTime.getTime();
    metrics.success = success;
    metrics.strategy = strategy as any;
    metrics.errorType = errorType;
    metrics.fileSize = fileSize;
    metrics.downloadTime = downloadTime;

    // Store completed metrics for analysis
    this.storeCompletedMetrics(metrics);

    // Update performance history
    this.updatePerformanceHistory(metrics);

    // Remove from active tracking
    this.processingMetrics.delete(itemId);

    this.emit('metrics:processing_completed', {
      itemId,
      success,
      duration: metrics.duration,
      strategy
    });
  }

  /**
   * Record system metrics snapshot
   */
  recordSystemMetrics(data: Partial<SystemMetrics>): void {
    const metrics: SystemMetrics = {
      timestamp: new Date(),
      queueSize: data.queueSize || 0,
      activeDownloads: data.activeDownloads || 0,
      averageProcessingTime: data.averageProcessingTime || 0,
      successRate: data.successRate || 0,
      memoryUsage: data.memoryUsage,
      cpuUsage: data.cpuUsage,
      errorRate: data.errorRate || 0
    };

    this.systemMetrics.push(metrics);

    // Keep only recent metrics (last 24 hours)
    const cutoff = new Date(Date.now() - 24 * 60 * 60 * 1000);
    this.systemMetrics = this.systemMetrics.filter(m => m.timestamp > cutoff);

    this.emit('metrics:system_recorded', metrics);
  }

  /**
   * Get processing statistics
   */
  getProcessingStats(timeframe: 'hour' | 'day' | 'week' | 'month' = 'day'): {
    totalProcessed: number;
    successRate: number;
    averageProcessingTime: number;
    averageDownloadTime: number;
    byDocumentType: Record<string, any>;
    byStrategy: Record<string, any>;
    errorBreakdown: Record<string, number>;
  } {
    const cutoff = this.getTimeframeCutoff(timeframe);
    const recentMetrics = this.getCompletedMetrics().filter(m => m.startTime > cutoff);

    const stats = {
      totalProcessed: recentMetrics.length,
      successRate: recentMetrics.length > 0 ? 
        recentMetrics.filter(m => m.success).length / recentMetrics.length : 0,
      averageProcessingTime: this.calculateAverage(recentMetrics.map(m => m.duration || 0)),
      averageDownloadTime: this.calculateAverage(
        recentMetrics.map(m => m.downloadTime || 0).filter(t => t > 0)
      ),
      byDocumentType: this.groupByField(recentMetrics, 'documentType'),
      byStrategy: this.groupByField(recentMetrics, 'strategy'),
      errorBreakdown: this.groupByField(
        recentMetrics.filter(m => !m.success), 
        'errorType'
      )
    };

    return stats;
  }

  /**
   * Get performance insights and recommendations
   */
  getPerformanceInsights(): PerformanceInsights {
    const recentStats = this.getProcessingStats('day');
    const systemHealth = this.getSystemHealth();
    
    const insights: PerformanceInsights = {
      bottlenecks: [],
      trends: [],
      predictions: []
    };

    // Identify bottlenecks
    if (recentStats.successRate < this.analyticsConfig.performanceThresholds.successRate) {
      insights.bottlenecks.push({
        type: 'processing',
        severity: 'high',
        description: `Success rate is ${(recentStats.successRate * 100).toFixed(1)}%, below threshold of ${this.analyticsConfig.performanceThresholds.successRate}%`,
        recommendation: 'Review error patterns and optimize search strategies'
      });
    }

    if (recentStats.averageProcessingTime > this.analyticsConfig.performanceThresholds.processingTime) {
      insights.bottlenecks.push({
        type: 'processing',
        severity: 'medium',
        description: `Average processing time is ${recentStats.averageProcessingTime}ms, above threshold`,
        recommendation: 'Consider implementing parallel processing or optimizing strategies'
      });
    }

    // Calculate trends
    const trends = this.calculateTrends();
    insights.trends = trends;

    // Generate predictions
    const predictions = this.generatePredictions();
    insights.predictions = predictions;

    return insights;
  }

  /**
   * Get real-time dashboard data
   */
  getRealTimeDashboard(): {
    activeProcessing: number;
    queueSize: number;
    recentSuccessRate: number;
    averageProcessingTime: number;
    systemHealth: 'healthy' | 'degraded' | 'critical';
    recentErrors: { type: string; count: number }[];
    hourlyThroughput: number[];
  } {
    const recentStats = this.getProcessingStats('hour');
    const activeProcessing = this.processingMetrics.size;
    const recentSystemMetrics = this.getRecentSystemMetrics(1)[0];

    return {
      activeProcessing,
      queueSize: recentSystemMetrics?.queueSize || 0,
      recentSuccessRate: recentStats.successRate,
      averageProcessingTime: recentStats.averageProcessingTime,
      systemHealth: this.getSystemHealth(),
      recentErrors: Object.entries(recentStats.errorBreakdown)
        .map(([type, count]) => ({ type, count }))
        .sort((a, b) => b.count - a.count)
        .slice(0, 5),
      hourlyThroughput: this.getHourlyThroughput()
    };
  }

  /**
   * Export metrics for external analysis
   */
  exportMetrics(timeframe: 'day' | 'week' | 'month' = 'week'): {
    processingMetrics: ProcessingMetrics[];
    systemMetrics: SystemMetrics[];
    summary: any;
  } {
    const cutoff = this.getTimeframeCutoff(timeframe);
    
    return {
      processingMetrics: this.getCompletedMetrics().filter(m => m.startTime > cutoff),
      systemMetrics: this.systemMetrics.filter(m => m.timestamp > cutoff),
      summary: this.getProcessingStats(timeframe)
    };
  }

  /**
   * Private helper methods
   */
  private async loadHistoricalMetrics(): Promise<void> {
    // In a real implementation, this would load from database
    console.log('Loading historical metrics...');
  }

  private async saveMetrics(): Promise<void> {
    // In a real implementation, this would save to database
    console.log('Saving current metrics...');
  }

  private setupMetricsCollection(): void {
    setInterval(() => {
      // Collect system metrics periodically
      const metrics = this.collectSystemSnapshot();
      this.recordSystemMetrics(metrics);
    }, this.analyticsConfig.aggregationIntervalMs);
  }

  private setupPerformanceMonitoring(): void {
    setInterval(() => {
      const insights = this.getPerformanceInsights();
      
      // Emit critical issues
      const criticalBottlenecks = insights.bottlenecks.filter(b => b.severity === 'high');
      if (criticalBottlenecks.length > 0) {
        this.emit('analytics:critical_issues', criticalBottlenecks);
      }

      // Emit performance report
      this.emit('analytics:performance_report', {
        insights,
        dashboard: this.getRealTimeDashboard()
      });
    }, 60000); // Every minute
  }

  private collectSystemSnapshot(): Partial<SystemMetrics> {
    const recentStats = this.getProcessingStats('hour');
    
    return {
      queueSize: 0, // This would be provided by QueueManager
      activeDownloads: this.processingMetrics.size,
      averageProcessingTime: recentStats.averageProcessingTime,
      successRate: recentStats.successRate,
      errorRate: 1 - recentStats.successRate
    };
  }

  private getCompletedMetrics(): ProcessingMetrics[] {
    // In a real implementation, this would come from persistent storage
    return [];
  }

  private storeCompletedMetrics(metrics: ProcessingMetrics): void {
    // In a real implementation, this would persist to database
    console.log(`Storing metrics for ${metrics.itemId}`);
  }

  private updatePerformanceHistory(metrics: ProcessingMetrics): void {
    const key = `${metrics.documentType}_processing_time`;
    const history = this.performanceHistory.get(key) || [];
    history.push(metrics.duration || 0);
    
    // Keep only last 100 measurements
    if (history.length > 100) {
      history.shift();
    }
    
    this.performanceHistory.set(key, history);
  }

  private getRecentSystemMetrics(count: number): SystemMetrics[] {
    return this.systemMetrics.slice(-count);
  }

  private getSystemHealth(): 'healthy' | 'degraded' | 'critical' {
    const recentStats = this.getProcessingStats('hour');
    
    if (recentStats.successRate < 0.5) return 'critical';
    if (recentStats.successRate < 0.8) return 'degraded';
    return 'healthy';
  }

  private calculateTrends(): any[] {
    // Simple trend calculation - in real implementation would be more sophisticated
    return [
      {
        metric: 'success_rate',
        direction: 'stable' as const,
        changePercent: 0,
        period: 'last_24h'
      }
    ];
  }

  private generatePredictions(): any[] {
    // Simple predictions - in real implementation would use ML
    return [
      {
        metric: 'processing_time',
        predictedValue: 45000,
        confidence: 0.75,
        timeframe: 'next_hour'
      }
    ];
  }

  private getHourlyThroughput(): number[] {
    // Return throughput for last 24 hours
    const hours = Array.from({ length: 24 }, () => 0);
    // In real implementation, this would calculate actual throughput
    return hours;
  }

  private getTimeframeCutoff(timeframe: string): Date {
    const now = Date.now();
    switch (timeframe) {
      case 'hour': return new Date(now - 60 * 60 * 1000);
      case 'day': return new Date(now - 24 * 60 * 60 * 1000);
      case 'week': return new Date(now - 7 * 24 * 60 * 60 * 1000);
      case 'month': return new Date(now - 30 * 24 * 60 * 60 * 1000);
      default: return new Date(now - 24 * 60 * 60 * 1000);
    }
  }

  private calculateAverage(numbers: number[]): number {
    if (numbers.length === 0) return 0;
    return numbers.reduce((sum, n) => sum + n, 0) / numbers.length;
  }

  private groupByField(items: any[], field: string): Record<string, number> {
    const groups: Record<string, number> = {};
    for (const item of items) {
      const key = item[field] || 'unknown';
      groups[key] = (groups[key] || 0) + 1;
    }
    return groups;
  }
}
