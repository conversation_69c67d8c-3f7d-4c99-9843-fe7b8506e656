import { BaseService, ServiceConfig } from './base-service';
import { Stagehand } from '@browserbasehq/stagehand';
import { CONFIG } from '../config';
import { StagehandPage } from '../types';
import { CacheManager } from './cache-manager';

export interface SessionConfig extends ServiceConfig {
  maxSessionAge: number; // Max session age in milliseconds
  keepAliveInterval: number; // Keep-alive ping interval
  healthCheckInterval: number; // Health check frequency
  sessionTimeout: number; // Session timeout detection
  maxRetryAttempts: number; // Max recovery attempts
  gracefulHandoverTimeout: number; // Timeout for graceful handover
  sessionBackupInterval: number; // Session state backup frequency
}

export interface SessionState {
  id: string;
  createdAt: Date;
  lastActivity: Date;
  lastHealthCheck: Date;
  isAuthenticated: boolean;
  currentUrl: string;
  cookies: any[];
  localStorage: Record<string, string>;
  sessionStorage: Record<string, string>;
  userAgent: string;
  viewport: { width: number; height: number };
  healthScore: number; // 0-100 health score
  errorCount: number;
  recoveryCount: number;
  isActive: boolean;
  lockHolder?: string; // Worker ID holding the session
  lockExpiry?: Date;
}

export interface SessionLock {
  sessionId: string;
  workerId: string;
  expiresAt: Date;
  operation: string;
}

export class SessionManager extends BaseService {
  private sessionConfig: SessionConfig;
  private stagehand?: Stagehand;
  private page?: StagehandPage;
  private currentSession?: SessionState;
  private cacheManager?: CacheManager;
  private keepAliveTimer?: NodeJS.Timeout;
  private healthCheckTimer?: NodeJS.Timeout;
  private backupTimer?: NodeJS.Timeout;
  private isShuttingDown = false;
  private sessionLocks = new Map<string, SessionLock>();

  constructor(config: SessionConfig) {
    super(config);
    this.sessionConfig = config;
  }

  async initialize(): Promise<void> {
    console.log('Initializing session manager', { config: this.sessionConfig });

    // Initialize cache manager for session state persistence
    if (this.dependencies.has('CacheManager')) {
      this.cacheManager = this.dependencies.get('CacheManager') as CacheManager;
    }

    // Try to restore existing session
    await this.restoreSession();

    // Start monitoring timers
    this.startKeepAlive();
    this.startHealthChecking();
    this.startSessionBackup();

    console.log('Session manager initialized');
  }

  async shutdown(): Promise<void> {
    this.isShuttingDown = true;
    console.log('Shutting down session manager');

    // Stop all timers
    if (this.keepAliveTimer) clearInterval(this.keepAliveTimer);
    if (this.healthCheckTimer) clearInterval(this.healthCheckTimer);
    if (this.backupTimer) clearInterval(this.backupTimer);

    // Save session state before shutdown
    if (this.currentSession) {
      await this.backupSessionState();
    }

    // Close browser
    if (this.stagehand) {
      await this.stagehand.close();
    }

    console.log('Session manager shutdown complete');
  }

  async healthCheck(): Promise<boolean> {
    try {
      if (!this.currentSession || !this.page) {
        return false;
      }

      // Check if page is responsive
      const startTime = Date.now();
      await this.page.evaluate('document.readyState');
      const responseTime = Date.now() - startTime;

      // Update health score based on response time
      const healthScore = Math.max(0, 100 - (responseTime / 10));
      this.currentSession.healthScore = healthScore;
      this.currentSession.lastHealthCheck = new Date();

      return healthScore > 50;
    } catch (error) {
      this.handleError(error, 'health_check');
      if (this.currentSession) {
        this.currentSession.healthScore = 0;
        this.currentSession.errorCount++;
      }
      return false;
    }
  }

  async getSession(): Promise<{ stagehand: Stagehand; page: StagehandPage } | null> {
    if (!this.currentSession || !this.stagehand || !this.page) {
      return null;
    }

    // Validate session health
    const isHealthy = await this.validateSession();
    if (!isHealthy) {
      console.log('Session unhealthy, attempting recovery...');
      const recovered = await this.recoverSession();
      if (!recovered) {
        return null;
      }
    }

    // Update last activity
    this.currentSession.lastActivity = new Date();
    
    return {
      stagehand: this.stagehand,
      page: this.page!
    };
  }

  async acquireSessionLock(workerId: string, operation: string, timeout: number = 30000): Promise<boolean> {
    if (!this.currentSession) {
      return false;
    }

    const sessionId = this.currentSession.id;
    const existingLock = this.sessionLocks.get(sessionId);
    
    // Check if lock is expired
    if (existingLock && existingLock.expiresAt > new Date()) {
      if (existingLock.workerId === workerId) {
        // Extend existing lock
        existingLock.expiresAt = new Date(Date.now() + timeout);
        return true;
      }
      return false; // Lock held by another worker
    }

    // Acquire new lock
    const lock: SessionLock = {
      sessionId,
      workerId,
      expiresAt: new Date(Date.now() + timeout),
      operation
    };

    this.sessionLocks.set(sessionId, lock);
    this.currentSession.lockHolder = workerId;
    this.currentSession.lockExpiry = lock.expiresAt;

    console.log(`Session lock acquired by worker ${workerId} for ${operation}`);
    return true;
  }

  async releaseSessionLock(workerId: string): Promise<void> {
    if (!this.currentSession) {
      return;
    }

    const sessionId = this.currentSession.id;
    const lock = this.sessionLocks.get(sessionId);

    if (lock && lock.workerId === workerId) {
      this.sessionLocks.delete(sessionId);
      this.currentSession.lockHolder = undefined;
      this.currentSession.lockExpiry = undefined;
      console.log(`Session lock released by worker ${workerId}`);
    }
  }

  async createNewSession(): Promise<boolean> {
    try {
      console.log('Creating new browser session...');

      // Close existing session if any
      if (this.stagehand) {
        await this.stagehand.close();
      }

      // Create new Stagehand instance
      this.stagehand = new Stagehand({
        env: CONFIG.HEADLESS ? 'BROWSERBASE' : 'LOCAL',
        modelName: CONFIG.MODEL_NAME as any,
        modelClientOptions: CONFIG.MODEL_API_KEY ? { apiKey: CONFIG.MODEL_API_KEY } : undefined,
        enableCaching: CONFIG.ENABLE_CACHING,
        selfHeal: CONFIG.SELF_HEAL,
        verbose: (CONFIG.VERBOSE as 0 | 1 | 2) || 1,
        domSettleTimeoutMs: CONFIG.DOM_SETTLE_TIMEOUT,
        localBrowserLaunchOptions: {
          headless: CONFIG.HEADLESS,
          downloadsPath: CONFIG.BASE_DOWNLOAD_DIR,
          viewport: { width: 1280, height: 720 }
        }
      });

      await this.stagehand.init();
      this.page = this.stagehand.page as unknown as StagehandPage;

      // Perform authentication
      const authSuccess = await this.performAuthentication();
      if (!authSuccess) {
        throw new Error('Authentication failed');
      }

      // Create session state
      this.currentSession = {
        id: `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        createdAt: new Date(),
        lastActivity: new Date(),
        lastHealthCheck: new Date(),
        isAuthenticated: true,
        currentUrl: await this.page.url(),
        cookies: [],
        localStorage: {},
        sessionStorage: {},
        userAgent: await this.page.evaluate('navigator.userAgent'),
        viewport: { width: 1280, height: 720 },
        healthScore: 100,
        errorCount: 0,
        recoveryCount: 0,
        isActive: true
      };

      // Capture initial session state
      await this.captureSessionState();

      console.log(`New session created: ${this.currentSession.id}`);
      return true;

    } catch (error) {
      console.error('Failed to create new session:', error);
      this.handleError(error, 'create_session');
      return false;
    }
  }

  private async performAuthentication(): Promise<boolean> {
    try {
      if (!this.page) return false;

      console.log('Performing authentication...');

      // Navigate to the main page first
      await this.page.goto(CONFIG.BASE_URL);
      await this.page.waitForTimeout(1000);
      
      // Click on the first 'Entrée du site' link (element 0)
      await this.page.act("click on the link with href '/Sirf/pf_acces.asp'");
      await this.page.waitForTimeout(2000);
      
      // Now we should be on the login page - fill the form
      await this.page.act(`type '${CONFIG.USER_CODE}' in the 'Code d'utilisateur' input field`);
      await this.page.act(`type '${CONFIG.PASSWORD}' in the 'Mot de passe' input field`);
      await this.page.act("click the 'Soumettre' button");
      
      // Wait for login to complete
      await this.page.waitForTimeout(3000);
      
      // Verify authentication success
      const currentUrl = await this.page.url();
      const isAuthPage = currentUrl.includes('pf_acces.asp');
      
      if (isAuthPage) {
        console.error('Authentication failed - still on login page');
        return false;
      }

      console.log('Authentication successful');
      return true;

    } catch (error) {
      console.error('Authentication error:', error);
      return false;
    }
  }

  private async validateSession(): Promise<boolean> {
    if (!this.currentSession || !this.page) {
      return false;
    }

    try {
      // Check session age
      const sessionAge = Date.now() - this.currentSession.createdAt.getTime();
      if (sessionAge > this.sessionConfig.maxSessionAge) {
        console.log('Session expired due to age');
        return false;
      }

      // Check if page is responsive
      const startTime = Date.now();
      await this.page.evaluate('document.title');
      const responseTime = Date.now() - startTime;

      if (responseTime > this.sessionConfig.sessionTimeout) {
        console.log('Session timeout detected');
        return false;
      }

      // Check if still authenticated
      const currentUrl = await this.page.url();
      if (currentUrl.includes('pf_acces.asp') || currentUrl.includes('login')) {
        console.log('Session lost - redirected to login');
        this.currentSession.isAuthenticated = false;
        return false;
      }

      // Update health metrics
      this.currentSession.healthScore = Math.max(0, 100 - (responseTime / 10));
      this.currentSession.lastHealthCheck = new Date();

      return this.currentSession.healthScore > 30;

    } catch (error) {
      console.error('Session validation error:', error);
      if (this.currentSession) {
        this.currentSession.errorCount++;
        this.currentSession.healthScore = 0;
      }
      return false;
    }
  }

  private async recoverSession(): Promise<boolean> {
    if (!this.currentSession) {
      return await this.createNewSession();
    }

    console.log(`Attempting session recovery (attempt ${this.currentSession.recoveryCount + 1})`);

    if (this.currentSession.recoveryCount >= this.sessionConfig.maxRetryAttempts) {
      console.log('Max recovery attempts reached, creating new session');
      return await this.createNewSession();
    }

    try {
      this.currentSession.recoveryCount++;

      // Progressive recovery strategies
      if (this.currentSession.recoveryCount === 1) {
        // Strategy 1: Simple page refresh
        console.log('Recovery strategy 1: Page refresh');
        if (this.page) {
          await this.page.reload();
          await this.page.waitForTimeout(3000);
        }
      } else if (this.currentSession.recoveryCount === 2) {
        // Strategy 2: Navigate back to main page
        console.log('Recovery strategy 2: Navigate to main page');
        if (this.page) {
          await this.page.goto(CONFIG.BASE_URL);
          await this.page.waitForTimeout(3000);
        }
      } else if (this.currentSession.recoveryCount === 3) {
        // Strategy 3: Re-authenticate
        console.log('Recovery strategy 3: Re-authenticate');
        const authSuccess = await this.performAuthentication();
        if (!authSuccess) {
          throw new Error('Re-authentication failed');
        }
        this.currentSession.isAuthenticated = true;
      }

      // Validate recovery
      const isValid = await this.validateSession();
      if (isValid) {
        console.log('Session recovery successful');
        this.currentSession.recoveryCount = 0; // Reset recovery count on success
        this.currentSession.errorCount = Math.max(0, this.currentSession.errorCount - 1);
        return true;
      }

      return false;

    } catch (error) {
      console.error('Session recovery failed:', error);
      return false;
    }
  }

  private async restoreSession(): Promise<boolean> {
    try {
      if (!this.cacheManager) {
        return false;
      }

      const sessionData = await this.cacheManager.getSessionData('current_session');
      if (!sessionData) {
        return false;
      }

      // Check if session is still valid
      const sessionAge = Date.now() - new Date(sessionData.createdAt).getTime();
      if (sessionAge > this.sessionConfig.maxSessionAge) {
        console.log('Stored session expired, creating new one');
        return false;
      }

      console.log('Attempting to restore previous session...');
      
      // Try to restore browser state
      // Note: This is a simplified restoration - in practice, you might need
      // to restore cookies, localStorage, etc.
      const sessionCreated = await this.createNewSession();
      if (sessionCreated && this.currentSession) {
        // Restore session metadata
        this.currentSession.errorCount = sessionData.errorCount || 0;
        this.currentSession.recoveryCount = sessionData.recoveryCount || 0;
        console.log('Session restored successfully');
        return true;
      }

      return false;

    } catch (error) {
      console.error('Failed to restore session:', error);
      return false;
    }
  }

  private async captureSessionState(): Promise<void> {
    if (!this.currentSession || !this.page) {
      return;
    }

    try {
      // Capture cookies
      this.currentSession.cookies = await this.page.context().cookies();

      // Capture local storage
      this.currentSession.localStorage = await this.page.evaluate(`
        (() => {
          const storage = {};
          for (let i = 0; i < localStorage.length; i++) {
            const key = localStorage.key(i);
            if (key) {
              storage[key] = localStorage.getItem(key) || '';
            }
          }
          return storage;
        })()
      `);

      // Capture session storage
      this.currentSession.sessionStorage = await this.page.evaluate(`
        (() => {
          const storage = {};
          for (let i = 0; i < sessionStorage.length; i++) {
            const key = sessionStorage.key(i);
            if (key) {
              storage[key] = sessionStorage.getItem(key) || '';
            }
          }
          return storage;
        })()
      `);

      // Update current URL
      this.currentSession.currentUrl = await this.page.url();

    } catch (error) {
      console.error('Failed to capture session state:', error);
    }
  }

  private async backupSessionState(): Promise<void> {
    if (!this.currentSession || !this.cacheManager) {
      return;
    }

    try {
      await this.captureSessionState();
      await this.cacheManager.cacheSessionData('current_session', this.currentSession);
    } catch (error) {
      console.error('Failed to backup session state:', error);
    }
  }

  private startKeepAlive(): void {
    this.keepAliveTimer = setInterval(async () => {
      if (this.isShuttingDown || !this.currentSession || !this.page) {
        return;
      }

      try {
        // Simple keep-alive ping
        await this.page.evaluate('document.readyState');
        this.currentSession!.lastActivity = new Date();
      } catch (error) {
        console.error('Keep-alive failed:', error);
        if (this.currentSession) {
          this.currentSession.errorCount++;
        }
      }
    }, this.sessionConfig.keepAliveInterval);
  }

  private startHealthChecking(): void {
    this.healthCheckTimer = setInterval(async () => {
      if (this.isShuttingDown) {
        return;
      }

      const isHealthy = await this.healthCheck();
      if (!isHealthy && this.currentSession) {
        console.log('Session unhealthy, may need recovery');
        // Could trigger automatic recovery here if desired
      }
    }, this.sessionConfig.healthCheckInterval);
  }

  private startSessionBackup(): void {
    this.backupTimer = setInterval(async () => {
      if (this.isShuttingDown) {
        return;
      }

      await this.backupSessionState();
    }, this.sessionConfig.sessionBackupInterval);
  }

  // Public API for session statistics
  getSessionStatistics(): {
    sessionId?: string;
    age: number;
    healthScore: number;
    errorCount: number;
    recoveryCount: number;
    isAuthenticated: boolean;
    lastActivity: Date;
    isLocked: boolean;
    lockHolder?: string;
  } | null {
    if (!this.currentSession) {
      return null;
    }

    return {
      sessionId: this.currentSession.id,
      age: Date.now() - this.currentSession.createdAt.getTime(),
      healthScore: this.currentSession.healthScore,
      errorCount: this.currentSession.errorCount,
      recoveryCount: this.currentSession.recoveryCount,
      isAuthenticated: this.currentSession.isAuthenticated,
      lastActivity: this.currentSession.lastActivity,
      isLocked: !!this.currentSession.lockHolder,
      lockHolder: this.currentSession.lockHolder
    };
  }

  async forceSessionReset(): Promise<boolean> {
    console.log('Forcing session reset...');
    
    if (this.currentSession) {
      this.currentSession.recoveryCount = 0;
      this.currentSession.errorCount = 0;
    }

    return await this.createNewSession();
  }
}
