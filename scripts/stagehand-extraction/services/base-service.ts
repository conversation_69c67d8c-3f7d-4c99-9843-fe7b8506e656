import { EventEmitter } from 'events';

export interface ServiceConfig {
  name: string;
  dependencies?: string[];
  retryAttempts?: number;
  retryDelay?: number;
}

export interface ServiceHealth {
  status: 'healthy' | 'degraded' | 'unhealthy';
  lastCheck: Date;
  errors: number;
  uptime: number;
}

export abstract class BaseService extends EventEmitter {
  protected config: ServiceConfig;
  protected health: ServiceHealth;
  protected startTime: Date;
  protected dependencies: Map<string, BaseService> = new Map();

  constructor(config: ServiceConfig) {
    super();
    this.config = config;
    this.startTime = new Date();
    this.health = {
      status: 'healthy',
      lastCheck: new Date(),
      errors: 0,
      uptime: 0
    };

    // Set up health monitoring
    this.setupHealthMonitoring();
  }

  /**
   * Abstract methods that must be implemented by concrete services
   */
  abstract initialize(): Promise<void>;
  abstract shutdown(): Promise<void>;
  abstract healthCheck(): Promise<boolean>;

  /**
   * Start the service
   */
  async start(): Promise<void> {
    try {
      console.log(`Starting service: ${this.config.name}`);
      await this.initialize();
      this.emit('service:started', { name: this.config.name });
      console.log(`Service started successfully: ${this.config.name}`);
    } catch (error) {
      this.handleError(error);
      throw error;
    }
  }

  /**
   * Stop the service
   */
  async stop(): Promise<void> {
    try {
      console.log(`Stopping service: ${this.config.name}`);
      await this.shutdown();
      this.emit('service:stopped', { name: this.config.name });
      console.log(`Service stopped successfully: ${this.config.name}`);
    } catch (error) {
      this.handleError(error);
      throw error;
    }
  }

  /**
   * Get service health status
   */
  getHealth(): ServiceHealth {
    this.health.uptime = Date.now() - this.startTime.getTime();
    this.health.lastCheck = new Date();
    return { ...this.health };
  }

  /**
   * Register a dependency service
   */
  addDependency(name: string, service: BaseService): void {
    this.dependencies.set(name, service);
    service.on('service:error', (error) => {
      this.emit('dependency:error', { dependency: name, error });
    });
  }

  /**
   * Execute with retry logic
   */
  protected async executeWithRetry<T>(
    operation: () => Promise<T>,
    context: string
  ): Promise<T> {
    const maxAttempts = this.config.retryAttempts || 3;
    const retryDelay = this.config.retryDelay || 1000;

    for (let attempt = 1; attempt <= maxAttempts; attempt++) {
      try {
        return await operation();
      } catch (error) {
        if (attempt === maxAttempts) {
          this.handleError(error, context);
          throw error;
        }

        console.warn(`${this.config.name}: Attempt ${attempt}/${maxAttempts} failed for ${context}. Retrying in ${retryDelay}ms...`);
        await this.delay(retryDelay * attempt); // Exponential backoff
      }
    }

    throw new Error('Max retry attempts exceeded');
  }

  /**
   * Handle service errors
   */
  protected handleError(error: any, context?: string): void {
    this.health.errors++;
    this.health.status = this.health.errors > 5 ? 'unhealthy' : 'degraded';

    const errorInfo = {
      service: this.config.name,
      context: context || 'unknown',
      error: error instanceof Error ? error.message : String(error),
      timestamp: new Date()
    };

    console.error(`Service error in ${this.config.name}:`, errorInfo);
    this.emit('service:error', errorInfo);
  }

  /**
   * Setup health monitoring
   */
  private setupHealthMonitoring(): void {
    setInterval(async () => {
      try {
        const isHealthy = await this.healthCheck();
        if (isHealthy && this.health.status !== 'healthy') {
          this.health.status = 'healthy';
          this.health.errors = Math.max(0, this.health.errors - 1);
          this.emit('service:recovered', { name: this.config.name });
        }
      } catch (error) {
        this.handleError(error, 'health_check');
      }
    }, 30000); // Check every 30 seconds
  }

  /**
   * Utility method for delays
   */
  protected delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}
