import { BaseService } from './base-service';
import { ActeSearchParams, IndexSearchParams, ExtractionQueueItem } from '../config';
import { StagehandPage } from '../types';
import { ExactMatchStrategy } from '../strategies/exact-match';
import { IntelligentMatchStrategy } from '../strategies/intelligent-match';
import { ExploratoryStrategy } from '../strategies/exploratory';
import { ErrorLogger } from '../utils/error-logger';
import { SupabaseService } from '../supabase-client';
import { z } from 'zod';

export interface DocumentDiscoveryConfig {
  name: string;
  maxRetryAttempts: number;
  retryDelay: number;
  strategyTimeout: number;
}

export interface SearchResult {
  success: boolean;
  strategy: 'exact_match' | 'intelligent_match' | 'exploratory';
  error?: string;
  hasResults: boolean;
  pageState?: string;
}

export class DocumentDiscoveryService extends BaseService {
  private exactMatchStrategy: ExactMatchStrategy;
  private intelligentMatchStrategy: IntelligentMatchStrategy;
  private exploratoryStrategy: ExploratoryStrategy;
  private errorLogger: ErrorLogger;
  private supabaseService: SupabaseService;
  private discoveryConfig: DocumentDiscoveryConfig;

  constructor(config: DocumentDiscoveryConfig, supabaseService: SupabaseService) {
    super(config);
    this.discoveryConfig = config;
    this.supabaseService = supabaseService;
    this.errorLogger = new ErrorLogger(supabaseService);
    
    // Initialize search strategies
    this.exactMatchStrategy = new ExactMatchStrategy(this.errorLogger);
    this.intelligentMatchStrategy = new IntelligentMatchStrategy(this.errorLogger);
    this.exploratoryStrategy = new ExploratoryStrategy(this.errorLogger);
  }

  async initialize(): Promise<void> {
    console.log('Initializing Document Discovery Service...');
    
    // Initialize strategies if needed
    // Set up strategy performance monitoring
    this.setupStrategyMonitoring();
    
    console.log('Document Discovery Service initialized successfully');
  }

  async shutdown(): Promise<void> {
    console.log('Shutting down Document Discovery Service...');
    // Clean up any resources
    console.log('Document Discovery Service shutdown complete');
  }

  async healthCheck(): Promise<boolean> {
    try {
      // Check if strategies are functioning
      return true;
    } catch (error) {
      return false;
    }
  }

  /**
   * Discover and search for an Acte document using all available strategies
   */
  async discoverActeDocument(
    page: StagehandPage,
    item: ExtractionQueueItem
  ): Promise<SearchResult> {
    const params: ActeSearchParams = {
      inscriptionNumber: item.document_number,
      circonscription: item.circonscription_fonciere,
      acteType: item.acte_type
    };

    console.log(`Starting Acte document discovery for ${item.document_number}`);
    this.emit('discovery:started', { 
      itemId: item.id, 
      documentType: 'acte', 
      documentNumber: item.document_number 
    });

    // Strategy 1: Exact Match
    const exactResult = await this.executeWithRetry(
      () => this.tryExactMatchStrategy(page, params, item),
      'exact_match_acte'
    );

    if (exactResult.success) {
      this.emit('discovery:success', { 
        itemId: item.id, 
        strategy: 'exact_match',
        documentType: 'acte'
      });
      return exactResult;
    }

    // Strategy 2: Intelligent Match
    await this.resetToSearchPage(page, 'acte');
    const intelligentResult = await this.executeWithRetry(
      () => this.tryIntelligentMatchStrategy(page, params, item),
      'intelligent_match_acte'
    );

    if (intelligentResult.success) {
      this.emit('discovery:success', { 
        itemId: item.id, 
        strategy: 'intelligent_match',
        documentType: 'acte'
      });
      return intelligentResult;
    }

    // Strategy 3: Exploratory
    await this.resetToSearchPage(page, 'acte');
    const exploratoryResult = await this.executeWithRetry(
      () => this.tryExploratoryStrategy(page, params, item),
      'exploratory_acte'
    );

    if (exploratoryResult.success) {
      this.emit('discovery:success', { 
        itemId: item.id, 
        strategy: 'exploratory',
        documentType: 'acte'
      });
      return exploratoryResult;
    }

    // All strategies failed
    this.emit('discovery:failed', { 
      itemId: item.id, 
      documentType: 'acte',
      error: 'All strategies failed'
    });

    return {
      success: false,
      strategy: 'exploratory',
      error: 'Document not found after trying all strategies',
      hasResults: false
    };
  }

  /**
   * Discover and search for an Index document using all available strategies
   */
  async discoverIndexDocument(
    page: StagehandPage,
    item: ExtractionQueueItem
  ): Promise<SearchResult> {
    const params: IndexSearchParams = {
      lotNumber: item.document_number,
      circonscription: item.circonscription_fonciere,
      cadastre: item.cadastre,
      designationSecondaire: item.designation_secondaire
    };

    console.log(`Starting Index document discovery for ${item.document_number}`);
    this.emit('discovery:started', { 
      itemId: item.id, 
      documentType: 'index', 
      documentNumber: item.document_number 
    });

    // Strategy 1: Exact Match
    const exactResult = await this.executeWithRetry(
      () => this.tryExactMatchStrategy(page, params, item),
      'exact_match_index'
    );

    if (exactResult.success) {
      this.emit('discovery:success', { 
        itemId: item.id, 
        strategy: 'exact_match',
        documentType: 'index'
      });
      return exactResult;
    }

    // Strategy 2: Intelligent Match
    await this.resetToSearchPage(page, 'index');
    const intelligentResult = await this.executeWithRetry(
      () => this.tryIntelligentMatchStrategy(page, params, item),
      'intelligent_match_index'
    );

    if (intelligentResult.success) {
      this.emit('discovery:success', { 
        itemId: item.id, 
        strategy: 'intelligent_match',
        documentType: 'index'
      });
      return intelligentResult;
    }

    // Strategy 3: Exploratory
    await this.resetToSearchPage(page, 'index');
    const exploratoryResult = await this.executeWithRetry(
      () => this.tryExploratoryStrategy(page, params, item),
      'exploratory_index'
    );

    if (exploratoryResult.success) {
      this.emit('discovery:success', { 
        itemId: item.id, 
        strategy: 'exploratory',
        documentType: 'index'
      });
      return exploratoryResult;
    }

    // All strategies failed
    this.emit('discovery:failed', { 
      itemId: item.id, 
      documentType: 'index',
      error: 'All strategies failed'
    });

    return {
      success: false,
      strategy: 'exploratory',
      error: 'Document not found after trying all strategies',
      hasResults: false
    };
  }

  /**
   * Get discovery statistics
   */
  getDiscoveryStats(): {
    strategiesUsed: Record<string, number>;
    successRates: Record<string, number>;
    averageDiscoveryTime: number;
  } {
    // This would be implemented with actual tracking
    return {
      strategiesUsed: {
        exact_match: 0,
        intelligent_match: 0,
        exploratory: 0
      },
      successRates: {
        exact_match: 0.85,
        intelligent_match: 0.65,
        exploratory: 0.45
      },
      averageDiscoveryTime: 45000 // ms
    };
  }

  /**
   * Try exact match strategy
   */
  private async tryExactMatchStrategy(
    page: StagehandPage,
    params: ActeSearchParams | IndexSearchParams,
    item: ExtractionQueueItem
  ): Promise<SearchResult> {
    try {
      if ('inscriptionNumber' in params) {
        // Acte search
        await this.exactMatchStrategy.executeActeSearch(page, params as ActeSearchParams, item.id);
      } else {
        // Index search
        await this.exactMatchStrategy.executeIndexSearch(page, params as IndexSearchParams, item.id);
      }
      
      await page.waitForTimeout(3000);
      
      const hasResults = await this.exactMatchStrategy.verifyResultsPage(page, item.document_number);
      
      return {
        success: hasResults,
        strategy: 'exact_match',
        hasResults,
        error: hasResults ? undefined : 'No results found with exact match'
      };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      
      // Log error for analysis
      const errorLog = this.errorLogger.createErrorLog(
        item.id, 
        item.document_number, 
        1, 
        'exact_match', 
        error instanceof Error ? error : new Error(errorMessage)
      );
      await this.errorLogger.logError(errorLog, page);
      
      return {
        success: false,
        strategy: 'exact_match',
        hasResults: false,
        error: errorMessage
      };
    }
  }

  /**
   * Try intelligent match strategy
   */
  private async tryIntelligentMatchStrategy(
    page: StagehandPage,
    params: ActeSearchParams | IndexSearchParams,
    item: ExtractionQueueItem
  ): Promise<SearchResult> {
    try {
      if ('inscriptionNumber' in params) {
        // Acte search
        await this.intelligentMatchStrategy.executeActeSearch(page, params as ActeSearchParams, item.id);
      } else {
        // Index search
        await this.intelligentMatchStrategy.executeIndexSearch(page, params as IndexSearchParams, item.id);
      }
      
      await page.waitForTimeout(3000);
      
      const hasResults = await this.checkForResults(page);
      
      return {
        success: hasResults,
        strategy: 'intelligent_match',
        hasResults,
        error: hasResults ? undefined : 'No results found with intelligent match'
      };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      
      // Log error for analysis
      const errorLog = this.errorLogger.createErrorLog(
        item.id, 
        item.document_number, 
        2, 
        'intelligent_match', 
        error instanceof Error ? error : new Error(errorMessage)
      );
      await this.errorLogger.logError(errorLog, page);
      
      return {
        success: false,
        strategy: 'intelligent_match',
        hasResults: false,
        error: errorMessage
      };
    }
  }

  /**
   * Try exploratory strategy
   */
  private async tryExploratoryStrategy(
    page: StagehandPage,
    params: ActeSearchParams | IndexSearchParams,
    item: ExtractionQueueItem
  ): Promise<SearchResult> {
    try {
      if ('inscriptionNumber' in params) {
        // Acte search
        await this.exploratoryStrategy.executeActeSearch(page, params as ActeSearchParams, item.id);
      } else {
        // Index search
        await this.exploratoryStrategy.executeIndexSearch(page, params as IndexSearchParams, item.id);
      }
      
      await page.waitForTimeout(3000);
      
      const hasResults = await this.checkForResults(page);
      
      return {
        success: hasResults,
        strategy: 'exploratory',
        hasResults,
        error: hasResults ? undefined : 'No results found with exploratory search'
      };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      
      // Log error for analysis
      const errorLog = this.errorLogger.createErrorLog(
        item.id, 
        item.document_number, 
        3, 
        'exploratory', 
        error instanceof Error ? error : new Error(errorMessage)
      );
      await this.errorLogger.logError(errorLog, page);
      
      return {
        success: false,
        strategy: 'exploratory',
        hasResults: false,
        error: errorMessage
      };
    }
  }

  /**
   * Reset page to appropriate search page
   */
  private async resetToSearchPage(page: StagehandPage, documentType: 'acte' | 'index'): Promise<void> {
    const url = documentType === 'acte' 
      ? 'https://www.registrefoncier.gouv.qc.ca/Sirf/Script/13_01_11/pf_13_01_11_08_reqst.asp'
      : 'https://www.registrefoncier.gouv.qc.ca/Sirf/Script/13_01_11/pf_13_01_11_02_indx_immbl.asp';
    
    await page.goto(url);
    await page.waitForTimeout(2000);
  }

  /**
   * Check if results are available on the page
   */
  private async checkForResults(page: StagehandPage): Promise<boolean> {
    try {
      // This is the same logic from the original extraction processor
      const errorCheck = await page.extract({
        instruction: "Check for error messages like 'Aucun document', 'introuvable', 'n'existe pas', 'Aucun lot', 'Erreur', or 'Invalid'",
        schema: z.object({
          hasError: z.boolean(),
          errorMessage: z.string().optional()
        })
      });

      if (errorCheck.hasError) {
        console.log(`Document not found: ${errorCheck.errorMessage}`);
        return false;
      }

      const resultsCheck = await page.extract({
        instruction: "Check if there are frames (page, frmNavgt) or an 'Imprimer' button, which indicate successful results",
        schema: z.object({
          hasFrames: z.boolean(),
          hasPrintButton: z.boolean(),
          hasResults: z.boolean()
        })
      });

      return resultsCheck.hasFrames || resultsCheck.hasPrintButton || resultsCheck.hasResults;
    } catch (error) {
      return false;
    }
  }

  /**
   * Setup strategy performance monitoring
   */
  private setupStrategyMonitoring(): void {
    // Monitor strategy performance and emit metrics
    setInterval(() => {
      const stats = this.getDiscoveryStats();
      this.emit('strategy:performance', stats);
    }, 60000); // Every minute
  }
}
