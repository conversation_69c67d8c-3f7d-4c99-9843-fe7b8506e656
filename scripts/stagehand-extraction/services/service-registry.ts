import { EventEmitter } from 'events';
import { BaseService, ServiceConfig, ServiceHealth } from './base-service';

export interface RegistryConfig {
  maxStartupTime: number;
  healthCheckInterval: number;
}

export class ServiceRegistry extends EventEmitter {
  private services: Map<string, BaseService> = new Map();
  private serviceConfigs: Map<string, ServiceConfig> = new Map();
  private startupOrder: string[] = [];
  private config: RegistryConfig;

  constructor(config: RegistryConfig = { maxStartupTime: 30000, healthCheckInterval: 60000 }) {
    super();
    this.config = config;
    this.setupGlobalHealthMonitoring();
  }

  /**
   * Register a service with the registry
   */
  register<T extends BaseService>(
    name: string,
    serviceFactory: (config: ServiceConfig) => T,
    config: ServiceConfig
  ): void {
    if (this.services.has(name)) {
      throw new Error(`Service ${name} is already registered`);
    }

    // Store config for later service creation
    this.serviceConfigs.set(name, { ...config, name });
    
    // Create service instance
    const service = serviceFactory({ ...config, name });
    this.services.set(name, service);

    // Set up service event listeners
    this.setupServiceListeners(name, service);

    // Determine startup order based on dependencies
    this.updateStartupOrder();

    console.log(`Service registered: ${name}`);
    this.emit('service:registered', { name, dependencies: config.dependencies || [] });
  }

  /**
   * Get a service by name
   */
  get<T extends BaseService>(name: string): T | undefined {
    return this.services.get(name) as T;
  }

  /**
   * Start all services in dependency order
   */
  async startAll(): Promise<void> {
    console.log('Starting all services...');
    
    for (const serviceName of this.startupOrder) {
      const service = this.services.get(serviceName);
      if (!service) {
        throw new Error(`Service ${serviceName} not found during startup`);
      }

      // Inject dependencies
      await this.injectDependencies(serviceName, service);

      // Start service with timeout
      await this.startServiceWithTimeout(serviceName, service);
    }

    this.emit('registry:all_started');
    console.log('All services started successfully');
  }

  /**
   * Stop all services in reverse dependency order
   */
  async stopAll(): Promise<void> {
    console.log('Stopping all services...');
    
    const shutdownOrder = [...this.startupOrder].reverse();
    
    for (const serviceName of shutdownOrder) {
      const service = this.services.get(serviceName);
      if (service) {
        try {
          await service.stop();
        } catch (error) {
          console.error(`Error stopping service ${serviceName}:`, error);
        }
      }
    }

    this.emit('registry:all_stopped');
    console.log('All services stopped');
  }

  /**
   * Get health status of all services
   */
  getSystemHealth(): { overall: 'healthy' | 'degraded' | 'unhealthy', services: Record<string, ServiceHealth> } {
    const serviceHealths: Record<string, ServiceHealth> = {};
    let healthyCount = 0;
    let degradedCount = 0;
    let unhealthyCount = 0;

    for (const [name, service] of this.services) {
      const health = service.getHealth();
      serviceHealths[name] = health;

      switch (health.status) {
        case 'healthy':
          healthyCount++;
          break;
        case 'degraded':
          degradedCount++;
          break;
        case 'unhealthy':
          unhealthyCount++;
          break;
      }
    }

    let overall: 'healthy' | 'degraded' | 'unhealthy' = 'healthy';
    if (unhealthyCount > 0) {
      overall = 'unhealthy';
    } else if (degradedCount > 0) {
      overall = 'degraded';
    }

    return { overall, services: serviceHealths };
  }

  /**
   * Restart a specific service
   */
  async restartService(name: string): Promise<void> {
    const service = this.services.get(name);
    if (!service) {
      throw new Error(`Service ${name} not found`);
    }

    console.log(`Restarting service: ${name}`);
    
    try {
      await service.stop();
    } catch (error) {
      console.warn(`Error stopping service ${name} during restart:`, error);
    }

    await this.injectDependencies(name, service);
    await this.startServiceWithTimeout(name, service);

    this.emit('service:restarted', { name });
    console.log(`Service restarted successfully: ${name}`);
  }

  /**
   * Inject dependencies into a service
   */
  private async injectDependencies(serviceName: string, service: BaseService): Promise<void> {
    const config = this.serviceConfigs.get(serviceName);
    if (!config?.dependencies) return;

    for (const depName of config.dependencies) {
      const depService = this.services.get(depName);
      if (!depService) {
        throw new Error(`Dependency ${depName} not found for service ${serviceName}`);
      }
      service.addDependency(depName, depService);
    }
  }

  /**
   * Start a service with timeout protection
   */
  private async startServiceWithTimeout(name: string, service: BaseService): Promise<void> {
    const startPromise = service.start();
    const timeoutPromise = new Promise<never>((_, reject) => {
      setTimeout(() => reject(new Error(`Service ${name} startup timeout`)), this.config.maxStartupTime);
    });

    try {
      await Promise.race([startPromise, timeoutPromise]);
    } catch (error) {
      console.error(`Failed to start service ${name}:`, error);
      throw error;
    }
  }

  /**
   * Update startup order based on dependencies
   */
  private updateStartupOrder(): void {
    const visited = new Set<string>();
    const visiting = new Set<string>();
    const order: string[] = [];

    const visit = (serviceName: string): void => {
      if (visiting.has(serviceName)) {
        throw new Error(`Circular dependency detected involving service: ${serviceName}`);
      }
      if (visited.has(serviceName)) return;

      visiting.add(serviceName);

      const config = this.serviceConfigs.get(serviceName);
      if (config?.dependencies) {
        for (const dep of config.dependencies) {
          if (!this.serviceConfigs.has(dep)) {
            throw new Error(`Dependency ${dep} not registered for service ${serviceName}`);
          }
          visit(dep);
        }
      }

      visiting.delete(serviceName);
      visited.add(serviceName);
      order.push(serviceName);
    };

    for (const serviceName of this.serviceConfigs.keys()) {
      visit(serviceName);
    }

    this.startupOrder = order;
    console.log('Service startup order determined:', order);
  }

  /**
   * Setup event listeners for a service
   */
  private setupServiceListeners(name: string, service: BaseService): void {
    service.on('service:error', (error) => {
      this.emit('service:error', { service: name, ...error });
    });

    service.on('service:recovered', () => {
      this.emit('service:recovered', { service: name });
    });

    service.on('dependency:error', (error) => {
      this.emit('dependency:error', { service: name, ...error });
    });
  }

  /**
   * Setup global health monitoring
   */
  private setupGlobalHealthMonitoring(): void {
    setInterval(() => {
      const systemHealth = this.getSystemHealth();
      this.emit('system:health_update', systemHealth);

      if (systemHealth.overall === 'unhealthy') {
        this.emit('system:critical', systemHealth);
      }
    }, this.config.healthCheckInterval);
  }
}
