import { BaseService, ServiceConfig } from './base-service.js';
import * as fs from 'fs/promises';
import * as path from 'path';

export interface DocumentNamingRule {
  pattern: RegExp;
  extractorFunction: (text: string) => string | null;
  priority: number;
  documentType: string;
}

export interface NamingResult {
  success: boolean;
  originalName: string;
  finalName: string;
  documentNumber?: string;
  conflicts: number;
  reasoning: string;
}

export class DocumentEnhancementService extends BaseService {
  private namingRules: Map<string, DocumentNamingRule[]> = new Map();
  private namingCache: Map<string, string> = new Map();
  private usedNames: Set<string> = new Set();

  constructor() {
    const config: ServiceConfig = {
      name: 'DocumentEnhancementService',
      retryAttempts: 3,
      retryDelay: 1000
    };
    super(config);
    this.initializeNamingRules();
  }

  async initialize(): Promise<void> {
    console.log('DocumentEnhancementService initialized');
  }

  async shutdown(): Promise<void> {
    console.log('DocumentEnhancementService shutdown complete');
  }

  async healthCheck(): Promise<boolean> {
    // Service is always healthy if naming rules are loaded
    return this.namingRules.size > 0;
  }

  /**
   * Initialize document naming rules for different document types
   */
  private initializeNamingRules(): void {
    // Index document naming rules
    const indexRules: DocumentNamingRule[] = [
      {
        pattern: /^(\d{4}-\d{2}-\d{2}[-_]\d+)/,
        extractorFunction: (text: string) => {
          const match = text.match(/(\d{4}-\d{2}-\d{2}[-_]\d+)/);
          return match ? match[1] : null;
        },
        priority: 100,
        documentType: 'Index'
      },
      {
        pattern: /Index[_\s]*(\d+[-_]\d+)/i,
        extractorFunction: (text: string) => {
          const match = text.match(/Index[_\s]*(\d+[-_]\d+)/i);
          return match ? match[1] : null;
        },
        priority: 90,
        documentType: 'Index'
      },
      {
        pattern: /(\d{6,})/,
        extractorFunction: (text: string) => {
          const match = text.match(/(\d{6,})/);
          return match ? match[1] : null;
        },
        priority: 70,
        documentType: 'Index'
      }
    ];

    // Acte document naming rules
    const acteRules: DocumentNamingRule[] = [
      {
        pattern: /Acte[_\s]*(\d+[-_]\d+)/i,
        extractorFunction: (text: string) => {
          const match = text.match(/Acte[_\s]*(\d+[-_]\d+)/i);
          return match ? match[1] : null;
        },
        priority: 100,
        documentType: 'Acte'
      },
      {
        pattern: /(\d{4}-\d{2}-\d{2}[-_]\d+)/,
        extractorFunction: (text: string) => {
          const match = text.match(/(\d{4}-\d{2}-\d{2}[-_]\d+)/);
          return match ? match[1] : null;
        },
        priority: 90,
        documentType: 'Acte'
      },
      {
        pattern: /(\d{8,})/,
        extractorFunction: (text: string) => {
          const match = text.match(/(\d{8,})/);
          return match ? match[1] : null;
        },
        priority: 60,
        documentType: 'Acte'
      }
    ];

    this.namingRules.set('Index', indexRules);
    this.namingRules.set('Acte', acteRules);

    console.log(`Initialized naming rules for ${this.namingRules.size} document types`);
  }

  /**
   * Extract document number from various sources
   */
  async extractDocumentNumber(
    documentType: string,
    sources: {
      filename?: string;
      url?: string;
      pageContent?: string;
      metadata?: any;
    }
  ): Promise<string | null> {
    const rules = this.namingRules.get(documentType) || [];
    const searchTexts = [
      sources.filename || '',
      sources.url || '',
      sources.pageContent || '',
      JSON.stringify(sources.metadata || {})
    ];

    // Try each rule in priority order
    for (const rule of rules.sort((a, b) => b.priority - a.priority)) {
      for (const text of searchTexts) {
        if (text) {
          const documentNumber = rule.extractorFunction(text);
          if (documentNumber) {
            console.log(`Extracted document number "${documentNumber}" using rule priority ${rule.priority}`);
            return this.cleanDocumentNumber(documentNumber);
          }
        }
      }
    }

    console.warn(`No document number found for ${documentType} in provided sources`);
    return null;
  }

  /**
   * Generate proper filename according to {document_number}.pdf specification
   */
  async generateFilename(
    documentType: string,
    sources: {
      filename?: string;
      url?: string;
      pageContent?: string;
      metadata?: any;
    },
    originalExtension: string = 'pdf'
  ): Promise<NamingResult> {
    const originalName = sources.filename || 'unknown';
    
    // Check cache first
    const cacheKey = `${documentType}_${originalName}`;
    if (this.namingCache.has(cacheKey)) {
      const cachedName = this.namingCache.get(cacheKey)!;
      return {
        success: true,
        originalName,
        finalName: cachedName,
        documentNumber: path.parse(cachedName).name,
        conflicts: 0,
        reasoning: 'Retrieved from naming cache'
      };
    }

    // Extract document number
    const documentNumber = await this.extractDocumentNumber(documentType, sources);
    
    if (!documentNumber) {
      // Fallback naming strategy
      const fallbackName = this.generateFallbackName(documentType, originalName);
      return {
        success: false,
        originalName,
        finalName: fallbackName,
        conflicts: 0,
        reasoning: 'No document number found, used fallback naming'
      };
    }

    // Generate filename with proper extension
    const baseFilename = `${documentNumber}.${originalExtension}`;
    const finalFilename = await this.resolveNamingConflicts(baseFilename);
    
    // Cache the result
    this.namingCache.set(cacheKey, finalFilename);
    this.usedNames.add(finalFilename);

    const conflicts = finalFilename !== baseFilename ? 1 : 0;

    return {
      success: true,
      originalName,
      finalName: finalFilename,
      documentNumber,
      conflicts,
      reasoning: conflicts > 0 ? 'Document number found, resolved naming conflict' : 'Document number found, no conflicts'
    };
  }

  /**
   * Clean and standardize document number format
   */
  private cleanDocumentNumber(rawNumber: string): string {
    // Remove common prefixes and suffixes
    let cleaned = rawNumber
      .replace(/^(doc|document|acte|index)[_\s]*[-_]*/i, '')
      .replace(/[-_]*\.(pdf|doc|docx|txt)$/i, '')
      .trim();

    // Standardize separators to hyphens
    cleaned = cleaned.replace(/[_\s]+/g, '-');
    
    // Remove multiple consecutive separators
    cleaned = cleaned.replace(/-+/g, '-');
    
    // Remove leading/trailing separators
    cleaned = cleaned.replace(/^-+|-+$/g, '');

    return cleaned;
  }

  /**
   * Generate fallback name when document number cannot be extracted
   */
  private generateFallbackName(documentType: string, originalName: string): string {
    const timestamp = Date.now();
    const cleanOriginal = originalName
      .replace(/\.[^.]*$/, '') // Remove extension
      .replace(/[^a-zA-Z0-9-_]/g, '_') // Replace special chars
      .substring(0, 20); // Limit length

    return `${documentType.toLowerCase()}_${cleanOriginal}_${timestamp}.pdf`;
  }

  /**
   * Resolve naming conflicts by adding timestamp suffixes
   */
  private async resolveNamingConflicts(baseFilename: string): Promise<string> {
    if (!this.usedNames.has(baseFilename)) {
      return baseFilename;
    }

    const { name, ext } = path.parse(baseFilename);
    let counter = 1;
    let conflictedName = `${name}_${Date.now()}_${counter}${ext}`;

    // Keep incrementing until we find an unused name
    while (this.usedNames.has(conflictedName)) {
      counter++;
      conflictedName = `${name}_${Date.now()}_${counter}${ext}`;
    }

    return conflictedName;
  }

  /**
   * Validate filename according to specifications
   */
  validateFilename(filename: string, documentType: string): {
    isValid: boolean;
    issues: string[];
    suggestions: string[];
  } {
    const issues: string[] = [];
    const suggestions: string[] = [];

    // Check basic format
    if (!filename.match(/^[^<>:"/\\|?*]+\.pdf$/)) {
      issues.push('Filename contains invalid characters or wrong extension');
      suggestions.push('Use only alphanumeric characters, hyphens, underscores and .pdf extension');
    }

    // Check document number pattern
    const { name } = path.parse(filename);
    const rules = this.namingRules.get(documentType) || [];
    let hasValidPattern = false;

    for (const rule of rules) {
      if (rule.pattern.test(name)) {
        hasValidPattern = true;
        break;
      }
    }

    if (!hasValidPattern) {
      issues.push('Filename does not follow document number pattern');
      suggestions.push('Ensure filename starts with valid document number');
    }

    // Check length
    if (filename.length > 255) {
      issues.push('Filename too long');
      suggestions.push('Shorten filename to under 255 characters');
    }

    return {
      isValid: issues.length === 0,
      issues,
      suggestions
    };
  }

  /**
   * Bulk rename documents according to specifications
   */
  async bulkRename(
    documents: Array<{
      documentType: string;
      currentPath: string;
      sources: {
        filename?: string;
        url?: string;
        pageContent?: string;
        metadata?: any;
      };
    }>
  ): Promise<Array<{
    originalPath: string;
    newPath: string;
    result: NamingResult;
    renamed: boolean;
  }>> {
    const results = [];

    for (const doc of documents) {
      try {
        const currentDir = path.dirname(doc.currentPath);
        const currentExt = path.extname(doc.currentPath).substring(1);
        
        const namingResult = await this.generateFilename(
          doc.documentType,
          doc.sources,
          currentExt
        );

        const newPath = path.join(currentDir, namingResult.finalName);
        let renamed = false;

        // Only rename if the name actually changed and file exists
        if (newPath !== doc.currentPath) {
          try {
            await fs.access(doc.currentPath);
            await fs.rename(doc.currentPath, newPath);
            renamed = true;
            console.log(`Renamed: ${doc.currentPath} -> ${newPath}`);
          } catch (error) {
            console.error(`Failed to rename ${doc.currentPath}:`, error);
          }
        }

        results.push({
          originalPath: doc.currentPath,
          newPath,
          result: namingResult,
          renamed
        });

      } catch (error) {
        console.error(`Error processing document ${doc.currentPath}:`, error);
        results.push({
          originalPath: doc.currentPath,
          newPath: doc.currentPath,
          result: {
            success: false,
            originalName: path.basename(doc.currentPath),
            finalName: path.basename(doc.currentPath),
            conflicts: 0,
            reasoning: `Error: ${error instanceof Error ? error.message : 'Unknown error'}`
          },
          renamed: false
        });
      }
    }

    return results;
  }

  /**
   * Add custom naming rule
   */
  addNamingRule(documentType: string, rule: DocumentNamingRule): void {
    const rules = this.namingRules.get(documentType) || [];
    rules.push(rule);
    rules.sort((a, b) => b.priority - a.priority);
    this.namingRules.set(documentType, rules);
    
    console.log(`Added custom naming rule for ${documentType} with priority ${rule.priority}`);
  }

  /**
   * Get naming statistics
   */
  getNamingStatistics(): {
    totalRules: number;
    rulesByType: Record<string, number>;
    cacheSize: number;
    usedNamesCount: number;
  } {
    const rulesByType: Record<string, number> = {};
    
    for (const [type, rules] of this.namingRules) {
      rulesByType[type] = rules.length;
    }

    return {
      totalRules: Array.from(this.namingRules.values()).reduce((sum, rules) => sum + rules.length, 0),
      rulesByType,
      cacheSize: this.namingCache.size,
      usedNamesCount: this.usedNames.size
    };
  }

  /**
   * Clear naming cache
   */
  clearCache(): void {
    this.namingCache.clear();
    this.usedNames.clear();
    console.log('Cleared naming cache and used names registry');
  }

  /**
   * Test document number extraction with given input
   */
  testExtraction(documentType: string, testInput: string): {
    documentNumber: string | null;
    rule: string | null;
    confidence: number;
  } {
    const rules = this.namingRules.get(documentType) || [];
    
    for (const rule of rules.sort((a, b) => b.priority - a.priority)) {
      const documentNumber = rule.extractorFunction(testInput);
      if (documentNumber) {
        return {
          documentNumber: this.cleanDocumentNumber(documentNumber),
          rule: rule.pattern.toString(),
          confidence: rule.priority / 100
        };
      }
    }

    return {
      documentNumber: null,
      rule: null,
      confidence: 0
    };
  }
}
