import { Worker, isMainThread, parentPort, workerData } from 'worker_threads';
import { EventEmitter } from 'events';
import { BaseService, ServiceConfig } from './base-service';
import { ExtractionQueueItem } from '../config';

export interface WorkerTask {
  id: string;
  queueItem: ExtractionQueueItem;
  priority: number;
  createdAt: Date;
  assignedWorker?: string;
  startedAt?: Date;
  completedAt?: Date;
}

export interface WorkerStatus {
  id: string;
  busy: boolean;
  currentTask?: string;
  tasksCompleted: number;
  tasksCompleted1Hour: number;
  lastTaskDuration: number;
  avgTaskDuration: number;
  memoryUsage: number;
  cpuUsage: number;
  errorCount: number;
  health: 'healthy' | 'degraded' | 'unhealthy';
  startedAt: Date;
  lastHeartbeat: Date;
}

export interface WorkerPoolConfig extends ServiceConfig {
  minWorkers: number;
  maxWorkers: number;
  taskTimeout: number;
  healthCheckInterval: number;
  scaleUpThreshold: number;
  scaleDownThreshold: number;
  workerScript: string;
}

export class WorkerPool extends BaseService {
  private workers: Map<string, Worker> = new Map();
  private workerStatus: Map<string, WorkerStatus> = new Map();
  private pendingTasks: WorkerTask[] = [];
  private activeTasks: Map<string, WorkerTask> = new Map();
  private completedTasks: WorkerTask[] = [];
  private workerConfig: WorkerPoolConfig;
  private healthCheckTimer?: NodeJS.Timeout;
  private scalingTimer?: NodeJS.Timeout;
  private isShuttingDown = false;

  constructor(config: WorkerPoolConfig) {
    super(config);
    this.workerConfig = config;
    this.setupEventHandlers();
  }

  async initialize(): Promise<void> {
    console.log('Initializing worker pool', { config: this.workerConfig });
    
    // Start with minimum workers
    for (let i = 0; i < this.workerConfig.minWorkers; i++) {
      await this.createWorker();
    }

    // Start health monitoring
    this.startHealthMonitoring();
    
    // Start scaling monitoring
    this.startScalingMonitoring();
    
    this.emit('initialized');
    console.log('Worker pool initialized', { workerCount: this.workers.size });
  }

  async healthCheck(): Promise<boolean> {
    try {
      // Check if we have healthy workers
      const healthyWorkers = Array.from(this.workerStatus.values())
        .filter(status => status.health === 'healthy').length;
      
      return healthyWorkers > 0;
    } catch (error) {
      this.handleError(error, 'health_check');
      return false;
    }
  }

  async addTask(queueItem: ExtractionQueueItem): Promise<string> {
    const task: WorkerTask = {
      id: `task_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      queueItem,
      priority: this.calculateTaskPriority(queueItem),
      createdAt: new Date()
    };

    this.pendingTasks.push(task);
    this.sortTasksByPriority();
    
    this.emit('taskAdded', task);
    console.log('Task added to queue', { taskId: task.id, priority: task.priority });
    
    // Try to assign immediately
    this.assignTasks();
    
    return task.id;
  }

  async removeTask(taskId: string): Promise<boolean> {
    // Remove from pending tasks
    const pendingIndex = this.pendingTasks.findIndex(t => t.id === taskId);
    if (pendingIndex !== -1) {
      this.pendingTasks.splice(pendingIndex, 1);
      this.emit('taskRemoved', taskId);
      return true;
    }

    // Cancel active task if possible
    const activeTask = this.activeTasks.get(taskId);
    if (activeTask && activeTask.assignedWorker) {
      const worker = this.workers.get(activeTask.assignedWorker);
      if (worker) {
        worker.postMessage({ type: 'cancel', taskId });
        this.activeTasks.delete(taskId);
        this.updateWorkerStatus(activeTask.assignedWorker, { busy: false, currentTask: undefined });
        this.emit('taskCancelled', taskId);
        return true;
      }
    }

    return false;
  }

  getWorkerStatistics(): {
    totalWorkers: number;
    busyWorkers: number;
    healthyWorkers: number;
    pendingTasks: number;
    activeTasks: number;
    completedTasks: number;
    avgTaskDuration: number;
    throughput: number;
  } {
    const busyWorkers = Array.from(this.workerStatus.values()).filter(w => w.busy).length;
    const healthyWorkers = Array.from(this.workerStatus.values()).filter(w => w.health === 'healthy').length;
    
    const recentTasks = this.completedTasks.filter(t => 
      t.completedAt && Date.now() - t.completedAt.getTime() < 3600000 // Last hour
    );
    
    const avgDuration = recentTasks.length > 0 
      ? recentTasks.reduce((sum, t) => sum + (t.completedAt!.getTime() - t.startedAt!.getTime()), 0) / recentTasks.length
      : 0;

    return {
      totalWorkers: this.workers.size,
      busyWorkers,
      healthyWorkers,
      pendingTasks: this.pendingTasks.length,
      activeTasks: this.activeTasks.size,
      completedTasks: this.completedTasks.length,
      avgTaskDuration: avgDuration,
      throughput: recentTasks.length // tasks per hour
    };
  }

  async shutdown(): Promise<void> {
    this.isShuttingDown = true;
    console.log('Shutting down worker pool');

    // Stop monitoring
    if (this.healthCheckTimer) clearInterval(this.healthCheckTimer);
    if (this.scalingTimer) clearInterval(this.scalingTimer);

    // Gracefully terminate all workers
    const shutdownPromises = Array.from(this.workers.values()).map(worker => 
      this.shutdownWorker(worker)
    );

    await Promise.all(shutdownPromises);
    
    this.workers.clear();
    this.workerStatus.clear();
    this.pendingTasks = [];
    this.activeTasks.clear();
    
    this.emit('shutdown');
    console.log('Worker pool shutdown complete');
  }

  private async createWorker(): Promise<string> {
    const workerId = `worker_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    const worker = new Worker(this.workerConfig.workerScript, {
      workerData: { workerId }
    });

    worker.on('message', (message) => this.handleWorkerMessage(workerId, message));
    worker.on('error', (error) => this.handleWorkerError(workerId, error));
    worker.on('exit', (code) => this.handleWorkerExit(workerId, code));

    this.workers.set(workerId, worker);
    this.workerStatus.set(workerId, {
      id: workerId,
      busy: false,
      tasksCompleted: 0,
      tasksCompleted1Hour: 0,
      lastTaskDuration: 0,
      avgTaskDuration: 0,
      memoryUsage: 0,
      cpuUsage: 0,
      errorCount: 0,
      health: 'healthy',
      startedAt: new Date(),
      lastHeartbeat: new Date()
    });

    this.emit('workerCreated', workerId);
    console.log('Worker created', { workerId });
    
    return workerId;
  }

  private async shutdownWorker(worker: Worker): Promise<void> {
    return new Promise((resolve) => {
      const timeout = setTimeout(() => {
        worker.terminate();
        resolve();
      }, 5000);

      worker.postMessage({ type: 'shutdown' });
      
      worker.once('exit', () => {
        clearTimeout(timeout);
        resolve();
      });
    });
  }

  private handleWorkerMessage(workerId: string, message: any): void {
    switch (message.type) {
      case 'heartbeat':
        this.updateWorkerStatus(workerId, {
          lastHeartbeat: new Date(),
          memoryUsage: message.memoryUsage,
          cpuUsage: message.cpuUsage
        });
        break;

      case 'taskStarted':
        this.handleTaskStarted(workerId, message.taskId);
        break;

      case 'taskCompleted':
        this.handleTaskCompleted(workerId, message.taskId, message.result);
        break;

      case 'taskFailed':
        this.handleTaskFailed(workerId, message.taskId, message.error);
        break;

      case 'ready':
        this.updateWorkerStatus(workerId, { busy: false });
        this.assignTasks();
        break;
    }
  }

  private handleWorkerError(workerId: string, error: Error): void {
    console.error('Worker error', { workerId, error: error.message });
    
    const status = this.workerStatus.get(workerId);
    if (status) {
      status.errorCount++;
      status.health = status.errorCount > 3 ? 'unhealthy' : 'degraded';
      this.workerStatus.set(workerId, status);
    }

    this.emit('workerError', { workerId, error });
  }

  private handleWorkerExit(workerId: string, code: number): void {
    console.warn('Worker exited', { workerId, code });
    
    // Clean up
    this.workers.delete(workerId);
    this.workerStatus.delete(workerId);
    
    // Reassign any active tasks from this worker
    for (const [taskId, task] of this.activeTasks) {
      if (task.assignedWorker === workerId) {
        task.assignedWorker = undefined;
        task.startedAt = undefined;
        this.pendingTasks.push(task);
        this.activeTasks.delete(taskId);
      }
    }

    this.sortTasksByPriority();
    this.emit('workerExited', { workerId, code });

    // Create new worker if not shutting down and below minimum
    if (!this.isShuttingDown && this.workers.size < this.workerConfig.minWorkers) {
      this.createWorker();
    }
  }

  private calculateTaskPriority(queueItem: ExtractionQueueItem): number {
    // Index documents get highest priority (1000)
    if (queueItem.document_source === 'index') {
      return 1000 + (queueItem.retry_count || 0) * 10;
    }
    
    // Acte documents get standard priority (100)
    return 100 + (queueItem.retry_count || 0) * 10;
  }

  private sortTasksByPriority(): void {
    this.pendingTasks.sort((a, b) => {
      // Higher priority first
      if (a.priority !== b.priority) {
        return b.priority - a.priority;
      }
      // Then by creation time (FIFO for same priority)
      return a.createdAt.getTime() - b.createdAt.getTime();
    });
  }

  private assignTasks(): void {
    if (this.pendingTasks.length === 0) return;

    // Find available workers
    const availableWorkers = Array.from(this.workerStatus.entries())
      .filter(([_, status]) => !status.busy && status.health === 'healthy')
      .map(([id, _]) => id);

    for (const workerId of availableWorkers) {
      if (this.pendingTasks.length === 0) break;

      const task = this.pendingTasks.shift()!;
      const worker = this.workers.get(workerId);
      
      if (worker) {
        task.assignedWorker = workerId;
        task.startedAt = new Date();
        
        this.activeTasks.set(task.id, task);
        this.updateWorkerStatus(workerId, { 
          busy: true, 
          currentTask: task.id 
        });

        worker.postMessage({
          type: 'executeTask',
          task: task.queueItem,
          taskId: task.id
        });

        this.emit('taskAssigned', { taskId: task.id, workerId });
      }
    }
  }

  private handleTaskStarted(workerId: string, taskId: string): void {
    const task = this.activeTasks.get(taskId);
    if (task) {
      task.startedAt = new Date();
      this.emit('taskStarted', { taskId, workerId });
    }
  }

  private handleTaskCompleted(workerId: string, taskId: string, result: any): void {
    const task = this.activeTasks.get(taskId);
    if (task) {
      task.completedAt = new Date();
      
      const duration = task.completedAt.getTime() - (task.startedAt?.getTime() || 0);
      
      // Update worker statistics
      const status = this.workerStatus.get(workerId);
      if (status) {
        status.tasksCompleted++;
        status.tasksCompleted1Hour++;
        status.lastTaskDuration = duration;
        status.avgTaskDuration = (status.avgTaskDuration * (status.tasksCompleted - 1) + duration) / status.tasksCompleted;
        status.busy = false;
        status.currentTask = undefined;
        this.workerStatus.set(workerId, status);
      }

      this.activeTasks.delete(taskId);
      this.completedTasks.push(task);
      
      // Keep only recent completed tasks
      if (this.completedTasks.length > 1000) {
        this.completedTasks = this.completedTasks.slice(-500);
      }

      this.emit('taskCompleted', { taskId, workerId, result, duration });
      
      // Try to assign more tasks
      this.assignTasks();
    }
  }

  private handleTaskFailed(workerId: string, taskId: string, error: any): void {
    const task = this.activeTasks.get(taskId);
    if (task) {
      // Update worker error count
      const status = this.workerStatus.get(workerId);
      if (status) {
        status.errorCount++;
        status.busy = false;
        status.currentTask = undefined;
        this.workerStatus.set(workerId, status);
      }

      this.activeTasks.delete(taskId);
      this.emit('taskFailed', { taskId, workerId, error });
      
      // Decide whether to retry
      const retryCount = task.queueItem.retry_count || 0;
      if (retryCount < 3) {
        task.queueItem.retry_count = retryCount + 1;
        task.assignedWorker = undefined;
        task.startedAt = undefined;
        this.pendingTasks.push(task);
        this.sortTasksByPriority();
      }

      // Try to assign more tasks
      this.assignTasks();
    }
  }

  private updateWorkerStatus(workerId: string, updates: Partial<WorkerStatus>): void {
    const status = this.workerStatus.get(workerId);
    if (status) {
      Object.assign(status, updates);
      this.workerStatus.set(workerId, status);
    }
  }

  private startHealthMonitoring(): void {
    this.healthCheckTimer = setInterval(() => {
      this.performHealthCheck();
    }, this.workerConfig.healthCheckInterval);
  }

  private startScalingMonitoring(): void {
    this.scalingTimer = setInterval(() => {
      this.evaluateScaling();
    }, 30000); // Check every 30 seconds
  }

  private performHealthCheck(): void {
    const now = new Date();
    
    for (const [workerId, status] of this.workerStatus) {
      const timeSinceHeartbeat = now.getTime() - status.lastHeartbeat.getTime();
      
      if (timeSinceHeartbeat > this.workerConfig.healthCheckInterval * 2) {
        status.health = 'unhealthy';
        console.warn('Worker unhealthy - no heartbeat', { workerId, timeSinceHeartbeat });
        
        // Terminate unhealthy worker
        const worker = this.workers.get(workerId);
        if (worker) {
          worker.terminate();
        }
      }
    }

    // Reset hourly counters
    for (const status of this.workerStatus.values()) {
      const hoursSinceStart = (now.getTime() - status.startedAt.getTime()) / 3600000;
      if (hoursSinceStart >= 1) {
        status.tasksCompleted1Hour = 0;
      }
    }
  }

  private evaluateScaling(): void {
    if (this.isShuttingDown) return;

    const stats = this.getWorkerStatistics();
    const utilizationRatio = stats.busyWorkers / stats.totalWorkers;
    const queueLoadRatio = stats.pendingTasks / Math.max(stats.totalWorkers, 1);

    // Scale up conditions
    if ((utilizationRatio > this.workerConfig.scaleUpThreshold || queueLoadRatio > 2) 
        && stats.totalWorkers < this.workerConfig.maxWorkers) {
      console.log('Scaling up workers', { 
        currentWorkers: stats.totalWorkers, 
        utilization: utilizationRatio,
        queueLoad: queueLoadRatio 
      });
      this.createWorker();
    }

    // Scale down conditions
    if (utilizationRatio < this.workerConfig.scaleDownThreshold 
        && queueLoadRatio < 0.5 
        && stats.totalWorkers > this.workerConfig.minWorkers) {
      console.log('Scaling down workers', { 
        currentWorkers: stats.totalWorkers, 
        utilization: utilizationRatio 
      });
      this.scaleDownOneWorker();
    }
  }

  private scaleDownOneWorker(): void {
    // Find the least utilized healthy worker
    const candidates = Array.from(this.workerStatus.entries())
      .filter(([_, status]) => !status.busy && status.health === 'healthy')
      .sort((a, b) => a[1].tasksCompleted - b[1].tasksCompleted);

    if (candidates.length > 0) {
      const [workerId, _] = candidates[0];
      const worker = this.workers.get(workerId);
      if (worker) {
        this.shutdownWorker(worker);
        this.workers.delete(workerId);
        this.workerStatus.delete(workerId);
        this.emit('workerScaledDown', workerId);
      }
    }
  }

  private setupEventHandlers(): void {
    this.on('taskAdded', (task: WorkerTask) => {
      // Analytics integration can be added later
      console.log('Task queued', {
        taskId: task.id,
        priority: task.priority,
        documentType: task.queueItem.document_source
      });
    });

    this.on('taskCompleted', (data: any) => {
      // Analytics integration can be added later
      console.log('Task completed', {
        taskId: data.taskId,
        workerId: data.workerId,
        duration: data.duration,
        success: true
      });
    });

    this.on('taskFailed', (data: any) => {
      // Analytics integration can be added later
      console.error('Task failed', {
        taskId: data.taskId,
        workerId: data.workerId,
        success: false,
        error: data.error
      });
    });
  }
}
