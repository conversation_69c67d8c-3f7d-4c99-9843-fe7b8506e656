import { BaseService, ServiceConfig } from './base-service.js';

export interface DocumentPredictionFeatures {
  documentNumber: string;
  documentNumberLength: number;
  hasSpaces: boolean;
  hasNumbers: boolean;
  hasLetters: boolean;
  circonscriptionFonciere: string;
  documentSource: 'acte' | 'index';
  numberDigitCount: number;
  numberFormat: 'standard' | 'spaced' | 'mixed' | 'complex';
  timeOfDay: number;
  dayOfWeek: number;
  monthOfYear: number;
  recentFailureRate: number;
  regionSuccessRate: number;
}

export interface DocumentPrediction {
  exists: boolean;
  confidence: number;
  estimatedProcessingTime: number;
  riskFactors: string[];
  shouldAttempt: boolean;
  recommendedStrategy: string;
}

export interface TrainingRecord {
  features: DocumentPredictionFeatures;
  outcome: 'success' | 'failure';
  actualProcessingTime: number;
  errorType?: string;
  timestamp: Date;
}

export interface ModelPerformance {
  accuracy: number;
  precision: number;
  recall: number;
  f1Score: number;
  totalPredictions: number;
  correctPredictions: number;
  lastUpdated: Date;
}

export class MLDocumentDiscoveryService extends BaseService {
  private trainingData: TrainingRecord[] = [];
  private regionStats: Map<string, { success: number; total: number }> = new Map();
  private documentFormatStats: Map<string, { success: number; total: number }> = new Map();
  private modelPerformance: ModelPerformance = {
    accuracy: 0,
    precision: 0,
    recall: 0,
    f1Score: 0,
    totalPredictions: 0,
    correctPredictions: 0,
    lastUpdated: new Date()
  };
  private minTrainingDataSize = 100;
  private safetyThreshold = 0.9; // 90% confidence required

  constructor(config: ServiceConfig) {
    super(config);
  }

  async initialize(): Promise<void> {
    console.log('Initializing ML Document Discovery Service...');
    await this.loadTrainingData();
    await this.buildStatistics();
    this.startPerformanceMonitoring();
    console.log('ML Document Discovery Service initialized successfully');
  }

  async shutdown(): Promise<void> {
    console.log('Shutting down ML Document Discovery Service...');
    await this.saveTrainingData();
    console.log('ML Document Discovery Service shutdown complete');
  }

  async healthCheck(): Promise<boolean> {
    // Check if we have sufficient training data and reasonable performance
    return this.trainingData.length >= this.minTrainingDataSize / 2 && 
           this.modelPerformance.accuracy > 0.6;
  }

  /**
   * Predict if a document exists and should be attempted
   */
  async predictDocument(features: Partial<DocumentPredictionFeatures>): Promise<DocumentPrediction> {
    const completeFeatures = await this.enrichFeatures(features);
    
    // If we don't have enough training data, return conservative prediction
    if (this.trainingData.length < this.minTrainingDataSize) {
      return {
        exists: true, // Conservative: assume exists
        confidence: 0.5, // Low confidence
        estimatedProcessingTime: 120000, // 2 minutes default
        riskFactors: ['Insufficient training data'],
        shouldAttempt: true, // Always attempt when uncertain
        recommendedStrategy: 'conservative'
      };
    }

    const prediction = this.computePrediction(completeFeatures);
    
    // Apply safety threshold
    if (prediction.confidence < this.safetyThreshold) {
      prediction.shouldAttempt = true; // Fall back to manual search
      prediction.recommendedStrategy = 'manual_fallback';
      prediction.riskFactors.push('Low confidence - manual verification required');
    }

    // Log prediction for learning
    this.emit('prediction:made', {
      features: completeFeatures,
      prediction: { ...prediction }
    });

    return prediction;
  }

  /**
   * Record the actual outcome of a prediction for learning
   */
  async recordOutcome(
    features: DocumentPredictionFeatures,
    outcome: 'success' | 'failure',
    actualProcessingTime: number,
    errorType?: string
  ): Promise<void> {
    const record: TrainingRecord = {
      features,
      outcome,
      actualProcessingTime,
      errorType,
      timestamp: new Date()
    };

    // Add to training data
    this.trainingData.push(record);
    
    // Keep only recent data (last 10,000 records)
    if (this.trainingData.length > 10000) {
      this.trainingData = this.trainingData.slice(-10000);
    }

    // Update statistics
    this.updateStatistics(record);
    
    // Recalculate model performance periodically
    if (this.trainingData.length % 50 === 0) {
      this.calculateModelPerformance();
    }

    this.emit('learning:updated', {
      trainingDataSize: this.trainingData.length,
      modelPerformance: { ...this.modelPerformance }
    });
  }

  /**
   * Get model performance metrics
   */
  getModelPerformance(): ModelPerformance {
    return { ...this.modelPerformance };
  }

  /**
   * Get recommendations for improving success rate
   */
  getOptimizationRecommendations(): {
    regionInsights: Array<{ region: string; successRate: number; recommendation: string }>;
    formatInsights: Array<{ format: string; successRate: number; recommendation: string }>;
    timeInsights: Array<{ timeWindow: string; recommendation: string }>;
  } {
    const regionInsights = Array.from(this.regionStats.entries())
      .map(([region, stats]) => ({
        region,
        successRate: stats.total > 0 ? stats.success / stats.total : 0,
        recommendation: this.generateRegionRecommendation(region, stats)
      }))
      .filter(insight => insight.successRate < 0.8); // Focus on problematic regions

    const formatInsights = Array.from(this.documentFormatStats.entries())
      .map(([format, stats]) => ({
        format,
        successRate: stats.total > 0 ? stats.success / stats.total : 0,
        recommendation: this.generateFormatRecommendation(format, stats)
      }))
      .filter(insight => insight.successRate < 0.8);

    const timeInsights = this.generateTimeInsights();

    return {
      regionInsights,
      formatInsights,
      timeInsights
    };
  }

  /**
   * Enrich partial features with computed and contextual data
   */
  private async enrichFeatures(partial: Partial<DocumentPredictionFeatures>): Promise<DocumentPredictionFeatures> {
    const docNum = partial.documentNumber || '';
    const now = new Date();

    const features: DocumentPredictionFeatures = {
      documentNumber: docNum,
      documentNumberLength: docNum.length,
      hasSpaces: docNum.includes(' '),
      hasNumbers: /\d/.test(docNum),
      hasLetters: /[a-zA-Z]/.test(docNum),
      circonscriptionFonciere: partial.circonscriptionFonciere || 'Unknown',
      documentSource: partial.documentSource || 'acte',
      numberDigitCount: (docNum.match(/\d/g) || []).length,
      numberFormat: this.categorizeNumberFormat(docNum),
      timeOfDay: now.getHours(),
      dayOfWeek: now.getDay(),
      monthOfYear: now.getMonth(),
      recentFailureRate: this.calculateRecentFailureRate(partial.circonscriptionFonciere),
      regionSuccessRate: this.calculateRegionSuccessRate(partial.circonscriptionFonciere),
      ...partial
    };

    return features;
  }

  /**
   * Compute prediction using rule-based ML approach
   */
  private computePrediction(features: DocumentPredictionFeatures): DocumentPrediction {
    let existsProbability = 0.7; // Base probability
    let estimatedTime = 90000; // Base 90 seconds
    const riskFactors: string[] = [];

    // Region-based adjustment
    const regionStats = this.regionStats.get(features.circonscriptionFonciere);
    if (regionStats && regionStats.total > 10) {
      const regionSuccessRate = regionStats.success / regionStats.total;
      existsProbability = existsProbability * 0.3 + regionSuccessRate * 0.7;
      
      if (regionSuccessRate < 0.5) {
        riskFactors.push(`Low success rate in ${features.circonscriptionFonciere}`);
      }
    }

    // Document format analysis
    const formatStats = this.documentFormatStats.get(features.numberFormat);
    if (formatStats && formatStats.total > 5) {
      const formatSuccessRate = formatStats.success / formatStats.total;
      existsProbability = existsProbability * 0.7 + formatSuccessRate * 0.3;
      
      if (formatSuccessRate < 0.6) {
        riskFactors.push(`Document format "${features.numberFormat}" has low success rate`);
      }
    }

    // Document complexity analysis
    if (features.documentNumberLength > 15) {
      existsProbability *= 0.9;
      estimatedTime *= 1.2;
      riskFactors.push('Long document number may indicate complexity');
    }

    if (features.numberDigitCount < 3) {
      existsProbability *= 0.8;
      riskFactors.push('Very short document numbers often indicate invalid references');
    }

    // Time-based adjustments
    if (features.timeOfDay >= 9 && features.timeOfDay <= 17) {
      estimatedTime *= 1.3; // Slower during business hours
    }

    // Recent failure rate impact
    if (features.recentFailureRate > 0.3) {
      existsProbability *= 0.8;
      riskFactors.push('High recent failure rate in this region');
    }

    // Index documents typically have higher success rates
    if (features.documentSource === 'index') {
      existsProbability *= 1.1;
      estimatedTime *= 0.9;
    }

    // Clamp probability
    existsProbability = Math.max(0.1, Math.min(0.99, existsProbability));

    // Determine strategy
    let strategy = 'standard';
    if (existsProbability > 0.9) {
      strategy = 'aggressive';
    } else if (existsProbability < 0.6) {
      strategy = 'conservative';
    } else if (riskFactors.length > 2) {
      strategy = 'cautious';
    }

    return {
      exists: existsProbability > 0.5,
      confidence: existsProbability,
      estimatedProcessingTime: Math.round(estimatedTime),
      riskFactors,
      shouldAttempt: existsProbability > 0.3, // Very conservative threshold
      recommendedStrategy: strategy
    };
  }

  /**
   * Categorize document number format
   */
  private categorizeNumberFormat(docNum: string): 'standard' | 'spaced' | 'mixed' | 'complex' {
    if (!docNum) return 'complex';
    
    const hasSpaces = docNum.includes(' ');
    const hasLetters = /[a-zA-Z]/.test(docNum);
    const digitCount = (docNum.match(/\d/g) || []).length;
    
    if (hasLetters && hasSpaces) return 'complex';
    if (hasLetters) return 'mixed';
    if (hasSpaces && digitCount > 5) return 'spaced';
    if (digitCount >= 3 && digitCount <= 12 && !hasSpaces && !hasLetters) return 'standard';
    
    return 'complex';
  }

  /**
   * Calculate recent failure rate for a region
   */
  private calculateRecentFailureRate(region?: string): number {
    if (!region) return 0.3; // Default assumption
    
    const recentRecords = this.trainingData
      .filter(record => 
        record.features.circonscriptionFonciere === region &&
        Date.now() - record.timestamp.getTime() < 7 * 24 * 60 * 60 * 1000 // Last 7 days
      );
    
    if (recentRecords.length < 5) return 0.2; // Not enough data
    
    const failures = recentRecords.filter(record => record.outcome === 'failure').length;
    return failures / recentRecords.length;
  }

  /**
   * Calculate overall success rate for a region
   */
  private calculateRegionSuccessRate(region?: string): number {
    if (!region) return 0.7; // Default assumption
    
    const stats = this.regionStats.get(region);
    if (!stats || stats.total < 10) return 0.7;
    
    return stats.success / stats.total;
  }

  /**
   * Update statistics with new training record
   */
  private updateStatistics(record: TrainingRecord): void {
    // Update region stats
    const region = record.features.circonscriptionFonciere;
    const regionStats = this.regionStats.get(region) || { success: 0, total: 0 };
    regionStats.total++;
    if (record.outcome === 'success') {
      regionStats.success++;
    }
    this.regionStats.set(region, regionStats);

    // Update format stats
    const format = record.features.numberFormat;
    const formatStats = this.documentFormatStats.get(format) || { success: 0, total: 0 };
    formatStats.total++;
    if (record.outcome === 'success') {
      formatStats.success++;
    }
    this.documentFormatStats.set(format, formatStats);
  }

  /**
   * Calculate model performance metrics
   */
  private calculateModelPerformance(): void {
    if (this.trainingData.length < 20) return;

    // Use last 1000 records for performance calculation
    const testData = this.trainingData.slice(-1000);
    let correct = 0;
    let truePositives = 0;
    let falsePositives = 0;
    let trueNegatives = 0;
    let falseNegatives = 0;

    for (const record of testData) {
      const prediction = this.computePrediction(record.features);
      const predicted = prediction.exists;
      const actual = record.outcome === 'success';

      if (predicted === actual) correct++;

      if (predicted && actual) truePositives++;
      else if (predicted && !actual) falsePositives++;
      else if (!predicted && !actual) trueNegatives++;
      else if (!predicted && actual) falseNegatives++;
    }

    const accuracy = correct / testData.length;
    const precision = truePositives / (truePositives + falsePositives) || 0;
    const recall = truePositives / (truePositives + falseNegatives) || 0;
    const f1Score = 2 * (precision * recall) / (precision + recall) || 0;

    this.modelPerformance = {
      accuracy,
      precision,
      recall,
      f1Score,
      totalPredictions: testData.length,
      correctPredictions: correct,
      lastUpdated: new Date()
    };
  }

  /**
   * Generate region-specific recommendation
   */
  private generateRegionRecommendation(region: string, stats: { success: number; total: number }): string {
    const successRate = stats.success / stats.total;
    
    if (successRate < 0.5) {
      return `Very low success rate (${(successRate * 100).toFixed(1)}%). Consider investigating regional access issues or implementing region-specific strategies.`;
    } else if (successRate < 0.7) {
      return `Below average success rate (${(successRate * 100).toFixed(1)}%). May benefit from optimized search parameters.`;
    } else {
      return `Acceptable success rate (${(successRate * 100).toFixed(1)}%).`;
    }
  }

  /**
   * Generate format-specific recommendation
   */
  private generateFormatRecommendation(format: string, stats: { success: number; total: number }): string {
    const successRate = stats.success / stats.total;
    
    if (successRate < 0.6) {
      return `Format "${format}" has low success rate (${(successRate * 100).toFixed(1)}%). Consider implementing format-specific handling.`;
    } else {
      return `Format "${format}" performing adequately (${(successRate * 100).toFixed(1)}%).`;
    }
  }

  /**
   * Generate time-based insights
   */
  private generateTimeInsights(): Array<{ timeWindow: string; recommendation: string }> {
    // This is a simplified version - in a real implementation, 
    // you'd analyze the training data by time windows
    return [
      {
        timeWindow: 'Business Hours (9-17)',
        recommendation: 'Consider rate limiting during peak hours to avoid overwhelming the system'
      },
      {
        timeWindow: 'Evening (18-23)',
        recommendation: 'Optimal processing window with lower server load'
      },
      {
        timeWindow: 'Night (0-6)',
        recommendation: 'Good for batch processing but monitor for maintenance windows'
      }
    ];
  }

  /**
   * Build initial statistics from existing data
   */
  private async buildStatistics(): Promise<void> {
    console.log('Building ML statistics from training data...');
    
    for (const record of this.trainingData) {
      this.updateStatistics(record);
    }

    if (this.trainingData.length > 20) {
      this.calculateModelPerformance();
    }

    console.log(`Built statistics from ${this.trainingData.length} training records`);
    console.log(`Region stats: ${this.regionStats.size} regions`);
    console.log(`Format stats: ${this.documentFormatStats.size} formats`);
  }

  /**
   * Start performance monitoring
   */
  private startPerformanceMonitoring(): void {
    // Monitor and report model performance every hour
    setInterval(() => {
      if (this.trainingData.length >= this.minTrainingDataSize) {
        this.calculateModelPerformance();
        
        this.emit('model:performance', {
          performance: { ...this.modelPerformance },
          trainingDataSize: this.trainingData.length,
          recommendations: this.getOptimizationRecommendations()
        });
      }
    }, 60 * 60 * 1000); // Every hour
  }

  /**
   * Load training data from storage (mock implementation)
   */
  private async loadTrainingData(): Promise<void> {
    // In a real implementation, this would load from database
    console.log('Loading ML training data from storage...');
    // For now, we'll start with empty training data and learn over time
  }

  /**
   * Save training data to storage (mock implementation)
   */
  private async saveTrainingData(): Promise<void> {
    // In a real implementation, this would save to database
    console.log(`Saving ${this.trainingData.length} ML training records to storage...`);
  }
}
