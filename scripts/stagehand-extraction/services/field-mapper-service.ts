import { BaseService } from './base-service';
import { SupabaseService } from '../supabase-client';
import { StagehandPage } from '../types';
import { z } from 'zod';

export interface FieldMapperConfig {
  name: string;
  learningEnabled: boolean;
  confidenceThreshold: number;
  maxFieldMappings: number;
  enableLayoutSelectors: boolean;
  fallbackStrategies: ('css' | 'xpath' | 'text' | 'label')[];
}

export interface FieldMapping {
  id: string;
  pageUrl: string;
  pageHash: string;
  fieldName: string;
  fieldType: 'text' | 'dropdown' | 'radio' | 'checkbox' | 'button' | 'textarea';
  selectors: FieldSelector[];
  confidence: number;
  successCount: number;
  lastUsed: Date;
  metadata: {
    formId?: string;
    fieldPosition?: { x: number; y: number };
    nearbyElements?: string[];
    attributes?: Record<string, string>;
  };
}

export interface FieldSelector {
  strategy: 'label' | 'placeholder' | 'role' | 'testid' | 'css' | 'xpath' | 'layout' | 'text';
  selector: string;
  confidence: number;
  priority: number;
}

export interface FieldDetectionResult {
  success: boolean;
  mappings: FieldMapping[];
  errors: FieldDetectionError[];
  pageAnalysis: PageAnalysis;
}

export interface FieldDetectionError {
  fieldName: string;
  strategy: string;
  error: string;
  suggestion?: string;
}

export interface PageAnalysis {
  url: string;
  hash: string;
  formCount: number;
  fieldCount: number;
  hasFrames: boolean;
  layoutComplexity: 'simple' | 'medium' | 'complex';
  dynamicElements: boolean;
}

export interface FieldInteractionRequest {
  fieldName: string;
  action: 'fill' | 'click' | 'select' | 'check' | 'focus';
  value?: string;
  options?: string[];
}

export class FieldMapperService extends BaseService {
  private supabaseService: SupabaseService;
  private fieldMapperConfig: FieldMapperConfig;
  private fieldMappingCache: Map<string, FieldMapping[]> = new Map();
  private pageAnalysisCache: Map<string, PageAnalysis> = new Map();
  private learningData: Map<string, { successes: number; failures: number }> = new Map();

  constructor(config: FieldMapperConfig, supabaseService: SupabaseService) {
    super(config);
    this.fieldMapperConfig = config;
    this.supabaseService = supabaseService;
  }

  async initialize(): Promise<void> {
    console.log('Initializing Field Mapper Service...');
    
    // Load existing field mappings from database
    await this.loadFieldMappings();
    
    // Setup learning data cleanup
    this.setupLearningCleanup();
    
    console.log('Field Mapper Service initialized successfully');
  }

  async shutdown(): Promise<void> {
    console.log('Shutting down Field Mapper Service...');
    
    // Save current mappings and learning data
    await this.saveFieldMappings();
    
    console.log('Field Mapper Service shutdown complete');
  }

  async healthCheck(): Promise<boolean> {
    try {
      // Check if we have recent field mappings and learning is working
      const cacheSize = this.fieldMappingCache.size;
      return cacheSize >= 0; // Healthy even with empty cache
    } catch (error) {
      return false;
    }
  }

  /**
   * Detect and map all form fields on the current page
   */
  async detectPageFields(page: StagehandPage, formContext?: string): Promise<FieldDetectionResult> {
    const pageAnalysis = await this.analyzePage(page);
    const mappings: FieldMapping[] = [];
    const errors: FieldDetectionError[] = [];

    console.log(`Detecting fields on page: ${pageAnalysis.url}`);
    this.emit('field_detection:started', { 
      url: pageAnalysis.url, 
      formCount: pageAnalysis.formCount 
    });

    try {
      // Get existing mappings for this page
      const existingMappings = await this.getPageMappings(pageAnalysis.url, pageAnalysis.hash);
      
      // If we have high-confidence mappings, validate them first
      if (existingMappings.length > 0) {
        const validatedMappings = await this.validateExistingMappings(page, existingMappings);
        mappings.push(...validatedMappings);
      }

      // Detect new fields using multiple strategies
      const newFields = await this.detectNewFields(page, pageAnalysis, formContext);
      mappings.push(...newFields);

      // Learn from successful detections
      if (this.fieldMapperConfig.learningEnabled && mappings.length > 0) {
        await this.updateLearningData(mappings, true);
      }

      this.emit('field_detection:completed', {
        url: pageAnalysis.url,
        mappingCount: mappings.length,
        errorCount: errors.length
      });

      return {
        success: errors.length === 0 || mappings.length > 0,
        mappings,
        errors,
        pageAnalysis
      };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      errors.push({
        fieldName: 'unknown',
        strategy: 'detection',
        error: errorMessage,
        suggestion: 'Check page structure and try manual mapping'
      });

      this.emit('field_detection:failed', {
        url: pageAnalysis.url,
        error: errorMessage
      });

      return {
        success: false,
        mappings,
        errors,
        pageAnalysis
      };
    }
  }

  /**
   * Interact with a specific field using learned mappings
   */
  async interactWithField(
    page: StagehandPage, 
    request: FieldInteractionRequest,
    fallbackToDiscovery = true
  ): Promise<{ success: boolean; selector?: string; error?: string }> {
    const pageUrl = page.url();
    const pageHash = await this.generatePageHash(page);
    
    // Try to find existing mapping
    let mapping = await this.findFieldMapping(pageUrl, pageHash, request.fieldName);
    
    if (!mapping && fallbackToDiscovery) {
      // Attempt real-time field discovery
      console.log(`No mapping found for field '${request.fieldName}', attempting discovery...`);
      const discoveryResult = await this.discoverField(page, request.fieldName);
      if (discoveryResult.success && discoveryResult.mapping) {
        mapping = discoveryResult.mapping;
      }
    }

    if (!mapping) {
      return {
        success: false,
        error: `No mapping found for field: ${request.fieldName}`
      };
    }

    // Try each selector in order of confidence
    const sortedSelectors = mapping.selectors.sort((a, b) => b.confidence - a.confidence);
    
    for (const selector of sortedSelectors) {
      try {
        const success = await this.executeFieldInteraction(page, selector, request);
        if (success) {
          // Update mapping confidence and usage
          await this.updateMappingSuccess(mapping, selector);
          
          this.emit('field_interaction:success', {
            fieldName: request.fieldName,
            action: request.action,
            strategy: selector.strategy,
            selector: selector.selector
          });

          return {
            success: true,
            selector: selector.selector
          };
        }
      } catch (error) {
        console.warn(`Selector failed: ${selector.selector}`, error);
        continue;
      }
    }

    // All selectors failed
    await this.updateMappingFailure(mapping);
    
    return {
      success: false,
      error: `All selectors failed for field: ${request.fieldName}`
    };
  }

  /**
   * Learn field mapping from successful interaction
   */
  async learnFieldMapping(
    page: StagehandPage,
    fieldName: string,
    selector: string,
    strategy: string,
    success: boolean
  ): Promise<void> {
    if (!this.fieldMapperConfig.learningEnabled) return;

    const pageUrl = page.url();
    const pageHash = await this.generatePageHash(page);

    if (success) {
      // Create or update mapping
      let mapping = await this.findFieldMapping(pageUrl, pageHash, fieldName);
      
      if (!mapping) {
        mapping = await this.createFieldMapping(page, fieldName, selector, strategy);
      } else {
        // Add new selector or update existing one
        await this.addOrUpdateSelector(mapping, selector, strategy, true);
      }

      await this.saveFieldMapping(mapping);
      
      this.emit('field_learning:success', {
        fieldName,
        strategy,
        selector,
        pageUrl
      });
    } else {
      // Reduce confidence of failed selector
      const mapping = await this.findFieldMapping(pageUrl, pageHash, fieldName);
      if (mapping) {
        await this.addOrUpdateSelector(mapping, selector, strategy, false);
        await this.saveFieldMapping(mapping);
      }
    }
  }

  /**
   * Get field mapping suggestions for a page
   */
  async getFieldSuggestions(
    page: StagehandPage,
    targetFields: string[]
  ): Promise<{
    fieldName: string;
    suggestions: Array<{
      selector: string;
      strategy: string;
      confidence: number;
      description: string;
    }>;
  }[]> {
    const suggestions: Array<{
      fieldName: string;
      suggestions: Array<{
        selector: string;
        strategy: string;
        confidence: number;
        description: string;
      }>;
    }> = [];

    for (const fieldName of targetFields) {
      const fieldSuggestions = await this.generateFieldSuggestions(page, fieldName);
      suggestions.push({
        fieldName,
        suggestions: fieldSuggestions
      });
    }

    return suggestions;
  }

  /**
   * Analyze page structure and complexity
   */
  private async analyzePage(page: StagehandPage): Promise<PageAnalysis> {
    const url = page.url();
    const cached = this.pageAnalysisCache.get(url);
    if (cached) return cached;

    try {
      const analysis = await page.extract({
        instruction: "Analyze the page structure for forms, fields, and complexity",
        schema: z.object({
          formCount: z.number(),
          fieldCount: z.number(),
          hasFrames: z.boolean(),
          dynamicElements: z.boolean(),
          layoutComplexity: z.enum(['simple', 'medium', 'complex'])
        })
      });

      const pageHash = await this.generatePageHash(page);

      const pageAnalysis: PageAnalysis = {
        url,
        hash: pageHash,
        formCount: analysis.formCount,
        fieldCount: analysis.fieldCount,
        hasFrames: analysis.hasFrames,
        layoutComplexity: analysis.layoutComplexity,
        dynamicElements: analysis.dynamicElements
      };

      this.pageAnalysisCache.set(url, pageAnalysis);
      return pageAnalysis;

    } catch (error) {
      // Fallback analysis
      return {
        url,
        hash: await this.generatePageHash(page),
        formCount: 1,
        fieldCount: 0,
        hasFrames: false,
        layoutComplexity: 'medium',
        dynamicElements: false
      };
    }
  }

  /**
   * Detect new fields using multiple strategies
   */
  private async detectNewFields(
    page: StagehandPage,
    pageAnalysis: PageAnalysis,
    formContext?: string
  ): Promise<FieldMapping[]> {
    const mappings: FieldMapping[] = [];

    // Strategy 1: Label-based detection
    const labelFields = await this.detectFieldsByLabels(page);
    mappings.push(...labelFields);

    // Strategy 2: Placeholder-based detection
    const placeholderFields = await this.detectFieldsByPlaceholders(page);
    mappings.push(...placeholderFields);

    // Strategy 3: Role-based detection
    const roleFields = await this.detectFieldsByRoles(page);
    mappings.push(...roleFields);

    // Strategy 4: TestId-based detection
    const testIdFields = await this.detectFieldsByTestIds(page);
    mappings.push(...testIdFields);

    // Strategy 5: Layout-based detection (if enabled)
    if (this.fieldMapperConfig.enableLayoutSelectors) {
      const layoutFields = await this.detectFieldsByLayout(page);
      mappings.push(...layoutFields);
    }

    // Deduplicate and merge mappings
    return this.mergeDuplicateMappings(mappings);
  }

  /**
   * Detect fields by their associated labels
   */
  private async detectFieldsByLabels(page: StagehandPage): Promise<FieldMapping[]> {
    const mappings: FieldMapping[] = [];

    try {
      const labelInfo = await page.extract({
        instruction: "Find all form labels and their associated input fields",
        schema: z.object({
          labels: z.array(z.object({
            text: z.string(),
            for: z.string().optional(),
            inputType: z.string().optional(),
            position: z.object({ x: z.number(), y: z.number() }).optional()
          }))
        })
      });

      for (const label of labelInfo.labels) {
        const fieldName = this.normalizeFieldName(label.text);
        const fieldType = this.mapInputTypeToFieldType(label.inputType || 'text');

        const selectors: FieldSelector[] = [
          {
            strategy: 'label',
            selector: `input[id="${label.for}"]`,
            confidence: 0.9,
            priority: 1
          },
          {
            strategy: 'label',
            selector: `getByLabel("${label.text}")`,
            confidence: 0.95,
            priority: 0
          }
        ];

        const mapping: FieldMapping = {
          id: this.generateMappingId(page.url(), fieldName),
          pageUrl: page.url(),
          pageHash: await this.generatePageHash(page),
          fieldName,
          fieldType,
          selectors,
          confidence: 0.9,
          successCount: 0,
          lastUsed: new Date(),
          metadata: {
            fieldPosition: label.position,
            attributes: label.for ? { for: label.for } : {}
          }
        };

        mappings.push(mapping);
      }
    } catch (error) {
      console.warn('Failed to detect fields by labels:', error);
    }

    return mappings;
  }

  /**
   * Detect fields by placeholder text
   */
  private async detectFieldsByPlaceholders(page: StagehandPage): Promise<FieldMapping[]> {
    const mappings: FieldMapping[] = [];

    try {
      const placeholderInfo = await page.extract({
        instruction: "Find all input fields with placeholder text",
        schema: z.object({
          fields: z.array(z.object({
            placeholder: z.string(),
            type: z.string().optional(),
            name: z.string().optional(),
            id: z.string().optional()
          }))
        })
      });

      for (const field of placeholderInfo.fields) {
        const fieldName = this.normalizeFieldName(field.placeholder);
        const fieldType = this.mapInputTypeToFieldType(field.type || 'text');

        const selectors: FieldSelector[] = [
          {
            strategy: 'placeholder',
            selector: `getByPlaceholder("${field.placeholder}")`,
            confidence: 0.85,
            priority: 1
          }
        ];

        if (field.id) {
          selectors.push({
            strategy: 'css',
            selector: `#${field.id}`,
            confidence: 0.8,
            priority: 2
          });
        }

        if (field.name) {
          selectors.push({
            strategy: 'css',
            selector: `[name="${field.name}"]`,
            confidence: 0.75,
            priority: 3
          });
        }

        const mapping: FieldMapping = {
          id: this.generateMappingId(page.url(), fieldName),
          pageUrl: page.url(),
          pageHash: await this.generatePageHash(page),
          fieldName,
          fieldType,
          selectors,
          confidence: 0.8,
          successCount: 0,
          lastUsed: new Date(),
          metadata: {
            attributes: {
              placeholder: field.placeholder,
              ...(field.name && { name: field.name }),
              ...(field.id && { id: field.id })
            }
          }
        };

        mappings.push(mapping);
      }
    } catch (error) {
      console.warn('Failed to detect fields by placeholders:', error);
    }

    return mappings;
  }

  /**
   * Detect fields by ARIA roles
   */
  private async detectFieldsByRoles(page: StagehandPage): Promise<FieldMapping[]> {
    const mappings: FieldMapping[] = [];

    const roleSelectors = [
      { role: 'textbox', fieldType: 'text' as const },
      { role: 'button', fieldType: 'button' as const },
      { role: 'checkbox', fieldType: 'checkbox' as const },
      { role: 'radio', fieldType: 'radio' as const },
      { role: 'combobox', fieldType: 'dropdown' as const }
    ];

    for (const { role, fieldType } of roleSelectors) {
      try {
        const roleElements = await page.extract({
          instruction: `Find all elements with role="${role}" and their accessible names`,
          schema: z.object({
            elements: z.array(z.object({
              name: z.string().optional(),
              id: z.string().optional(),
              description: z.string().optional()
            }))
          })
        });

        for (const element of roleElements.elements) {
          if (!element.name) continue;

          const fieldName = this.normalizeFieldName(element.name);
          const selectors: FieldSelector[] = [
            {
              strategy: 'role',
              selector: `getByRole("${role}", { name: "${element.name}" })`,
              confidence: 0.9,
              priority: 1
            }
          ];

          if (element.id) {
            selectors.push({
              strategy: 'css',
              selector: `#${element.id}`,
              confidence: 0.8,
              priority: 2
            });
          }

          const mapping: FieldMapping = {
            id: this.generateMappingId(page.url(), fieldName),
            pageUrl: page.url(),
            pageHash: await this.generatePageHash(page),
            fieldName,
            fieldType,
            selectors,
            confidence: 0.85,
            successCount: 0,
            lastUsed: new Date(),
            metadata: {
              attributes: {
                role,
                name: element.name,
                ...(element.id && { id: element.id })
              }
            }
          };

          mappings.push(mapping);
        }
      } catch (error) {
        console.warn(`Failed to detect fields by role ${role}:`, error);
      }
    }

    return mappings;
  }

  /**
   * Detect fields by test IDs
   */
  private async detectFieldsByTestIds(page: StagehandPage): Promise<FieldMapping[]> {
    const mappings: FieldMapping[] = [];

    try {
      const testIdElements = await page.extract({
        instruction: "Find all elements with data-testid attributes",
        schema: z.object({
          elements: z.array(z.object({
            testId: z.string(),
            type: z.string().optional(),
            tag: z.string().optional()
          }))
        })
      });

      for (const element of testIdElements.elements) {
        const fieldName = this.normalizeFieldName(element.testId);
        const fieldType = this.mapTagToFieldType(element.tag || 'input');

        const selectors: FieldSelector[] = [
          {
            strategy: 'testid',
            selector: `getByTestId("${element.testId}")`,
            confidence: 0.95,
            priority: 1
          },
          {
            strategy: 'css',
            selector: `[data-testid="${element.testId}"]`,
            confidence: 0.9,
            priority: 2
          }
        ];

        const mapping: FieldMapping = {
          id: this.generateMappingId(page.url(), fieldName),
          pageUrl: page.url(),
          pageHash: await this.generatePageHash(page),
          fieldName,
          fieldType,
          selectors,
          confidence: 0.9,
          successCount: 0,
          lastUsed: new Date(),
          metadata: {
            attributes: {
              'data-testid': element.testId,
              tag: element.tag
            }
          }
        };

        mappings.push(mapping);
      }
    } catch (error) {
      console.warn('Failed to detect fields by test IDs:', error);
    }

    return mappings;
  }

  /**
   * Detect fields using layout-based selectors
   */
  private async detectFieldsByLayout(page: StagehandPage): Promise<FieldMapping[]> {
    const mappings: FieldMapping[] = [];

    try {
      // Use Playwright's layout selectors to find fields relative to labels
      const layoutElements = await page.extract({
        instruction: "Find input fields and their spatial relationships to nearby text elements",
        schema: z.object({
          relationships: z.array(z.object({
            labelText: z.string(),
            inputType: z.string().optional(),
            relationship: z.enum(['right-of', 'left-of', 'near'])
          }))
        })
      });

      for (const rel of layoutElements.relationships) {
        const fieldName = this.normalizeFieldName(rel.labelText);
        const fieldType = this.mapInputTypeToFieldType(rel.inputType || 'text');

        const selectors: FieldSelector[] = [
          {
            strategy: 'layout',
            selector: `input:${rel.relationship}(:text("${rel.labelText}"))`,
            confidence: 0.7,
            priority: 1
          }
        ];

        const mapping: FieldMapping = {
          id: this.generateMappingId(page.url(), fieldName),
          pageUrl: page.url(),
          pageHash: await this.generatePageHash(page),
          fieldName,
          fieldType,
          selectors,
          confidence: 0.7,
          successCount: 0,
          lastUsed: new Date(),
          metadata: {
            nearbyElements: [rel.labelText],
            attributes: {
              relationship: rel.relationship
            }
          }
        };

        mappings.push(mapping);
      }
    } catch (error) {
      console.warn('Failed to detect fields by layout:', error);
    }

    return mappings;
  }

  /**
   * Execute field interaction using the best selector
   */
  private async executeFieldInteraction(
    page: StagehandPage,
    selector: FieldSelector,
    request: FieldInteractionRequest
  ): Promise<boolean> {
    try {
      let locator;

      // Build locator based on strategy
      switch (selector.strategy) {
        case 'label':
          if (selector.selector.startsWith('getByLabel')) {
            const labelText = selector.selector.match(/getByLabel\("([^"]+)"\)/)?.[1];
            if (labelText) {
              locator = page.getByLabel(labelText);
            } else {
              locator = page.locator(selector.selector);
            }
          } else {
            locator = page.locator(selector.selector);
          }
          break;

        case 'placeholder':
          if (selector.selector.startsWith('getByPlaceholder')) {
            const placeholderText = selector.selector.match(/getByPlaceholder\("([^"]+)"\)/)?.[1];
            if (placeholderText) {
              locator = page.getByPlaceholder(placeholderText);
            } else {
              locator = page.locator(selector.selector);
            }
          } else {
            locator = page.locator(selector.selector);
          }
          break;

        case 'role':
          if (selector.selector.startsWith('getByRole')) {
            // Parse getByRole selector
            const match = selector.selector.match(/getByRole\("([^"]+)"(?:,\s*\{\s*name:\s*"([^"]+)"\s*\})?\)/);
            if (match && match[1]) {
              const [, role, name] = match;
              locator = name ? page.getByRole(role as any, { name }) : page.getByRole(role as any);
            } else {
              locator = page.locator(selector.selector);
            }
          } else {
            locator = page.locator(selector.selector);
          }
          break;

        case 'testid':
          if (selector.selector.startsWith('getByTestId')) {
            const testId = selector.selector.match(/getByTestId\("([^"]+)"\)/)?.[1];
            if (testId) {
              locator = page.getByTestId(testId);
            } else {
              locator = page.locator(selector.selector);
            }
          } else {
            locator = page.locator(selector.selector);
          }
          break;

        case 'css':
        case 'xpath':
        case 'layout':
          locator = page.locator(selector.selector);
          break;

        default:
          locator = page.locator(selector.selector);
      }

      if (!locator) {
        return false;
      }

      // Execute the requested action
      switch (request.action) {
        case 'fill':
          if (request.value !== undefined) {
            await locator.fill(request.value);
          }
          break;

        case 'click':
          await locator.click();
          break;

        case 'select':
          if (request.value !== undefined) {
            await locator.selectOption(request.value);
          }
          break;

        case 'check':
          await locator.check();
          break;

        case 'focus':
          await locator.focus();
          break;

        default:
          return false;
      }

      return true;

    } catch (error) {
      console.warn(`Field interaction failed with selector ${selector.selector}:`, error);
      return false;
    }
  }

  /**
   * Utility methods
   */
  private normalizeFieldName(text: string): string {
    return text
      .toLowerCase()
      .replace(/[^a-z0-9]/g, '_')
      .replace(/_+/g, '_')
      .replace(/^_|_$/g, '');
  }

  private mapInputTypeToFieldType(inputType: string): FieldMapping['fieldType'] {
    switch (inputType.toLowerCase()) {
      case 'text':
      case 'email':
      case 'password':
      case 'search':
      case 'url':
        return 'text';
      case 'select':
      case 'select-one':
        return 'dropdown';
      case 'radio':
        return 'radio';
      case 'checkbox':
        return 'checkbox';
      case 'button':
      case 'submit':
        return 'button';
      case 'textarea':
        return 'textarea';
      default:
        return 'text';
    }
  }

  private mapTagToFieldType(tag: string): FieldMapping['fieldType'] {
    switch (tag.toLowerCase()) {
      case 'input':
        return 'text';
      case 'select':
        return 'dropdown';
      case 'button':
        return 'button';
      case 'textarea':
        return 'textarea';
      default:
        return 'text';
    }
  }

  private generateMappingId(url: string, fieldName: string): string {
    return `mapping_${Buffer.from(`${url}_${fieldName}`).toString('base64').slice(0, 16)}`;
  }

  private async generatePageHash(page: StagehandPage): Promise<string> {
    // In a real implementation, this would generate a hash of the page structure
    return `hash_${page.url().replace(/[^a-z0-9]/gi, '_')}`;
  }

  private mergeDuplicateMappings(mappings: FieldMapping[]): FieldMapping[] {
    const merged = new Map<string, FieldMapping>();

    for (const mapping of mappings) {
      const key = `${mapping.fieldName}_${mapping.fieldType}`;
      
      if (merged.has(key)) {
        const existing = merged.get(key)!;
        // Merge selectors and keep highest confidence
        existing.selectors.push(...mapping.selectors);
        existing.confidence = Math.max(existing.confidence, mapping.confidence);
      } else {
        merged.set(key, mapping);
      }
    }

    return Array.from(merged.values());
  }

  private async loadFieldMappings(): Promise<void> {
    // In a real implementation, this would load from database
    console.log('Loading field mappings from database...');
  }

  private async saveFieldMappings(): Promise<void> {
    // In a real implementation, this would save to database
    console.log('Saving field mappings to database...');
  }

  private async getPageMappings(url: string, hash: string): Promise<FieldMapping[]> {
    const cacheKey = `${url}_${hash}`;
    return this.fieldMappingCache.get(cacheKey) || [];
  }

  private async findFieldMapping(url: string, hash: string, fieldName: string): Promise<FieldMapping | null> {
    const mappings = await this.getPageMappings(url, hash);
    const found = mappings.find(m => m.fieldName === fieldName);
    return found || null;
  }

  private async validateExistingMappings(page: StagehandPage, mappings: FieldMapping[]): Promise<FieldMapping[]> {
    const validated: FieldMapping[] = [];

    for (const mapping of mappings) {
      // Quick validation by checking if selectors still work
      let hasValidSelector = false;
      
      for (const selector of mapping.selectors) {
        try {
          const locator = page.locator(selector.selector);
          const count = await locator.count();
          if (count > 0) {
            hasValidSelector = true;
            break;
          }
        } catch {
          continue;
        }
      }

      if (hasValidSelector) {
        validated.push(mapping);
      }
    }

    return validated;
  }

  private async discoverField(page: StagehandPage, fieldName: string): Promise<{ success: boolean; mapping?: FieldMapping }> {
    // Real-time field discovery implementation
    try {
      const detection = await this.detectPageFields(page);
      const matchingMapping = detection.mappings.find(m => 
        m.fieldName === fieldName || 
        m.fieldName.includes(fieldName.toLowerCase()) ||
        fieldName.toLowerCase().includes(m.fieldName)
      );

      if (matchingMapping) {
        return { success: true, mapping: matchingMapping };
      }

      return { success: false };
    } catch (error) {
      return { success: false };
    }
  }

  private setupLearningCleanup(): void {
    // Clean up old learning data periodically
    setInterval(() => {
      const cutoff = Date.now() - 30 * 24 * 60 * 60 * 1000; // 30 days
      for (const [key, data] of this.learningData.entries()) {
        // Remove entries with poor performance
        if (data.failures > data.successes * 2) {
          this.learningData.delete(key);
        }
      }
    }, 24 * 60 * 60 * 1000); // Daily cleanup
  }

  private async updateLearningData(mappings: FieldMapping[], success: boolean): Promise<void> {
    for (const mapping of mappings) {
      const key = `${mapping.pageUrl}_${mapping.fieldName}`;
      const current = this.learningData.get(key) || { successes: 0, failures: 0 };
      
      if (success) {
        current.successes++;
      } else {
        current.failures++;
      }
      
      this.learningData.set(key, current);
    }
  }

  private async updateMappingSuccess(mapping: FieldMapping, selector: FieldSelector): Promise<void> {
    mapping.successCount++;
    mapping.lastUsed = new Date();
    
    // Increase confidence of successful selector
    const selectorIndex = mapping.selectors.findIndex(s => s.selector === selector.selector);
    if (selectorIndex !== -1) {
      mapping.selectors[selectorIndex].confidence = Math.min(0.99, selector.confidence + 0.05);
    }
    
    // Update overall mapping confidence
    mapping.confidence = Math.min(0.99, mapping.confidence + 0.02);
  }

  private async updateMappingFailure(mapping: FieldMapping): Promise<void> {
    // Reduce overall confidence
    mapping.confidence = Math.max(0.1, mapping.confidence - 0.1);
    
    // Reduce confidence of all selectors slightly
    for (const selector of mapping.selectors) {
      selector.confidence = Math.max(0.1, selector.confidence - 0.05);
    }
  }

  private async createFieldMapping(
    page: StagehandPage,
    fieldName: string,
    selector: string,
    strategy: string
  ): Promise<FieldMapping> {
    const fieldSelector: FieldSelector = {
      strategy: strategy as any,
      selector,
      confidence: 0.8,
      priority: 1
    };

    return {
      id: this.generateMappingId(page.url(), fieldName),
      pageUrl: page.url(),
      pageHash: await this.generatePageHash(page),
      fieldName,
      fieldType: 'text', // Default, could be improved
      selectors: [fieldSelector],
      confidence: 0.8,
      successCount: 1,
      lastUsed: new Date(),
      metadata: {
        attributes: {}
      }
    };
  }

  private async addOrUpdateSelector(
    mapping: FieldMapping,
    selector: string,
    strategy: string,
    success: boolean
  ): Promise<void> {
    const existingIndex = mapping.selectors.findIndex(s => s.selector === selector);
    
    if (existingIndex !== -1) {
      // Update existing selector
      const existing = mapping.selectors[existingIndex];
      if (success) {
        existing.confidence = Math.min(0.99, existing.confidence + 0.05);
      } else {
        existing.confidence = Math.max(0.1, existing.confidence - 0.1);
      }
    } else {
      // Add new selector
      mapping.selectors.push({
        strategy: strategy as any,
        selector,
        confidence: success ? 0.8 : 0.3,
        priority: mapping.selectors.length
      });
    }
  }

  private async saveFieldMapping(mapping: FieldMapping): Promise<void> {
    const cacheKey = `${mapping.pageUrl}_${mapping.pageHash}`;
    const existingMappings = this.fieldMappingCache.get(cacheKey) || [];
    
    const existingIndex = existingMappings.findIndex(m => m.id === mapping.id);
    if (existingIndex !== -1) {
      existingMappings[existingIndex] = mapping;
    } else {
      existingMappings.push(mapping);
    }
    
    this.fieldMappingCache.set(cacheKey, existingMappings);
    
    // In a real implementation, this would also save to database
  }

  private async generateFieldSuggestions(
    page: StagehandPage,
    fieldName: string
  ): Promise<Array<{
    selector: string;
    strategy: string;
    confidence: number;
    description: string;
  }>> {
    const suggestions: Array<{
      selector: string;
      strategy: string;
      confidence: number;
      description: string;
    }> = [];

    // Generate suggestions based on common patterns
    const normalizedName = this.normalizeFieldName(fieldName);
    
    // Label-based suggestions
    suggestions.push({
      selector: `getByLabel("${fieldName}")`,
      strategy: 'label',
      confidence: 0.9,
      description: `Find field by exact label text "${fieldName}"`
    });

    // Placeholder suggestions
    suggestions.push({
      selector: `getByPlaceholder("${fieldName}")`,
      strategy: 'placeholder',
      confidence: 0.8,
      description: `Find field by placeholder text "${fieldName}"`
    });

    // CSS ID suggestions
    suggestions.push({
      selector: `#${normalizedName}`,
      strategy: 'css',
      confidence: 0.7,
      description: `Find field by ID "${normalizedName}"`
    });

    // CSS name attribute suggestions
    suggestions.push({
      selector: `[name="${normalizedName}"]`,
      strategy: 'css',
      confidence: 0.6,
      description: `Find field by name attribute "${normalizedName}"`
    });

    return suggestions;
  }
}
