import { BaseService, ServiceConfig } from './base-service.js';

export interface SystemMetrics {
  timestamp: Date;
  queueSize: number;
  activeWorkers: number;
  averageProcessingTime: number;
  memoryUsage: number;
  cpuUsage: number;
  errorRate: number;
  throughput: number; // documents per hour
  systemLoad: 'low' | 'medium' | 'high';
}

export interface ProcessingTimeMetrics {
  documentType: 'index' | 'acte';
  circonscription: string;
  documentComplexity: 'simple' | 'medium' | 'complex';
  averageTime: number;
  medianTime: number;
  p95Time: number;
  successRate: number;
  totalAttempts: number;
  lastUpdated: Date;
}

export interface BottleneckPrediction {
  type: 'queue_overflow' | 'worker_shortage' | 'memory_pressure' | 'cpu_overload' | 'network_timeout';
  severity: 'low' | 'medium' | 'high' | 'critical';
  probability: number;
  estimatedTimeToOccurrence: number; // minutes
  currentTrend: 'improving' | 'stable' | 'degrading';
  recommendedActions: string[];
  affectedComponents: string[];
}

export interface CapacityPrediction {
  timeframe: '1h' | '4h' | '24h' | '7d';
  expectedLoad: number;
  recommendedWorkers: number;
  estimatedQueueTime: number;
  resourceUtilization: {
    cpu: number;
    memory: number;
    network: number;
  };
  optimizationSuggestions: string[];
}

export interface PerformanceInsight {
  category: 'efficiency' | 'reliability' | 'scalability' | 'optimization';
  title: string;
  description: string;
  impact: 'low' | 'medium' | 'high';
  implementationEffort: 'easy' | 'moderate' | 'complex';
  expectedImprovement: string;
  actionItems: string[];
}

export class PredictiveAnalyticsService extends BaseService {
  private metricsHistory: SystemMetrics[] = [];
  private processingTimeMetrics: Map<string, ProcessingTimeMetrics> = new Map();
  private bottleneckThresholds = {
    queueSize: { warning: 50, critical: 100 },
    averageProcessingTime: { warning: 120000, critical: 300000 }, // ms
    errorRate: { warning: 0.1, critical: 0.25 },
    memoryUsage: { warning: 0.8, critical: 0.95 },
    cpuUsage: { warning: 0.7, critical: 0.9 }
  };
  private maxHistorySize = 2000; // Keep 2000 metric points
  private predictionModels: Map<string, any> = new Map();

  constructor(config: ServiceConfig) {
    super(config);
  }

  async initialize(): Promise<void> {
    console.log('Initializing Predictive Analytics Service...');
    await this.loadHistoricalData();
    this.startMetricsCollection();
    this.startPredictionEngine();
    console.log('Predictive Analytics Service initialized successfully');
  }

  async shutdown(): Promise<void> {
    console.log('Shutting down Predictive Analytics Service...');
    await this.saveMetricsData();
    console.log('Predictive Analytics Service shutdown complete');
  }

  async healthCheck(): Promise<boolean> {
    // Check if we have recent metrics and prediction models are working
    const recentMetrics = this.metricsHistory.filter(
      m => Date.now() - m.timestamp.getTime() < 5 * 60 * 1000 // Last 5 minutes
    );
    return recentMetrics.length > 0;
  }

  /**
   * Record new system metrics
   */
  async recordMetrics(metrics: Partial<SystemMetrics>): Promise<void> {
    const completeMetrics: SystemMetrics = {
      timestamp: new Date(),
      queueSize: 0,
      activeWorkers: 1,
      averageProcessingTime: 90000,
      memoryUsage: 0.5,
      cpuUsage: 0.3,
      errorRate: 0.05,
      throughput: 20,
      systemLoad: 'medium',
      ...metrics
    };

    // Add to history
    this.metricsHistory.push(completeMetrics);

    // Keep only recent history
    if (this.metricsHistory.length > this.maxHistorySize) {
      this.metricsHistory = this.metricsHistory.slice(-this.maxHistorySize);
    }

    // Update prediction models
    this.updatePredictionModels();

    // Check for immediate bottlenecks
    const bottlenecks = this.detectImmediateBottlenecks(completeMetrics);
    if (bottlenecks.length > 0) {
      this.emit('bottleneck:detected', { bottlenecks, metrics: completeMetrics });
    }

    this.emit('metrics:recorded', completeMetrics);
  }

  /**
   * Record processing time for a specific document type and region
   */
  async recordProcessingTime(
    documentType: 'index' | 'acte',
    circonscription: string,
    processingTime: number,
    success: boolean,
    complexity: 'simple' | 'medium' | 'complex' = 'medium'
  ): Promise<void> {
    const key = `${documentType}:${circonscription}:${complexity}`;
    let metrics = this.processingTimeMetrics.get(key);

    if (!metrics) {
      metrics = {
        documentType,
        circonscription,
        documentComplexity: complexity,
        averageTime: processingTime,
        medianTime: processingTime,
        p95Time: processingTime,
        successRate: success ? 1 : 0,
        totalAttempts: 1,
        lastUpdated: new Date()
      };
    } else {
      // Update running statistics
      const newTotal = metrics.totalAttempts + 1;
      const successCount = metrics.successRate * metrics.totalAttempts + (success ? 1 : 0);
      
      metrics.averageTime = (metrics.averageTime * metrics.totalAttempts + processingTime) / newTotal;
      metrics.successRate = successCount / newTotal;
      metrics.totalAttempts = newTotal;
      metrics.lastUpdated = new Date();
    }

    this.processingTimeMetrics.set(key, metrics);
    this.emit('processing_time:recorded', { key, metrics, processingTime, success });
  }

  /**
   * Predict processing time for a document
   */
  async predictProcessingTime(
    documentType: 'index' | 'acte',
    circonscription: string,
    complexity: 'simple' | 'medium' | 'complex' = 'medium'
  ): Promise<{
    estimatedTime: number;
    confidence: number;
    range: { min: number; max: number };
    factors: string[];
  }> {
    const key = `${documentType}:${circonscription}:${complexity}`;
    const metrics = this.processingTimeMetrics.get(key);

    let baseTime = 90000; // Default 90 seconds
    let confidence = 0.5;
    const factors: string[] = [];

    if (metrics && metrics.totalAttempts >= 5) {
      baseTime = metrics.averageTime;
      confidence = Math.min(0.9, metrics.totalAttempts / 50);
      factors.push(`Based on ${metrics.totalAttempts} historical attempts`);
    } else {
      factors.push('Using default estimate (insufficient historical data)');
    }

    // Apply current system load adjustments
    const currentLoad = this.getCurrentSystemLoad();
    let loadMultiplier = 1;
    
    switch (currentLoad) {
      case 'high':
        loadMultiplier = 1.5;
        factors.push('System under high load (+50%)');
        break;
      case 'medium':
        loadMultiplier = 1.2;
        factors.push('System under medium load (+20%)');
        break;
      case 'low':
        loadMultiplier = 0.9;
        factors.push('System under low load (-10%)');
        break;
    }

    const adjustedTime = baseTime * loadMultiplier;

    // Apply document type adjustments
    if (documentType === 'index') {
      factors.push('Index documents typically faster (-10%)');
    }

    // Apply complexity adjustments
    switch (complexity) {
      case 'simple':
        factors.push('Simple document (-20%)');
        break;
      case 'complex':
        factors.push('Complex document (+30%)');
        break;
    }

    const finalTime = Math.round(adjustedTime);
    const range = {
      min: Math.round(finalTime * 0.7),
      max: Math.round(finalTime * 1.5)
    };

    return {
      estimatedTime: finalTime,
      confidence,
      range,
      factors
    };
  }

  /**
   * Predict potential bottlenecks
   */
  async predictBottlenecks(): Promise<BottleneckPrediction[]> {
    if (this.metricsHistory.length < 10) {
      return []; // Not enough data for predictions
    }

    const bottlenecks: BottleneckPrediction[] = [];
    const recentMetrics = this.metricsHistory.slice(-20); // Last 20 data points
    const currentMetrics = recentMetrics[recentMetrics.length - 1];

    // Queue overflow prediction
    const queueTrend = this.calculateTrend(recentMetrics.map(m => m.queueSize));
    if (queueTrend.slope > 2 && currentMetrics.queueSize > this.bottleneckThresholds.queueSize.warning) {
      const timeToOverflow = Math.max(5, (this.bottleneckThresholds.queueSize.critical - currentMetrics.queueSize) / queueTrend.slope);
      
      bottlenecks.push({
        type: 'queue_overflow',
        severity: currentMetrics.queueSize > this.bottleneckThresholds.queueSize.critical ? 'critical' : 'high',
        probability: Math.min(0.95, (currentMetrics.queueSize / this.bottleneckThresholds.queueSize.critical) * 0.8),
        estimatedTimeToOccurrence: timeToOverflow,
        currentTrend: 'degrading',
        recommendedActions: [
          'Scale up worker capacity',
          'Implement queue prioritization',
          'Consider load balancing'
        ],
        affectedComponents: ['Queue Manager', 'Worker Pool']
      });
    }

    // Memory pressure prediction
    const memoryTrend = this.calculateTrend(recentMetrics.map(m => m.memoryUsage));
    if (memoryTrend.slope > 0.05 && currentMetrics.memoryUsage > this.bottleneckThresholds.memoryUsage.warning) {
      const timeToOverload = Math.max(10, (this.bottleneckThresholds.memoryUsage.critical - currentMetrics.memoryUsage) / memoryTrend.slope);
      
      bottlenecks.push({
        type: 'memory_pressure',
        severity: currentMetrics.memoryUsage > this.bottleneckThresholds.memoryUsage.critical ? 'critical' : 'medium',
        probability: Math.min(0.9, currentMetrics.memoryUsage / this.bottleneckThresholds.memoryUsage.critical),
        estimatedTimeToOccurrence: timeToOverload,
        currentTrend: memoryTrend.slope > 0.02 ? 'degrading' : 'stable',
        recommendedActions: [
          'Clear cache data',
          'Restart services to free memory',
          'Optimize memory usage patterns'
        ],
        affectedComponents: ['Browser Sessions', 'Cache Manager', 'Analytics Service']
      });
    }

    // Processing time degradation
    const processingTimeTrend = this.calculateTrend(recentMetrics.map(m => m.averageProcessingTime));
    if (processingTimeTrend.slope > 5000 && currentMetrics.averageProcessingTime > this.bottleneckThresholds.averageProcessingTime.warning) {
      bottlenecks.push({
        type: 'network_timeout',
        severity: 'medium',
        probability: 0.7,
        estimatedTimeToOccurrence: 30,
        currentTrend: 'degrading',
        recommendedActions: [
          'Check network connectivity',
          'Implement connection pooling',
          'Add retry mechanisms'
        ],
        affectedComponents: ['Extraction Engine', 'Document Discovery']
      });
    }

    return bottlenecks;
  }

  /**
   * Predict capacity requirements
   */
  async predictCapacity(timeframe: '1h' | '4h' | '24h' | '7d'): Promise<CapacityPrediction> {
    const hoursMap = { '1h': 1, '4h': 4, '24h': 24, '7d': 168 };
    const hours = hoursMap[timeframe];
    
    // Get current metrics
    const currentMetrics = this.metricsHistory[this.metricsHistory.length - 1];
    if (!currentMetrics) {
      throw new Error('No current metrics available for capacity prediction');
    }

    // Calculate expected load based on historical patterns
    const expectedLoad = this.calculateExpectedLoad(hours);
    
    // Calculate required workers based on expected load
    const avgProcessingTime = currentMetrics.averageProcessingTime / 1000; // Convert to seconds
    const documentsPerWorkerPerHour = 3600 / avgProcessingTime; // Theoretical max
    const targetUtilization = 0.8; // 80% utilization target
    const recommendedWorkers = Math.ceil(expectedLoad / (documentsPerWorkerPerHour * targetUtilization));

    // Estimate queue time
    const currentThroughput = currentMetrics.throughput;
    const estimatedQueueTime = expectedLoad > currentThroughput ? 
      ((expectedLoad - currentThroughput) / currentThroughput) * 60 : 0; // minutes

    // Resource utilization predictions
    const resourceUtilization = {
      cpu: Math.min(0.9, currentMetrics.cpuUsage * (expectedLoad / currentMetrics.throughput)),
      memory: Math.min(0.95, currentMetrics.memoryUsage * (recommendedWorkers / currentMetrics.activeWorkers)),
      network: Math.min(0.8, 0.3 * (expectedLoad / 50)) // Estimate based on expected load
    };

    // Generate optimization suggestions
    const optimizationSuggestions = this.generateOptimizationSuggestions(
      expectedLoad,
      recommendedWorkers,
      resourceUtilization
    );

    return {
      timeframe,
      expectedLoad,
      recommendedWorkers,
      estimatedQueueTime,
      resourceUtilization,
      optimizationSuggestions
    };
  }

  /**
   * Get performance insights and recommendations
   */
  async getPerformanceInsights(): Promise<PerformanceInsight[]> {
    const insights: PerformanceInsight[] = [];

    // Analyze recent performance trends
    if (this.metricsHistory.length >= 20) {
      const recentMetrics = this.metricsHistory.slice(-20);
      
      // Error rate analysis
      const avgErrorRate = recentMetrics.reduce((sum, m) => sum + m.errorRate, 0) / recentMetrics.length;
      if (avgErrorRate > 0.15) {
        insights.push({
          category: 'reliability',
          title: 'High Error Rate Detected',
          description: `Current error rate is ${(avgErrorRate * 100).toFixed(1)}%, which is above the 15% threshold.`,
          impact: 'high',
          implementationEffort: 'moderate',
          expectedImprovement: 'Reduce error rate by 30-50%',
          actionItems: [
            'Implement more robust retry mechanisms',
            'Improve error handling in extraction strategies',
            'Add circuit breakers for failing services'
          ]
        });
      }

      // Processing time optimization
      const avgProcessingTime = recentMetrics.reduce((sum, m) => sum + m.averageProcessingTime, 0) / recentMetrics.length;
      if (avgProcessingTime > 120000) { // 2 minutes
        insights.push({
          category: 'efficiency',
          title: 'Processing Time Optimization Opportunity',
          description: `Average processing time is ${(avgProcessingTime / 1000).toFixed(1)} seconds, which could be improved.`,
          impact: 'medium',
          implementationEffort: 'moderate',
          expectedImprovement: 'Reduce processing time by 20-30%',
          actionItems: [
            'Implement smart caching for form layouts',
            'Optimize browser session management',
            'Use parallel processing for multiple documents'
          ]
        });
      }

      // Throughput analysis
      const avgThroughput = recentMetrics.reduce((sum, m) => sum + m.throughput, 0) / recentMetrics.length;
      if (avgThroughput < 30) { // Documents per hour
        insights.push({
          category: 'scalability',
          title: 'Low Throughput Detected',
          description: `Current throughput is ${avgThroughput.toFixed(1)} documents/hour, which is below optimal.`,
          impact: 'high',
          implementationEffort: 'complex',
          expectedImprovement: 'Increase throughput by 2-5x',
          actionItems: [
            'Implement worker pool with dynamic scaling',
            'Add intelligent batching strategies',
            'Optimize queue management algorithms'
          ]
        });
      }
    }

    // Resource utilization insights
    const latestMetrics = this.metricsHistory[this.metricsHistory.length - 1];
    if (latestMetrics) {
      if (latestMetrics.memoryUsage > 0.8) {
        insights.push({
          category: 'optimization',
          title: 'High Memory Usage',
          description: `Memory usage is at ${(latestMetrics.memoryUsage * 100).toFixed(1)}%, approaching critical levels.`,
          impact: 'medium',
          implementationEffort: 'easy',
          expectedImprovement: 'Reduce memory usage by 20-40%',
          actionItems: [
            'Implement cache eviction policies',
            'Optimize browser session lifecycle',
            'Add memory monitoring and alerts'
          ]
        });
      }
    }

    return insights;
  }

  /**
   * Get current system load assessment
   */
  private getCurrentSystemLoad(): 'low' | 'medium' | 'high' {
    const recentMetrics = this.metricsHistory.slice(-5);
    if (recentMetrics.length === 0) return 'medium';

    const avgQueueSize = recentMetrics.reduce((sum, m) => sum + m.queueSize, 0) / recentMetrics.length;
    const avgCpuUsage = recentMetrics.reduce((sum, m) => sum + m.cpuUsage, 0) / recentMetrics.length;
    
    if (avgQueueSize > 50 || avgCpuUsage > 0.8) return 'high';
    if (avgQueueSize > 20 || avgCpuUsage > 0.5) return 'medium';
    return 'low';
  }

  /**
   * Calculate trend for a series of values
   */
  private calculateTrend(values: number[]): { slope: number; r2: number } {
    if (values.length < 2) return { slope: 0, r2: 0 };

    const n = values.length;
    const x = Array.from({ length: n }, (_, i) => i);
    const meanX = x.reduce((sum, val) => sum + val, 0) / n;
    const meanY = values.reduce((sum, val) => sum + val, 0) / n;

    let numerator = 0;
    let denominatorX = 0;
    let denominatorY = 0;

    for (let i = 0; i < n; i++) {
      const deltaX = x[i] - meanX;
      const deltaY = values[i] - meanY;
      numerator += deltaX * deltaY;
      denominatorX += deltaX * deltaX;
      denominatorY += deltaY * deltaY;
    }

    const slope = denominatorX === 0 ? 0 : numerator / denominatorX;
    const r2 = denominatorX === 0 || denominatorY === 0 ? 0 : 
      (numerator * numerator) / (denominatorX * denominatorY);

    return { slope, r2 };
  }

  /**
   * Calculate expected load for future timeframe
   */
  private calculateExpectedLoad(hours: number): number {
    if (this.metricsHistory.length === 0) return 20; // Default assumption

    // Use recent throughput as baseline
    const recentThroughput = this.metricsHistory.slice(-10)
      .reduce((sum, m) => sum + m.throughput, 0) / Math.min(10, this.metricsHistory.length);

    // Apply time-of-day factors (simplified)
    const currentHour = new Date().getHours();
    let timeFactor = 1;
    
    if (currentHour >= 9 && currentHour <= 17) {
      timeFactor = 1.3; // Business hours are busier
    } else if (currentHour >= 18 && currentHour <= 23) {
      timeFactor = 0.8; // Evening is quieter
    } else {
      timeFactor = 0.5; // Night is very quiet
    }

    return Math.round(recentThroughput * hours * timeFactor);
  }

  /**
   * Generate optimization suggestions based on predictions
   */
  private generateOptimizationSuggestions(
    expectedLoad: number,
    recommendedWorkers: number,
    resourceUtilization: { cpu: number; memory: number; network: number }
  ): string[] {
    const suggestions: string[] = [];

    if (recommendedWorkers > 5) {
      suggestions.push('Consider implementing horizontal scaling with multiple worker instances');
    }

    if (resourceUtilization.cpu > 0.8) {
      suggestions.push('CPU usage will be high - consider optimizing processing algorithms');
    }

    if (resourceUtilization.memory > 0.9) {
      suggestions.push('Memory pressure expected - implement aggressive cache eviction');
    }

    if (expectedLoad > 100) {
      suggestions.push('High load expected - consider batching similar documents');
    }

    if (expectedLoad < 10) {
      suggestions.push('Low load period - ideal time for maintenance and optimization');
    }

    return suggestions;
  }

  /**
   * Detect immediate bottlenecks from current metrics
   */
  private detectImmediateBottlenecks(metrics: SystemMetrics): BottleneckPrediction[] {
    const bottlenecks: BottleneckPrediction[] = [];

    // Queue size check
    if (metrics.queueSize > this.bottleneckThresholds.queueSize.critical) {
      bottlenecks.push({
        type: 'queue_overflow',
        severity: 'critical',
        probability: 1.0,
        estimatedTimeToOccurrence: 0,
        currentTrend: 'degrading',
        recommendedActions: ['Immediate worker scaling required'],
        affectedComponents: ['Queue Manager']
      });
    }

    // Memory usage check
    if (metrics.memoryUsage > this.bottleneckThresholds.memoryUsage.critical) {
      bottlenecks.push({
        type: 'memory_pressure',
        severity: 'critical',
        probability: 1.0,
        estimatedTimeToOccurrence: 0,
        currentTrend: 'degrading',
        recommendedActions: ['Clear caches immediately', 'Restart services'],
        affectedComponents: ['All Services']
      });
    }

    return bottlenecks;
  }

  /**
   * Update prediction models with new data
   */
  private updatePredictionModels(): void {
    // This is a simplified version - in a real implementation,
    // you'd update more sophisticated ML models here
    if (this.metricsHistory.length < 50) return;

    // Update simple moving average models
    const recentMetrics = this.metricsHistory.slice(-50);
    const avgProcessingTime = recentMetrics.reduce((sum, m) => sum + m.averageProcessingTime, 0) / recentMetrics.length;
    const avgThroughput = recentMetrics.reduce((sum, m) => sum + m.throughput, 0) / recentMetrics.length;

    this.predictionModels.set('avg_processing_time', avgProcessingTime);
    this.predictionModels.set('avg_throughput', avgThroughput);
  }

  /**
   * Start automated metrics collection
   */
  private startMetricsCollection(): void {
    // Collect system metrics every 30 seconds
    setInterval(async () => {
      try {
        // In a real implementation, you'd collect actual system metrics
        const metrics: Partial<SystemMetrics> = {
          timestamp: new Date(),
          queueSize: Math.floor(Math.random() * 30), // Mock data
          activeWorkers: Math.floor(Math.random() * 5) + 1,
          memoryUsage: Math.random() * 0.8,
          cpuUsage: Math.random() * 0.6,
          errorRate: Math.random() * 0.1,
          throughput: Math.floor(Math.random() * 40) + 10
        };

        await this.recordMetrics(metrics);
      } catch (error) {
        this.handleError(error, 'metrics_collection');
      }
    }, 30000); // Every 30 seconds
  }

  /**
   * Start prediction engine
   */
  private startPredictionEngine(): void {
    // Run bottleneck predictions every 5 minutes
    setInterval(async () => {
      try {
        const bottlenecks = await this.predictBottlenecks();
        if (bottlenecks.length > 0) {
          this.emit('predictions:bottlenecks', bottlenecks);
        }

        // Run capacity predictions for multiple timeframes
        const capacityPredictions: Record<string, CapacityPrediction> = {};
        for (const timeframe of ['1h', '4h', '24h'] as const) {
          capacityPredictions[timeframe] = await this.predictCapacity(timeframe);
        }

        this.emit('predictions:capacity', capacityPredictions);
      } catch (error) {
        this.handleError(error, 'prediction_engine');
      }
    }, 5 * 60 * 1000); // Every 5 minutes
  }

  /**
   * Load historical data from storage (mock implementation)
   */
  private async loadHistoricalData(): Promise<void> {
    console.log('Loading historical analytics data...');
    // In a real implementation, this would load from database
  }

  /**
   * Save metrics data to storage (mock implementation)
   */
  private async saveMetricsData(): Promise<void> {
    console.log(`Saving ${this.metricsHistory.length} analytics data points to storage...`);
    // In a real implementation, this would save to database
  }
}
