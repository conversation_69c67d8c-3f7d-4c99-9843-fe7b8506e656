import { BaseService } from './base-service';
import { SupabaseService } from '../supabase-client';
import { Alert } from '../types'; // Import Alert from types.ts

export interface NotificationConfig {
  name: string;
  channels: NotificationChannel[];
  escalationRules: EscalationRule[];
  deduplicationWindow: number; // minutes
  maxNotificationsPerHour: number;
  enableIntelligentFiltering: boolean;
  n8nWebhookUrl?: string;
}

export interface NotificationChannel {
  id: string;
  type: 'email' | 'slack' | 'webhook' | 'n8n';
  name: string;
  config: {
    url?: string;
    headers?: Record<string, string>;
    recipients?: string[];
    webhook_id?: string;
  };
  enabled: boolean;
  severity: ('low' | 'medium' | 'high' | 'critical')[];
}

export interface EscalationRule {
  id: string;
  name: string;
  trigger: {
    severity: 'low' | 'medium' | 'high' | 'critical';
    unacknowledgedTime: number; // minutes
    count?: number;
  };
  action: {
    type: 'escalate' | 'repeat' | 'suppress';
    channelIds: string[];
    delay?: number; // minutes
  };
}

export interface NotificationStats {
  totalSent: number;
  byChannel: Record<string, number>;
  bySeverity: Record<string, number>;
  acknowledgmentRate: number;
  averageResolutionTime: number;
  suppressedCount: number;
  escalatedCount: number;
}

export class NotificationService extends BaseService {
  private supabaseService: SupabaseService;
  private notificationConfig: NotificationConfig;
  private activeAlerts: Map<string, Alert> = new Map();
  private alertHistory: Alert[] = [];
  private notificationStats: NotificationStats;
  private notificationCounts: Map<string, number> = new Map(); // For rate limiting
  private deduplicationCache: Map<string, Date> = new Map();

  constructor(config: NotificationConfig, supabaseService: SupabaseService) {
    super(config);
    this.notificationConfig = config;
    this.supabaseService = supabaseService;
    this.notificationStats = {
      totalSent: 0,
      byChannel: {},
      bySeverity: {},
      acknowledgmentRate: 0,
      averageResolutionTime: 0,
      suppressedCount: 0,
      escalatedCount: 0
    };
  }

  async initialize(): Promise<void> {
    console.log('Initializing Notification Service...');
    
    // Load active alerts from database
    await this.loadActiveAlerts();
    
    // Setup escalation monitoring
    this.setupEscalationMonitoring();
    
    // Setup cleanup tasks
    this.setupCleanupTasks();
    
    // Setup rate limit reset
    this.setupRateLimitReset();
    
    console.log('Notification Service initialized successfully');
  }

  async shutdown(): Promise<void> {
    console.log('Shutting down Notification Service...');
    
    // Save active alerts
    await this.saveActiveAlerts();
    
    console.log('Notification Service shutdown complete');
  }

  async healthCheck(): Promise<boolean> {
    try {
      // Check if channels are reachable
      for (const channel of this.notificationConfig.channels) {
        if (channel.enabled && channel.type === 'webhook' && channel.config.url) {
          // In a real implementation, this would test the webhook
        }
      }
      
      // Check if we're not overwhelmed with alerts
      const criticalAlerts = Array.from(this.activeAlerts.values())
        .filter(alert => alert.severity === 'critical' && !alert.acknowledged);
      
      return criticalAlerts.length < 10;
    } catch (error) {
      return false;
    }
  }

  /**
   * Send an alert through appropriate channels
   */
  async sendAlert(alertData: Omit<Alert, 'id' | 'timestamp' | 'acknowledged' | 'resolved' | 'escalationLevel' | 'type'>): Promise<string> {
    const alertId = this.generateAlertId();
    const alert: Alert = {
      id: alertId,
      timestamp: new Date(),
      acknowledged: false,
      resolved: false,
      escalationLevel: 0,
      type: 'notification', // Default type for alerts sent via NotificationService
      ...alertData
    };

    // Check for deduplication
    if (this.shouldDeduplicate(alert)) {
      console.log(`Alert deduplicated: ${alert.title}`);
      return alertId;
    }

    // Check for intelligent filtering
    if (this.shouldFilter(alert)) {
      console.log(`Alert filtered: ${alert.title}`);
      this.notificationStats.suppressedCount++;
      return alertId;
    }

    // Store alert
    this.activeAlerts.set(alertId, alert);
    this.alertHistory.push(alert);

    // Send through appropriate channels
    await this.routeAlert(alert);

    // Update stats
    this.updateNotificationStats(alert);

    // Update deduplication cache
    this.updateDeduplicationCache(alert);

    this.emit('alert:sent', {
      alertId,
      severity: alert.severity,
      category: alert.category,
      title: alert.title
    });

    return alertId;
  }

  /**
   * Acknowledge an alert
   */
  async acknowledgeAlert(alertId: string, acknowledgedBy: string): Promise<boolean> {
    const alert = this.activeAlerts.get(alertId);
    if (!alert) {
      return false;
    }

    alert.acknowledged = true;
    alert.acknowledgedBy = acknowledgedBy;
    alert.acknowledgedAt = new Date();

    // Update in database
    await this.updateAlertInDatabase(alert);

    this.emit('alert:acknowledged', {
      alertId,
      acknowledgedBy,
      acknowledgedAt: alert.acknowledgedAt
    });

    return true;
  }

  /**
   * Resolve an alert
   */
  async resolveAlert(alertId: string, resolvedBy?: string): Promise<boolean> {
    const alert = this.activeAlerts.get(alertId);
    if (!alert) {
      return false;
    }

    alert.resolved = true;
    alert.resolvedAt = new Date();

    // Remove from active alerts
    this.activeAlerts.delete(alertId);

    // Update in database
    await this.updateAlertInDatabase(alert);

    // Update stats
    this.updateResolutionStats(alert);

    this.emit('alert:resolved', {
      alertId,
      resolvedBy,
      resolvedAt: alert.resolvedAt
    });

    return true;
  }

  /**
   * Get active alerts
   */
  getActiveAlerts(filters?: {
    severity?: string[];
    category?: string[];
    acknowledged?: boolean;
  }): Alert[] {
    let alerts = Array.from(this.activeAlerts.values());

    if (filters) {
      if (filters.severity) {
        alerts = alerts.filter(alert => filters.severity!.includes(alert.severity));
      }
      if (filters.category) {
        alerts = alerts.filter(alert => filters.category!.includes(alert.category));
      }
      if (filters.acknowledged !== undefined) {
        alerts = alerts.filter(alert => alert.acknowledged === filters.acknowledged);
      }
    }

    return alerts.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());
  }

  /**
   * Get notification statistics
   */
  getNotificationStats(timeframe: 'hour' | 'day' | 'week' = 'day'): NotificationStats {
    // In a real implementation, this would calculate stats from stored data
    return this.notificationStats;
  }

  /**
   * Suppress alerts temporarily
   */
  async suppressAlerts(
    criteria: {
      category?: string;
      severity?: string;
      service?: string;
    },
    durationMinutes: number
  ): Promise<void> {
    const suppressUntil = new Date(Date.now() + durationMinutes * 60 * 1000);

    // Apply suppression to matching active alerts
    for (const alert of this.activeAlerts.values()) {
      if (this.alertMatchesCriteria(alert, criteria)) {
        alert.suppressUntil = suppressUntil;
      }
    }

    this.emit('alerts:suppressed', {
      criteria,
      suppressUntil,
      affectedCount: Array.from(this.activeAlerts.values())
        .filter(alert => this.alertMatchesCriteria(alert, criteria)).length
    });
  }

  /**
   * Configure notification channels
   */
  updateChannelConfig(channelId: string, updates: Partial<NotificationChannel>): boolean {
    const channelIndex = this.notificationConfig.channels.findIndex(c => c.id === channelId);
    if (channelIndex === -1) {
      return false;
    }

    this.notificationConfig.channels[channelIndex] = {
      ...this.notificationConfig.channels[channelIndex],
      ...updates
    };

    this.emit('channel:updated', { channelId, updates });
    return true;
  }

  /**
   * Test notification channel
   */
  async testChannel(channelId: string): Promise<{ success: boolean; error?: string }> {
    const channel = this.notificationConfig.channels.find(c => c.id === channelId);
    if (!channel) {
      return { success: false, error: 'Channel not found' };
    }

    const testAlert: Alert = {
      id: 'test',
      timestamp: new Date(),
      severity: 'low',
      category: 'system',
      title: 'Test Notification',
      message: 'This is a test notification to verify channel configuration.',
      context: {},
      acknowledged: false,
      resolved: false,
      escalationLevel: 0
    };

    try {
      await this.sendToChannel(channel, testAlert);
      return { success: true };
    } catch (error) {
      return { 
        success: false, 
        error: error instanceof Error ? error.message : String(error) 
      };
    }
  }

  /**
   * Private helper methods
   */
  private async loadActiveAlerts(): Promise<void> {
    // In a real implementation, this would load from database
    console.log('Loading active alerts from database...');
  }

  private async saveActiveAlerts(): Promise<void> {
    // In a real implementation, this would save to database
    console.log('Saving active alerts to database...');
  }

  private async updateAlertInDatabase(alert: Alert): Promise<void> {
    // In a real implementation, this would update in database
    console.log(`Updating alert ${alert.id} in database`);
  }

  private shouldDeduplicate(alert: Alert): boolean {
    const key = this.getDeduplicationKey(alert);
    const lastSent = this.deduplicationCache.get(key);
    
    if (!lastSent) {
      return false;
    }

    const windowMs = this.notificationConfig.deduplicationWindow * 60 * 1000;
    return Date.now() - lastSent.getTime() < windowMs;
  }

  private shouldFilter(alert: Alert): boolean {
    if (!this.notificationConfig.enableIntelligentFiltering) {
      return false;
    }

    // Check rate limits
    const hour = new Date().getHours();
    const hourKey = `${hour}`;
    const currentCount = this.notificationCounts.get(hourKey) || 0;
    
    if (currentCount >= this.notificationConfig.maxNotificationsPerHour) {
      return true;
    }

    // Check if alert is suppressed
    if (alert.suppressUntil && alert.suppressUntil > new Date()) {
      return true;
    }

    // Additional intelligent filtering logic could go here
    return false;
  }

  private async routeAlert(alert: Alert): Promise<void> {
    const appropriateChannels = this.notificationConfig.channels.filter(channel => 
      channel.enabled && channel.severity.includes(alert.severity)
    );

    const sendPromises = appropriateChannels.map(channel => 
      this.sendToChannel(channel, alert).catch(error => {
        console.error(`Failed to send alert to channel ${channel.id}:`, error);
        this.emit('channel:error', { channelId: channel.id, error });
      })
    );

    await Promise.allSettled(sendPromises);
  }

  private async sendToChannel(channel: NotificationChannel, alert: Alert): Promise<void> {
    switch (channel.type) {
      case 'n8n':
        await this.sendToN8n(channel, alert);
        break;
      case 'webhook':
        await this.sendToWebhook(channel, alert);
        break;
      case 'email':
        await this.sendToEmail(channel, alert);
        break;
      case 'slack':
        await this.sendToSlack(channel, alert);
        break;
      default:
        throw new Error(`Unsupported channel type: ${channel.type}`);
    }
  }

  private async sendToN8n(channel: NotificationChannel, alert: Alert): Promise<void> {
    const webhookUrl = this.notificationConfig.n8nWebhookUrl || channel.config.url;
    if (!webhookUrl) {
      throw new Error('No n8n webhook URL configured');
    }

    const payload = {
      type: 'stagehand_alert',
      alert: {
        id: alert.id,
        severity: alert.severity,
        category: alert.category,
        title: alert.title,
        message: alert.message,
        timestamp: alert.timestamp.toISOString(),
        context: alert.context
      },
      channel: channel.name
    };

    await this.makeHttpRequest(webhookUrl, 'POST', payload, channel.config.headers);
  }

  private async sendToWebhook(channel: NotificationChannel, alert: Alert): Promise<void> {
    if (!channel.config.url) {
      throw new Error('No webhook URL configured');
    }

    const payload = {
      alert,
      channel: channel.name,
      timestamp: new Date().toISOString()
    };

    await this.makeHttpRequest(channel.config.url, 'POST', payload, channel.config.headers);
  }

  private async sendToEmail(channel: NotificationChannel, alert: Alert): Promise<void> {
    // In a real implementation, this would send email
    console.log(`Sending email alert: ${alert.title} to ${channel.config.recipients?.join(', ')}`);
  }

  private async sendToSlack(channel: NotificationChannel, alert: Alert): Promise<void> {
    // In a real implementation, this would send to Slack
    console.log(`Sending Slack alert: ${alert.title}`);
  }

  private async makeHttpRequest(
    url: string, 
    method: string, 
    body: any, 
    headers: Record<string, string> = {}
  ): Promise<void> {
    // In a real implementation, this would make the actual HTTP request
    console.log(`Making ${method} request to ${url}`, { body, headers });
  }

  private updateNotificationStats(alert: Alert): void {
    this.notificationStats.totalSent++;
    this.notificationStats.bySeverity[alert.severity] = 
      (this.notificationStats.bySeverity[alert.severity] || 0) + 1;

    // Update hourly rate limiting
    const hour = new Date().getHours();
    const hourKey = `${hour}`;
    this.notificationCounts.set(hourKey, (this.notificationCounts.get(hourKey) || 0) + 1);
  }

  private updateResolutionStats(alert: Alert): void {
    if (alert.resolvedAt && alert.timestamp) {
      const resolutionTime = alert.resolvedAt.getTime() - alert.timestamp.getTime();
      // Update average resolution time calculation
    }
  }

  private updateDeduplicationCache(alert: Alert): void {
    const key = this.getDeduplicationKey(alert);
    this.deduplicationCache.set(key, new Date());
  }

  private getDeduplicationKey(alert: Alert): string {
    return `${alert.category}:${alert.title}:${alert.context.service || ''}:${alert.context.errorType || ''}`;
  }

  private alertMatchesCriteria(alert: Alert, criteria: any): boolean {
    if (criteria.category && alert.category !== criteria.category) return false;
    if (criteria.severity && alert.severity !== criteria.severity) return false;
    if (criteria.service && alert.context.service !== criteria.service) return false;
    return true;
  }

  private generateAlertId(): string {
    return `alert_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private setupEscalationMonitoring(): void {
    setInterval(() => {
      this.checkForEscalations();
    }, 60000); // Check every minute
  }

  private setupCleanupTasks(): void {
    setInterval(() => {
      this.cleanupOldAlerts();
      this.cleanupDeduplicationCache();
    }, 60 * 60 * 1000); // Every hour
  }

  private setupRateLimitReset(): void {
    setInterval(() => {
      // Reset rate limiting counters every hour
      this.notificationCounts.clear();
    }, 60 * 60 * 1000);
  }

  private checkForEscalations(): void {
    const now = new Date();
    
    for (const alert of this.activeAlerts.values()) {
      if (alert.acknowledged || alert.suppressUntil && alert.suppressUntil > now) {
        continue;
      }

      for (const rule of this.notificationConfig.escalationRules) {
        if (this.shouldEscalate(alert, rule, now)) {
          this.escalateAlert(alert, rule);
        }
      }
    }
  }

  private shouldEscalate(alert: Alert, rule: EscalationRule, now: Date): boolean {
    if (alert.severity !== rule.trigger.severity) {
      return false;
    }

    const timeSinceAlert = now.getTime() - alert.timestamp.getTime();
    const escalationTime = rule.trigger.unacknowledgedTime * 60 * 1000;

    return timeSinceAlert >= escalationTime && alert.escalationLevel < 3; // Max 3 escalation levels
  }

  private async escalateAlert(alert: Alert, rule: EscalationRule): Promise<void> {
    alert.escalationLevel++;
    this.notificationStats.escalatedCount++;

    // Send escalated notification
    const escalatedAlert = {
      ...alert,
      title: `[ESCALATED] ${alert.title}`,
      message: `Alert has been escalated (Level ${alert.escalationLevel}). Original: ${alert.message}`
    };

    // Send to escalation channels
    const escalationChannels = this.notificationConfig.channels.filter(c => 
      rule.action.channelIds.includes(c.id) && c.enabled
    );

    for (const channel of escalationChannels) {
      try {
        await this.sendToChannel(channel, escalatedAlert);
      } catch (error) {
        console.error(`Failed to send escalated alert to channel ${channel.id}:`, error);
      }
    }

    this.emit('alert:escalated', {
      alertId: alert.id,
      escalationLevel: alert.escalationLevel,
      rule: rule.name
    });
  }

  private cleanupOldAlerts(): void {
    const cutoff = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000); // 7 days
    this.alertHistory = this.alertHistory.filter(alert => alert.timestamp > cutoff);
  }

  private cleanupDeduplicationCache(): void {
    const cutoff = new Date(Date.now() - this.notificationConfig.deduplicationWindow * 60 * 1000);
    for (const [key, timestamp] of this.deduplicationCache.entries()) {
      if (timestamp < cutoff) {
        this.deduplicationCache.delete(key);
      }
    }
  }
}
