import { ExtractionQueueItem } from '../config';

export interface QueueItem {
  id: string;
  priority: number;
  timestamp: Date;
  data: ExtractionQueueItem;
  retryCount: number;
  lastError?: string;
}

export interface PriorityQueueConfig {
  maxSize: number;
  enablePersistence: boolean;
  persistenceFile?: string;
}

export class PriorityQueue {
  private items: QueueItem[] = [];
  private config: PriorityQueueConfig;

  // Priority weights - Index documents always processed first
  private static readonly PRIORITY_WEIGHTS = {
    index: 1000,
    acte: 100
  };

  constructor(config: PriorityQueueConfig = { maxSize: 1000, enablePersistence: false }) {
    this.config = config;
  }

  /**
   * Add an item to the priority queue
   */
  enqueue(item: ExtractionQueueItem): void {
    if (this.items.length >= this.config.maxSize) {
      throw new Error(`Queue is at maximum capacity: ${this.config.maxSize}`);
    }

    const priority = this.calculatePriority(item);
    const queueItem: QueueItem = {
      id: item.id,
      priority,
      timestamp: new Date(),
      data: item,
      retryCount: item.retry_count || 0,
      lastError: item.error_message
    };

    // Insert in priority order (highest priority first)
    let insertIndex = 0;
    while (insertIndex < this.items.length && this.items[insertIndex].priority >= priority) {
      insertIndex++;
    }

    this.items.splice(insertIndex, 0, queueItem);

    console.log(`Enqueued item ${item.id} with priority ${priority} (${item.document_source}: ${item.document_number})`);
  }

  /**
   * Remove and return the highest priority item
   */
  dequeue(): QueueItem | null {
    if (this.items.length === 0) {
      return null;
    }

    const item = this.items.shift();
    if (item) {
      console.log(`Dequeued item ${item.id} with priority ${item.priority}`);
    }
    return item || null;
  }

  /**
   * Peek at the highest priority item without removing it
   */
  peek(): QueueItem | null {
    return this.items.length > 0 ? this.items[0] : null;
  }

  /**
   * Get current queue size
   */
  size(): number {
    return this.items.length;
  }

  /**
   * Check if queue is empty
   */
  isEmpty(): boolean {
    return this.items.length === 0;
  }

  /**
   * Get queue statistics
   */
  getStats(): {
    total: number;
    byDocumentType: Record<string, number>;
    averagePriority: number;
    oldestItem?: Date;
    newestItem?: Date;
  } {
    const stats = {
      total: this.items.length,
      byDocumentType: {} as Record<string, number>,
      averagePriority: 0,
      oldestItem: undefined as Date | undefined,
      newestItem: undefined as Date | undefined
    };

    if (this.items.length === 0) {
      return stats;
    }

    let totalPriority = 0;
    let oldest = this.items[0].timestamp;
    let newest = this.items[0].timestamp;

    for (const item of this.items) {
      // Count by document type
      const docType = item.data.document_source;
      stats.byDocumentType[docType] = (stats.byDocumentType[docType] || 0) + 1;

      // Calculate average priority
      totalPriority += item.priority;

      // Track oldest and newest
      if (item.timestamp < oldest) oldest = item.timestamp;
      if (item.timestamp > newest) newest = item.timestamp;
    }

    stats.averagePriority = totalPriority / this.items.length;
    stats.oldestItem = oldest;
    stats.newestItem = newest;

    return stats;
  }

  /**
   * Remove a specific item by ID
   */
  remove(itemId: string): boolean {
    const index = this.items.findIndex(item => item.id === itemId);
    if (index !== -1) {
      this.items.splice(index, 1);
      console.log(`Removed item ${itemId} from queue`);
      return true;
    }
    return false;
  }

  /**
   * Update retry count for an item
   */
  updateRetryCount(itemId: string, retryCount: number, error?: string): boolean {
    const item = this.items.find(item => item.id === itemId);
    if (item) {
      item.retryCount = retryCount;
      if (error) {
        item.lastError = error;
      }
      
      // Recalculate priority (failures reduce priority slightly)
      const newPriority = this.calculatePriority(item.data, retryCount);
      item.priority = newPriority;
      
      // Re-sort the queue
      this.resort();
      return true;
    }
    return false;
  }

  /**
   * Get items by document type
   */
  getItemsByType(documentType: 'acte' | 'index'): QueueItem[] {
    return this.items.filter(item => item.data.document_source === documentType);
  }

  /**
   * Clear all items from the queue
   */
  clear(): void {
    this.items = [];
    console.log('Queue cleared');
  }

  /**
   * Get a batch of items for processing (highest priority first)
   */
  getBatch(batchSize: number): QueueItem[] {
    const batch = this.items.splice(0, Math.min(batchSize, this.items.length));
    console.log(`Retrieved batch of ${batch.length} items`);
    return batch;
  }

  /**
   * Return items to the queue (for failed processing)
   */
  returnBatch(items: QueueItem[]): void {
    for (const item of items) {
      // Increase retry count and reduce priority slightly
      item.retryCount++;
      item.priority = this.calculatePriority(item.data, item.retryCount);
      
      // Re-insert in priority order
      let insertIndex = 0;
      while (insertIndex < this.items.length && this.items[insertIndex].priority >= item.priority) {
        insertIndex++;
      }
      this.items.splice(insertIndex, 0, item);
    }
    console.log(`Returned ${items.length} items to queue`);
  }

  /**
   * Calculate priority for an item
   * Index documents: 1000 + time bonus
   * Acte documents: 100 + time bonus
   * Failed items get slight priority reduction
   */
  private calculatePriority(item: ExtractionQueueItem, retryCount = 0): number {
    const baseWeight = PriorityQueue.PRIORITY_WEIGHTS[item.document_source] || 50;
    
    // Time-based sub-priority (older items get slightly higher priority)
    const createdAt = new Date(item.processing_started_at || Date.now());
    const ageInMinutes = (Date.now() - createdAt.getTime()) / (1000 * 60);
    const timePriority = Math.min(ageInMinutes * 0.1, 10); // Max 10 points for age
    
    // Retry penalty (reduce priority slightly for failed items)
    const retryPenalty = retryCount * 5;
    
    return Math.max(1, baseWeight + timePriority - retryPenalty);
  }

  /**
   * Resort the queue by priority
   */
  private resort(): void {
    this.items.sort((a, b) => b.priority - a.priority);
  }

  /**
   * Export queue state for persistence
   */
  exportState(): any {
    return {
      items: this.items,
      timestamp: new Date(),
      config: this.config
    };
  }

  /**
   * Import queue state from persistence
   */
  importState(state: any): void {
    if (state && state.items && Array.isArray(state.items)) {
      this.items = state.items.map((item: any) => ({
        ...item,
        timestamp: new Date(item.timestamp)
      }));
      this.resort();
      console.log(`Imported ${this.items.length} items from persistent state`);
    }
  }
}
