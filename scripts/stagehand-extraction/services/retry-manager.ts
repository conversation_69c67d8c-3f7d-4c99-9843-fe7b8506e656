import { BaseService, ServiceConfig } from './base-service.js';

export interface RetryStrategy {
  name: string;
  maxAttempts: number;
  baseDelay: number;
  maxDelay: number;
  backoffMultiplier: number;
  jitterPercent: number;
  contextualFactors: string[];
}

export interface RetryContext {
  operation: string;
  errorType: string;
  documentType: 'index' | 'acte';
  systemLoad: 'low' | 'medium' | 'high';
  timeOfDay: number; // Hour of day (0-23)
  isMaintenanceWindow: boolean;
  previousAttempts: number;
  lastAttemptTime: Date;
}

export interface RetryPattern {
  errorType: string;
  documentType: string;
  optimalDelay: number;
  successRate: number;
  totalAttempts: number;
  successfulAttempts: number;
  lastUpdated: Date;
}

export interface RetryBudget {
  operation: string;
  maxAttemptsPerHour: number;
  currentAttempts: number;
  resetTime: Date;
}

export class RetryManager extends BaseService {
  private strategies: Map<string, RetryStrategy> = new Map();
  private patterns: Map<string, RetryPattern> = new Map();
  private budgets: Map<string, RetryBudget> = new Map();
  private maintenanceWindows: Array<{ start: number; end: number }> = [];

  constructor(config: ServiceConfig) {
    super(config);
    this.initializeDefaultStrategies();
    this.setupMaintenanceWindows();
  }

  async initialize(): Promise<void> {
    console.log('Initializing RetryManager...');
    await this.loadRetryPatterns();
    this.startBudgetResetTimer();
    console.log('RetryManager initialized successfully');
  }

  async shutdown(): Promise<void> {
    console.log('Shutting down RetryManager...');
    await this.saveRetryPatterns();
    console.log('RetryManager shutdown complete');
  }

  async healthCheck(): Promise<boolean> {
    // Check if strategy selection is working
    const testContext: RetryContext = {
      operation: 'test',
      errorType: 'network',
      documentType: 'index',
      systemLoad: 'low',
      timeOfDay: new Date().getHours(),
      isMaintenanceWindow: false,
      previousAttempts: 0,
      lastAttemptTime: new Date()
    };

    try {
      const strategy = this.selectOptimalStrategy(testContext);
      return strategy !== null;
    } catch {
      return false;
    }
  }

  /**
   * Determine if a retry should be attempted
   */
  shouldRetry(context: RetryContext): boolean {
    // Check maintenance window
    if (context.isMaintenanceWindow) {
      console.log(`Skipping retry during maintenance window for ${context.operation}`);
      return false;
    }

    // Check retry budget
    if (!this.checkRetryBudget(context.operation)) {
      console.log(`Retry budget exceeded for ${context.operation}`);
      return false;
    }

    // Check if max attempts reached for any strategy
    const strategy = this.selectOptimalStrategy(context);
    if (!strategy || context.previousAttempts >= strategy.maxAttempts) {
      return false;
    }

    // Check success probability
    const successProbability = this.calculateSuccessProbability(context);
    if (successProbability < 0.1) { // Less than 10% chance of success
      console.log(`Low success probability (${successProbability}) for ${context.operation}, skipping retry`);
      return false;
    }

    return true;
  }

  /**
   * Calculate optimal retry delay
   */
  calculateRetryDelay(context: RetryContext): number {
    const strategy = this.selectOptimalStrategy(context);
    if (!strategy) {
      return 1000; // Default 1 second
    }

    // Get learned optimal delay if available
    const patternKey = this.getPatternKey(context);
    const pattern = this.patterns.get(patternKey);
    if (pattern && pattern.successRate > 0.5) {
      return this.applyJitter(pattern.optimalDelay, strategy.jitterPercent);
    }

    // Calculate exponential backoff with context factors
    let delay = strategy.baseDelay * Math.pow(strategy.backoffMultiplier, context.previousAttempts);
    
    // Apply contextual factors
    delay = this.applyContextualFactors(delay, context, strategy);
    
    // Ensure within bounds
    delay = Math.min(delay, strategy.maxDelay);
    
    // Apply jitter
    return this.applyJitter(delay, strategy.jitterPercent);
  }

  /**
   * Record retry attempt result for learning
   */
  recordRetryResult(context: RetryContext, success: boolean, actualDelay: number): void {
    const patternKey = this.getPatternKey(context);
    let pattern = this.patterns.get(patternKey);

    if (!pattern) {
      pattern = {
        errorType: context.errorType,
        documentType: context.documentType,
        optimalDelay: actualDelay,
        successRate: 0,
        totalAttempts: 0,
        successfulAttempts: 0,
        lastUpdated: new Date()
      };
    }

    // Update pattern statistics
    pattern.totalAttempts++;
    if (success) {
      pattern.successfulAttempts++;
      // Update optimal delay with exponential moving average
      const alpha = 0.3; // Learning rate
      pattern.optimalDelay = alpha * actualDelay + (1 - alpha) * pattern.optimalDelay;
    }

    pattern.successRate = pattern.successfulAttempts / pattern.totalAttempts;
    pattern.lastUpdated = new Date();

    this.patterns.set(patternKey, pattern);

    // Update retry budget
    this.updateRetryBudget(context.operation);

    // Emit learning event
    this.emit('retry:learned', {
      context,
      success,
      pattern: { ...pattern }
    });
  }

  /**
   * Get retry recommendation for operation
   */
  getRetryRecommendation(context: RetryContext): {
    shouldRetry: boolean;
    delay: number;
    strategy: string;
    confidence: number;
  } {
    const shouldRetry = this.shouldRetry(context);
    const delay = shouldRetry ? this.calculateRetryDelay(context) : 0;
    const strategy = this.selectOptimalStrategy(context)?.name || 'none';
    const confidence = this.calculateSuccessProbability(context);

    return {
      shouldRetry,
      delay,
      strategy,
      confidence
    };
  }

  /**
   * Select optimal retry strategy based on context
   */
  private selectOptimalStrategy(context: RetryContext): RetryStrategy | null {
    // For network errors during high load, use conservative strategy
    if (context.errorType === 'network' && context.systemLoad === 'high') {
      return this.strategies.get('conservative') || null;
    }

    // For timeout errors with index documents, use aggressive strategy
    if (context.errorType === 'timeout' && context.documentType === 'index') {
      return this.strategies.get('aggressive') || null;
    }

    // For authentication errors, use immediate strategy
    if (context.errorType === 'authentication') {
      return this.strategies.get('immediate') || null;
    }

    // For rate limiting, use patient strategy
    if (context.errorType === 'rate_limit') {
      return this.strategies.get('patient') || null;
    }

    // Default to adaptive strategy
    return this.strategies.get('adaptive') || null;
  }

  /**
   * Calculate success probability based on historical data
   */
  private calculateSuccessProbability(context: RetryContext): number {
    const patternKey = this.getPatternKey(context);
    const pattern = this.patterns.get(patternKey);

    if (!pattern || pattern.totalAttempts < 5) {
      // Default probability for unknown patterns
      return 0.5;
    }

    // Adjust probability based on context
    let probability = pattern.successRate;

    // Reduce probability during high load
    if (context.systemLoad === 'high') {
      probability *= 0.7;
    }

    // Reduce probability for repeated attempts
    probability *= Math.pow(0.8, context.previousAttempts);

    // Reduce probability during peak hours
    const hour = context.timeOfDay;
    if (hour >= 9 && hour <= 17) { // Business hours
      probability *= 0.9;
    }

    return Math.max(0, Math.min(1, probability));
  }

  /**
   * Apply contextual factors to delay calculation
   */
  private applyContextualFactors(baseDelay: number, context: RetryContext, strategy: RetryStrategy): number {
    let delay = baseDelay;

    // Increase delay during high system load
    if (context.systemLoad === 'high') {
      delay *= 2;
    } else if (context.systemLoad === 'medium') {
      delay *= 1.5;
    }

    // Increase delay during peak hours
    const hour = context.timeOfDay;
    if (hour >= 9 && hour <= 17) {
      delay *= 1.3;
    }

    // Decrease delay for high-priority index documents
    if (context.documentType === 'index') {
      delay *= 0.8;
    }

    return delay;
  }

  /**
   * Apply jitter to delay to avoid thundering herd
   */
  private applyJitter(delay: number, jitterPercent: number): number {
    const jitter = delay * (jitterPercent / 100);
    const randomJitter = (Math.random() - 0.5) * 2 * jitter;
    return Math.max(100, delay + randomJitter); // Minimum 100ms delay
  }

  /**
   * Check if operation has retry budget remaining
   */
  private checkRetryBudget(operation: string): boolean {
    const budget = this.budgets.get(operation);
    if (!budget) {
      return true; // No budget limit set
    }

    // Reset budget if time window has passed
    if (new Date() > budget.resetTime) {
      budget.currentAttempts = 0;
      budget.resetTime = new Date(Date.now() + 60 * 60 * 1000); // Next hour
    }

    return budget.currentAttempts < budget.maxAttemptsPerHour;
  }

  /**
   * Update retry budget usage
   */
  private updateRetryBudget(operation: string): void {
    let budget = this.budgets.get(operation);
    if (!budget) {
      budget = {
        operation,
        maxAttemptsPerHour: 20, // Default budget
        currentAttempts: 0,
        resetTime: new Date(Date.now() + 60 * 60 * 1000)
      };
      this.budgets.set(operation, budget);
    }

    budget.currentAttempts++;
  }

  /**
   * Generate pattern key for learning
   */
  private getPatternKey(context: RetryContext): string {
    return `${context.errorType}:${context.documentType}:${context.systemLoad}`;
  }

  /**
   * Initialize default retry strategies
   */
  private initializeDefaultStrategies(): void {
    this.strategies.set('conservative', {
      name: 'conservative',
      maxAttempts: 3,
      baseDelay: 2000,
      maxDelay: 30000,
      backoffMultiplier: 2,
      jitterPercent: 20,
      contextualFactors: ['system_load', 'time_of_day']
    });

    this.strategies.set('aggressive', {
      name: 'aggressive',
      maxAttempts: 5,
      baseDelay: 1000,
      maxDelay: 15000,
      backoffMultiplier: 1.5,
      jitterPercent: 15,
      contextualFactors: ['document_priority']
    });

    this.strategies.set('immediate', {
      name: 'immediate',
      maxAttempts: 2,
      baseDelay: 500,
      maxDelay: 2000,
      backoffMultiplier: 1.2,
      jitterPercent: 10,
      contextualFactors: []
    });

    this.strategies.set('patient', {
      name: 'patient',
      maxAttempts: 3,
      baseDelay: 5000,
      maxDelay: 60000,
      backoffMultiplier: 3,
      jitterPercent: 25,
      contextualFactors: ['rate_limit_window']
    });

    this.strategies.set('adaptive', {
      name: 'adaptive',
      maxAttempts: 4,
      baseDelay: 1500,
      maxDelay: 20000,
      backoffMultiplier: 1.8,
      jitterPercent: 20,
      contextualFactors: ['system_load', 'time_of_day', 'document_priority']
    });
  }

  /**
   * Setup maintenance windows (typically during low usage hours)
   */
  private setupMaintenanceWindows(): void {
    // Example: 2 AM to 4 AM
    this.maintenanceWindows.push({ start: 2, end: 4 });
  }

  /**
   * Check if current time is within maintenance window
   */
  private isMaintenanceWindow(): boolean {
    const hour = new Date().getHours();
    return this.maintenanceWindows.some(window => 
      hour >= window.start && hour < window.end
    );
  }

  /**
   * Start timer to reset retry budgets hourly
   */
  private startBudgetResetTimer(): void {
    setInterval(() => {
      const now = new Date();
      for (const budget of this.budgets.values()) {
        if (now > budget.resetTime) {
          budget.currentAttempts = 0;
          budget.resetTime = new Date(now.getTime() + 60 * 60 * 1000);
        }
      }
    }, 5 * 60 * 1000); // Check every 5 minutes
  }

  /**
   * Load retry patterns from storage (mock implementation)
   */
  private async loadRetryPatterns(): Promise<void> {
    // In a real implementation, this would load from database
    console.log('Loading retry patterns from storage...');
    // For now, we'll start with empty patterns and learn over time
  }

  /**
   * Save retry patterns to storage (mock implementation)
   */
  private async saveRetryPatterns(): Promise<void> {
    // In a real implementation, this would save to database
    console.log(`Saving ${this.patterns.size} retry patterns to storage...`);
  }
}
