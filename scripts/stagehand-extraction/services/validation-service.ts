import { BaseService } from './base-service';
import { ExtractionQueueItem } from '../config';
import { SupabaseService } from '../supabase-client';
import * as fs from 'fs/promises';
import * as path from 'path';

export interface ValidationConfig {
  name: string;
  minFileSizeBytes: number;
  maxFileSizeBytes: number;
  allowedFileTypes: string[];
  validationTimeout: number;
  quarantineDir?: string;
}

export interface ValidationResult {
  isValid: boolean;
  issues: ValidationIssue[];
  fileSize?: number;
  fileType?: string;
  confidence: number;
}

export interface ValidationIssue {
  type: 'file_size' | 'file_type' | 'file_content' | 'naming' | 'corruption';
  severity: 'low' | 'medium' | 'high' | 'critical';
  message: string;
  suggestedAction?: string;
}

export interface DocumentValidationRules {
  enforceNaming: boolean;
  namingPattern: RegExp;
  contentValidation: boolean;
  duplicateDetection: boolean;
  metadataExtraction: boolean;
}

export class ValidationService extends BaseService {
  private supabaseService: SupabaseService;
  private validationConfig: ValidationConfig;
  private validationRules: DocumentValidationRules;
  private validationStats: Map<string, number> = new Map();

  constructor(config: ValidationConfig, supabaseService: SupabaseService) {
    super(config);
    this.validationConfig = config;
    this.supabaseService = supabaseService;
    
    // Default validation rules
    this.validationRules = {
      enforceNaming: true,
      namingPattern: /^[A-Za-z0-9_-]+\.pdf$/,
      contentValidation: true,
      duplicateDetection: true,
      metadataExtraction: true
    };
  }

  async initialize(): Promise<void> {
    console.log('Initializing Validation Service...');
    
    // Ensure quarantine directory exists if specified
    if (this.validationConfig.quarantineDir) {
      await this.ensureDirectory(this.validationConfig.quarantineDir);
    }
    
    // Setup validation monitoring
    this.setupValidationMonitoring();
    
    console.log('Validation Service initialized successfully');
  }

  async shutdown(): Promise<void> {
    console.log('Shutting down Validation Service...');
    // Clean up any resources
    console.log('Validation Service shutdown complete');
  }

  async healthCheck(): Promise<boolean> {
    try {
      // Check if we can access file system
      if (this.validationConfig.quarantineDir) {
        await this.ensureDirectory(this.validationConfig.quarantineDir);
      }
      
      // Check validation performance
      const errorRate = this.getValidationErrorRate();
      return errorRate < 0.1; // Unhealthy if more than 10% validation errors
    } catch (error) {
      return false;
    }
  }

  /**
   * Validate a downloaded document
   */
  async validateDocument(
    filePath: string,
    item: ExtractionQueueItem
  ): Promise<ValidationResult> {
    const validationId = `${item.id}_${Date.now()}`;
    
    try {
      console.log(`Starting validation for document: ${filePath}`);
      this.emit('validation:started', { 
        itemId: item.id, 
        filePath,
        validationId 
      });

      const issues: ValidationIssue[] = [];
      let confidence = 1.0;

      // File existence check
      const fileExists = await this.fileExists(filePath);
      if (!fileExists) {
        issues.push({
          type: 'file_content',
          severity: 'critical',
          message: 'File does not exist',
          suggestedAction: 'Retry download'
        });
        return { isValid: false, issues, confidence: 0 };
      }

      // File size validation
      const fileSize = await this.getFileSize(filePath);
      const sizeValidation = this.validateFileSize(fileSize);
      if (!sizeValidation.isValid) {
        issues.push(...sizeValidation.issues);
        confidence -= 0.3;
      }

      // File type validation
      const fileType = this.getFileType(filePath);
      const typeValidation = this.validateFileType(fileType);
      if (!typeValidation.isValid) {
        issues.push(...typeValidation.issues);
        confidence -= 0.2;
      }

      // Naming validation
      if (this.validationRules.enforceNaming) {
        const namingValidation = this.validateNaming(filePath, item.document_number);
        if (!namingValidation.isValid) {
          issues.push(...namingValidation.issues);
          confidence -= 0.1;
        }
      }

      // Content validation (PDF-specific)
      if (this.validationRules.contentValidation && fileType === 'pdf') {
        const contentValidation = await this.validatePdfContent(filePath);
        if (!contentValidation.isValid) {
          issues.push(...contentValidation.issues);
          confidence -= 0.3;
        }
      }

      // Duplicate detection
      if (this.validationRules.duplicateDetection) {
        const duplicateValidation = await this.checkForDuplicates(filePath, item);
        if (!duplicateValidation.isValid) {
          issues.push(...duplicateValidation.issues);
          confidence -= 0.1;
        }
      }

      const isValid = issues.filter(issue => issue.severity === 'critical').length === 0;
      
      // Track validation stats
      this.updateValidationStats(isValid ? 'success' : 'failure');

      // Quarantine invalid files if configured
      if (!isValid && this.validationConfig.quarantineDir) {
        await this.quarantineFile(filePath, issues);
      }

      this.emit('validation:completed', {
        itemId: item.id,
        isValid,
        issueCount: issues.length,
        confidence,
        fileSize,
        fileType
      });

      return {
        isValid,
        issues,
        fileSize,
        fileType,
        confidence: Math.max(0, confidence)
      };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      
      this.emit('validation:failed', {
        itemId: item.id,
        error: errorMessage,
        validationId
      });

      return {
        isValid: false,
        issues: [{
          type: 'file_content',
          severity: 'critical',
          message: `Validation error: ${errorMessage}`,
          suggestedAction: 'Manual review required'
        }],
        confidence: 0
      };
    }
  }

  /**
   * Auto-correct document issues when possible
   */
  async autoCorrectDocument(
    filePath: string,
    issues: ValidationIssue[],
    item: ExtractionQueueItem
  ): Promise<{ corrected: boolean; newPath?: string; remainingIssues: ValidationIssue[] }> {
    const remainingIssues: ValidationIssue[] = [];
    let newPath = filePath;
    let corrected = false;

    for (const issue of issues) {
      switch (issue.type) {
        case 'naming':
          // Auto-correct naming issues
          const correctedPath = await this.correctNaming(filePath, item.document_number);
          if (correctedPath !== filePath) {
            newPath = correctedPath;
            corrected = true;
            this.emit('validation:auto_corrected', {
              itemId: item.id,
              issue: 'naming',
              oldPath: filePath,
              newPath: correctedPath
            });
          } else {
            remainingIssues.push(issue);
          }
          break;

        default:
          // Cannot auto-correct this issue
          remainingIssues.push(issue);
          break;
      }
    }

    return {
      corrected,
      newPath: corrected ? newPath : undefined,
      remainingIssues
    };
  }

  /**
   * Get validation statistics
   */
  getValidationStats(): {
    totalValidations: number;
    successRate: number;
    commonIssues: Record<string, number>;
    averageConfidence: number;
  } {
    const total = (this.validationStats.get('success') || 0) + (this.validationStats.get('failure') || 0);
    const successRate = total > 0 ? (this.validationStats.get('success') || 0) / total : 1;

    return {
      totalValidations: total,
      successRate,
      commonIssues: {
        file_size: this.validationStats.get('file_size') || 0,
        file_type: this.validationStats.get('file_type') || 0,
        naming: this.validationStats.get('naming') || 0,
        content: this.validationStats.get('content') || 0
      },
      averageConfidence: 0.85 // This would be calculated from actual data
    };
  }

  /**
   * Validate file size
   */
  private validateFileSize(fileSize: number): { isValid: boolean; issues: ValidationIssue[] } {
    const issues: ValidationIssue[] = [];

    if (fileSize < this.validationConfig.minFileSizeBytes) {
      issues.push({
        type: 'file_size',
        severity: 'high',
        message: `File too small: ${fileSize} bytes (minimum: ${this.validationConfig.minFileSizeBytes})`,
        suggestedAction: 'Check if file downloaded completely'
      });
    }

    if (fileSize > this.validationConfig.maxFileSizeBytes) {
      issues.push({
        type: 'file_size',
        severity: 'medium',
        message: `File very large: ${fileSize} bytes (maximum: ${this.validationConfig.maxFileSizeBytes})`,
        suggestedAction: 'Verify file content'
      });
    }

    return { isValid: issues.length === 0, issues };
  }

  /**
   * Validate file type
   */
  private validateFileType(fileType: string): { isValid: boolean; issues: ValidationIssue[] } {
    const issues: ValidationIssue[] = [];

    if (!this.validationConfig.allowedFileTypes.includes(fileType)) {
      issues.push({
        type: 'file_type',
        severity: 'high',
        message: `Invalid file type: ${fileType} (allowed: ${this.validationConfig.allowedFileTypes.join(', ')})`,
        suggestedAction: 'Convert to allowed format or verify download'
      });
    }

    return { isValid: issues.length === 0, issues };
  }

  /**
   * Validate naming convention
   */
  private validateNaming(filePath: string, expectedNumber: string): { isValid: boolean; issues: ValidationIssue[] } {
    const issues: ValidationIssue[] = [];
    const fileName = path.basename(filePath);
    const expectedName = `${expectedNumber}.pdf`;

    if (fileName !== expectedName) {
      issues.push({
        type: 'naming',
        severity: 'medium',
        message: `Incorrect naming: '${fileName}' (expected: '${expectedName}')`,
        suggestedAction: 'Rename file to match document number'
      });
    }

    if (!this.validationRules.namingPattern.test(fileName)) {
      issues.push({
        type: 'naming',
        severity: 'low',
        message: `File name doesn't match pattern: ${this.validationRules.namingPattern}`,
        suggestedAction: 'Use only alphanumeric characters, hyphens, and underscores'
      });
    }

    return { isValid: issues.length === 0, issues };
  }

  /**
   * Validate PDF content
   */
  private async validatePdfContent(filePath: string): Promise<{ isValid: boolean; issues: ValidationIssue[] }> {
    const issues: ValidationIssue[] = [];

    try {
      // Read first few bytes to check PDF header
      const fileHandle = await fs.open(filePath, 'r');
      const buffer = Buffer.alloc(8);
      await fileHandle.read(buffer, 0, 8, 0);
      await fileHandle.close();

      const header = buffer.toString('ascii', 0, 4);
      if (header !== '%PDF') {
        issues.push({
          type: 'file_content',
          severity: 'critical',
          message: 'File is not a valid PDF (missing PDF header)',
          suggestedAction: 'Re-download document'
        });
      }

      // Check for minimum PDF structure
      const fileContent = await fs.readFile(filePath, 'ascii');
      if (!fileContent.includes('%%EOF')) {
        issues.push({
          type: 'file_content',
          severity: 'high',
          message: 'PDF appears to be incomplete (missing EOF marker)',
          suggestedAction: 'Re-download document'
        });
      }

    } catch (error) {
      issues.push({
        type: 'file_content',
        severity: 'high',
        message: `Error reading PDF content: ${error instanceof Error ? error.message : String(error)}`,
        suggestedAction: 'Manual review required'
      });
    }

    return { isValid: issues.length === 0, issues };
  }

  /**
   * Check for duplicate files
   */
  private async checkForDuplicates(filePath: string, item: ExtractionQueueItem): Promise<{ isValid: boolean; issues: ValidationIssue[] }> {
    const issues: ValidationIssue[] = [];

    try {
      // This would implement actual duplicate detection logic
      // For now, we'll do a simple check based on file size and name
      const fileSize = await this.getFileSize(filePath);
      const fileName = path.basename(filePath);

      // In a real implementation, this would check against a database of known files
      // or use file hashing for content comparison
      
    } catch (error) {
      // Non-critical error for duplicate detection
    }

    return { isValid: true, issues };
  }

  /**
   * Correct naming issues
   */
  private async correctNaming(filePath: string, documentNumber: string): Promise<string> {
    try {
      const directory = path.dirname(filePath);
      const correctName = `${documentNumber}.pdf`;
      const newPath = path.join(directory, correctName);

      if (filePath !== newPath) {
        await fs.rename(filePath, newPath);
        return newPath;
      }
    } catch (error) {
      console.error('Failed to correct naming:', error);
    }

    return filePath;
  }

  /**
   * Quarantine invalid files
   */
  private async quarantineFile(filePath: string, issues: ValidationIssue[]): Promise<void> {
    if (!this.validationConfig.quarantineDir) return;

    try {
      const fileName = path.basename(filePath);
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const quarantineName = `${timestamp}_${fileName}`;
      const quarantinePath = path.join(this.validationConfig.quarantineDir, quarantineName);

      await fs.copyFile(filePath, quarantinePath);

      // Create issue report
      const reportPath = quarantinePath.replace('.pdf', '_issues.json');
      await fs.writeFile(reportPath, JSON.stringify({
        originalPath: filePath,
        quarantineTime: new Date().toISOString(),
        issues
      }, null, 2));

      this.emit('validation:quarantined', {
        originalPath: filePath,
        quarantinePath,
        issueCount: issues.length
      });

    } catch (error) {
      console.error('Failed to quarantine file:', error);
    }
  }

  /**
   * Utility methods
   */
  private async fileExists(filePath: string): Promise<boolean> {
    try {
      await fs.access(filePath);
      return true;
    } catch {
      return false;
    }
  }

  private async getFileSize(filePath: string): Promise<number> {
    try {
      const stats = await fs.stat(filePath);
      return stats.size;
    } catch {
      return 0;
    }
  }

  private getFileType(filePath: string): string {
    const ext = path.extname(filePath).toLowerCase();
    return ext.startsWith('.') ? ext.slice(1) : ext;
  }

  private async ensureDirectory(dirPath: string): Promise<void> {
    try {
      await fs.mkdir(dirPath, { recursive: true });
    } catch (error) {
      if ((error as any).code !== 'EEXIST') {
        throw error;
      }
    }
  }

  private updateValidationStats(type: string): void {
    const current = this.validationStats.get(type) || 0;
    this.validationStats.set(type, current + 1);
  }

  private getValidationErrorRate(): number {
    const total = (this.validationStats.get('success') || 0) + (this.validationStats.get('failure') || 0);
    if (total === 0) return 0;
    return (this.validationStats.get('failure') || 0) / total;
  }

  private setupValidationMonitoring(): void {
    setInterval(() => {
      const stats = this.getValidationStats();
      this.emit('validation:stats', stats);
    }, 60000); // Every minute
  }
}
