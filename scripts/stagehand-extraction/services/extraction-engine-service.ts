import { BaseService } from './base-service';
import { ExtractionQueueItem } from '../config';
import { StagehandPage } from '../types';
import { FileManager } from '../utils/file-manager';
import { z } from 'zod';

export interface ExtractionEngineConfig {
  name: string;
  maxRetryAttempts: number;
  retryDelay: number;
  downloadTimeout: number;
  baseDownloadDir: string;
}

export interface DownloadResult {
  success: boolean;
  filePath?: string;
  error?: string;
  fileSize?: number;
  downloadTime?: number;
}

export class ExtractionEngineService extends BaseService {
  private fileManager: FileManager;
  private engineConfig: ExtractionEngineConfig;
  private activeDownloads: Map<string, { startTime: Date; itemId: string }> = new Map();

  constructor(config: ExtractionEngineConfig) {
    super(config);
    this.engineConfig = config;
    this.fileManager = new FileManager();
  }

  async initialize(): Promise<void> {
    console.log('Initializing Extraction Engine Service...');
    
    // Ensure download directories exist
    await this.fileManager.ensureDownloadDirectory();
    await this.fileManager.ensureDownloadDirectory('screenshots');
    
    // Setup download monitoring
    this.setupDownloadMonitoring();
    
    console.log('Extraction Engine Service initialized successfully');
  }

  async shutdown(): Promise<void> {
    console.log('Shutting down Extraction Engine Service...');
    
    // Cancel any active downloads
    this.activeDownloads.clear();
    
    console.log('Extraction Engine Service shutdown complete');
  }

  async healthCheck(): Promise<boolean> {
    try {
      // Check if file system is accessible
      await this.fileManager.ensureDownloadDirectory();
      
      // Check if we have too many stuck downloads
      const stuckDownloads = Array.from(this.activeDownloads.values())
        .filter(download => Date.now() - download.startTime.getTime() > this.engineConfig.downloadTimeout * 2);
      
      return stuckDownloads.length < 3; // Unhealthy if more than 3 stuck downloads
    } catch (error) {
      return false;
    }
  }

  /**
   * Download a document from the current page state
   */
  async downloadDocument(
    page: StagehandPage,
    item: ExtractionQueueItem
  ): Promise<DownloadResult> {
    const startTime = Date.now();
    const downloadId = `${item.id}_${startTime}`;
    
    this.activeDownloads.set(downloadId, {
      startTime: new Date(),
      itemId: item.id
    });

    try {
      console.log(`Starting document download for ${item.document_number}`);
      this.emit('download:started', { 
        itemId: item.id, 
        documentNumber: item.document_number,
        downloadId 
      });

      // Ensure we have a proper download directory for this item
      const itemDownloadDir = await this.fileManager.ensureDownloadDirectory(item.id);

      // Check if we need to select a radio button first
      const needsSelection = await this.checkForRadioButtonSelection(page);
      if (needsSelection) {
        await this.handleResultSelection(page);
      }

      // Find and click the print button
      await this.triggerDownload(page);

      // Wait for download to complete
      const downloadResult = await this.waitForDownloadCompletion(
        itemDownloadDir,
        item.document_number,
        this.engineConfig.downloadTimeout
      );

      if (downloadResult.success) {
        this.emit('download:completed', {
          itemId: item.id,
          filePath: downloadResult.filePath,
          fileSize: downloadResult.fileSize,
          downloadTime: Date.now() - startTime
        });

        return {
          ...downloadResult,
          downloadTime: Date.now() - startTime
        };
      } else {
        throw new Error(downloadResult.error || 'Download failed');
      }

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      
      this.emit('download:failed', {
        itemId: item.id,
        error: errorMessage,
        downloadTime: Date.now() - startTime
      });

      return {
        success: false,
        error: errorMessage,
        downloadTime: Date.now() - startTime
      };
    } finally {
      this.activeDownloads.delete(downloadId);
    }
  }

  /**
   * Navigate to login page and authenticate
   */
  async performLogin(page: StagehandPage, userCode: string, password: string): Promise<void> {
    try {
      console.log('Performing login to Registre Foncier...');
      
      // Navigate to the main page first
      await page.goto('https://www.registrefoncier.gouv.qc.ca/Sirf/');
      await page.waitForTimeout(1000);
      
      // Click on the first 'Entrée du site' link
      await page.act("click on the link with href '/Sirf/pf_acces.asp'");
      await page.waitForTimeout(2000);
      
      // Fill the login form
      await page.act(`type '${userCode}' in the 'Code d'utilisateur' input field`);
      await page.act(`type '${password}' in the 'Mot de passe' input field`);
      await page.act("click the 'Soumettre' button");
      
      // Wait for login to complete
      await page.waitForTimeout(3000);
      
      this.emit('login:completed', { timestamp: new Date() });
      console.log('Login completed successfully');
    } catch (error) {
      this.emit('login:failed', { error: error instanceof Error ? error.message : String(error) });
      throw error;
    }
  }

  /**
   * Navigate to appropriate search page
   */
  async navigateToSearchPage(
    page: StagehandPage,
    documentType: 'acte' | 'index'
  ): Promise<void> {
    const url = documentType === 'acte'
      ? 'https://www.registrefoncier.gouv.qc.ca/Sirf/Script/13_01_11/pf_13_01_11_08_reqst.asp'
      : 'https://www.registrefoncier.gouv.qc.ca/Sirf/Script/13_01_11/pf_13_01_11_02_indx_immbl.asp';
    
    console.log(`Navigating to ${documentType} search page...`);
    await page.goto(url);
    await page.waitForTimeout(2000);
    
    this.emit('navigation:completed', { documentType, url });
  }

  /**
   * Take a screenshot for debugging purposes
   */
  async takeDebugScreenshot(page: StagehandPage, context: string, itemId?: string): Promise<string> {
    try {
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const filename = `debug_${context}_${timestamp}${itemId ? `_${itemId}` : ''}.png`;
      const screenshotDir = await this.fileManager.ensureDownloadDirectory('screenshots');
      const filePath = `${screenshotDir}/${filename}`;
      
      // Note: This would need to be implemented based on the actual page API
      // await page.screenshot({ path: filePath });
      
      this.emit('screenshot:taken', { context, filePath, itemId });
      return filePath;
    } catch (error) {
      console.error('Failed to take debug screenshot:', error);
      return '';
    }
  }

  /**
   * Get download statistics
   */
  getDownloadStats(): {
    activeDownloads: number;
    averageDownloadTime: number;
    successRate: number;
    totalDownloads: number;
  } {
    // This would be implemented with actual tracking
    return {
      activeDownloads: this.activeDownloads.size,
      averageDownloadTime: 15000, // ms
      successRate: 0.85,
      totalDownloads: 0
    };
  }

  /**
   * Check if we need to select a radio button before downloading
   */
  private async checkForRadioButtonSelection(page: StagehandPage): Promise<boolean> {
    try {
      const resultsCheck = await page.extract({
        instruction: "Check if there are radio buttons (rdRegst) that need to be selected, or if there's already an 'Imprimer' button available",
        schema: z.object({
          hasRadioButtons: z.boolean(),
          hasPrintButton: z.boolean(),
          radioButtons: z.array(z.string()).optional()
        })
      });

      return resultsCheck.hasRadioButtons && !resultsCheck.hasPrintButton;
    } catch (error) {
      return false;
    }
  }

  /**
   * Handle result selection when multiple results are found
   */
  private async handleResultSelection(page: StagehandPage): Promise<void> {
    console.log('Found search results with radio buttons. Selecting first option...');
    
    // Select the first radio button (usually the most relevant)
    await page.act("click on the first radio button in the search results");
    
    // Submit to go to the document
    await page.act("click the 'Soumettre' button to view the selected document");
    
    // Wait for the document page to load
    await page.waitForTimeout(3000);
  }

  /**
   * Trigger the document download with enhanced frame handling
   */
  private async triggerDownload(page: StagehandPage): Promise<void> {
    console.log('Looking for print button to trigger download...');
    
    // Wait for frames to load completely
    await page.waitForTimeout(3000);
    
    // Strategy 1: Use the exact path from working playwright script
    try {
      console.log('🔧 Engine attempt 1: Exact frame navigation...');
      await page.act("navigate to the frame named 'page', then to its nested frame named 'frmNavgt', and click the 'Imprimer' link");
      console.log('✅ Successfully triggered download using exact frame navigation');
      return;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.log('❌ Engine attempt 1 failed:', errorMessage);
    }

    // Strategy 2: Alternative frame navigation approach
    try {
      console.log('🔧 Engine attempt 2: Alternative frame navigation...');
      await page.act("in the frame 'page', then in its child frame 'frmNavgt', click the link with text 'Imprimer'");
      console.log('✅ Successfully triggered download using alternative frame navigation');
      return;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.log('❌ Engine attempt 2 failed:', errorMessage);
    }

    // Strategy 3: Frame detection and search
    try {
      console.log('🔧 Engine attempt 3: Frame detection...');
      await page.act("find any frame containing navigation controls, then look for and click the 'Imprimer' button within that frame");
      console.log('✅ Successfully triggered download using frame detection');
      return;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.log('❌ Engine attempt 3 failed:', errorMessage);
    }

    // Strategy 4: Comprehensive frame search
    try {
      console.log('🔧 Engine attempt 4: Comprehensive frame search...');
      await page.act("search all frames on the page for any element with text 'Imprimer' and click it");
      console.log('✅ Successfully triggered download using comprehensive search');
      return;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.log('❌ Engine attempt 4 failed:', errorMessage);
    }

    // Strategy 5: Fallback to any print-related element
    try {
      console.log('🔧 Engine attempt 5: Fallback print search...');
      await page.act("look for any clickable element anywhere on the page with text containing 'Imprimer', 'Print', or print symbols and click it");
      console.log('✅ Successfully triggered download using fallback search');
      return;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.log('❌ Engine attempt 5 failed:', errorMessage);
    }

    // If all strategies fail, throw descriptive error
    const errorMessage = `Failed to trigger download after 5 attempts. Could not locate 'Imprimer' button in expected nested frames (page -> frmNavgt) or anywhere else on the page.`;
    console.error('❌ All download trigger strategies failed');
    throw new Error(errorMessage);
  }

  /**
   * Wait for download to complete and handle file management
   */
  private async waitForDownloadCompletion(
    downloadDir: string,
    documentNumber: string,
    timeout: number
  ): Promise<DownloadResult> {
    console.log('Waiting for download to complete...');
    
    try {
      // Wait for download to complete
      const downloadedFile = await this.fileManager.waitForDownloadComplete(downloadDir, timeout);
      
      if (!downloadedFile) {
        return {
          success: false,
          error: 'Download timeout - no file was downloaded'
        };
      }

      // Rename the file to proper format
      const targetName = `${documentNumber}.pdf`;
      const finalPath = await this.fileManager.renameFileWithDuplicateHandling(
        downloadDir,
        downloadedFile,
        targetName
      );

      // Verify file content
      const isValid = await this.fileManager.verifyFileContent(finalPath);
      if (!isValid) {
        return {
          success: false,
          error: 'Downloaded file is empty or too small'
        };
      }

      // Get file size for metrics
      const fileSize = await this.getFileSize(finalPath);

      console.log(`Document downloaded successfully: ${finalPath}`);
      
      return {
        success: true,
        filePath: finalPath,
        fileSize
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  /**
   * Get file size
   */
  private async getFileSize(filePath: string): Promise<number> {
    try {
      const fs = await import('fs/promises');
      const stats = await fs.stat(filePath);
      return stats.size;
    } catch (error) {
      return 0;
    }
  }

  /**
   * Setup download monitoring
   */
  private setupDownloadMonitoring(): void {
    // Monitor for stuck downloads
    setInterval(() => {
      const now = Date.now();
      const stuckDownloads = Array.from(this.activeDownloads.entries())
        .filter(([_, download]) => now - download.startTime.getTime() > this.engineConfig.downloadTimeout * 1.5);

      if (stuckDownloads.length > 0) {
        this.emit('downloads:stuck', { 
          count: stuckDownloads.length,
          downloads: stuckDownloads.map(([id, download]) => ({
            id,
            itemId: download.itemId,
            duration: now - download.startTime.getTime()
          }))
        });

        // Clean up stuck downloads
        stuckDownloads.forEach(([id]) => {
          this.activeDownloads.delete(id);
        });
      }

      // Emit download statistics
      const stats = this.getDownloadStats();
      this.emit('downloads:stats', stats);
    }, 30000); // Every 30 seconds
  }
}
