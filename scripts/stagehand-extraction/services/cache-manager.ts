import { BaseService, ServiceConfig } from './base-service';
import { createHash } from 'crypto';
import { promises as fs } from 'fs';
import { join, dirname } from 'path';
import { SupabaseService } from '../supabase-client';

export interface CacheConfig extends ServiceConfig {
  memoryLimit: number; // Max items in memory cache
  diskCacheDir: string;
  ttl: {
    formLayouts: number; // TTL in milliseconds
    searchResults: number;
    sessionData: number;
    documentMetadata: number;
    default: number;
  };
  enableDiskCache: boolean;
  enableDatabaseCache: boolean;
  compressionEnabled: boolean;
}

export interface CacheItem<T = any> {
  key: string;
  value: T;
  createdAt: Date;
  expiresAt: Date;
  accessCount: number;
  lastAccessed: Date;
  size: number;
  compressed?: boolean;
}

export interface CacheLayer {
  get<T>(key: string): Promise<CacheItem<T> | null>;
  set<T>(key: string, value: T, ttl?: number): Promise<void>;
  delete(key: string): Promise<boolean>;
  clear(): Promise<void>;
  size(): Promise<number>;
  keys(): Promise<string[]>;
}

class MemoryCache implements CacheLayer {
  private cache = new Map<string, CacheItem>();
  private accessOrder: string[] = [];
  private maxSize: number;

  constructor(maxSize: number) {
    this.maxSize = maxSize;
  }

  async get<T>(key: string): Promise<CacheItem<T> | null> {
    const item = this.cache.get(key);
    if (!item) return null;

    // Check expiration
    if (item.expiresAt < new Date()) {
      this.cache.delete(key);
      this.removeFromAccessOrder(key);
      return null;
    }

    // Update access statistics
    item.accessCount++;
    item.lastAccessed = new Date();
    
    // Move to end of access order (most recently used)
    this.updateAccessOrder(key);

    return item as CacheItem<T>;
  }

  async set<T>(key: string, value: T, ttl: number = 300000): Promise<void> {
    const size = this.calculateSize(value);
    const item: CacheItem<T> = {
      key,
      value,
      createdAt: new Date(),
      expiresAt: new Date(Date.now() + ttl),
      accessCount: 0,
      lastAccessed: new Date(),
      size
    };

    // If cache is full, evict LRU items
    while (this.cache.size >= this.maxSize && this.accessOrder.length > 0) {
      const oldestKey = this.accessOrder.shift()!;
      this.cache.delete(oldestKey);
    }

    this.cache.set(key, item);
    this.updateAccessOrder(key);
  }

  async delete(key: string): Promise<boolean> {
    const deleted = this.cache.delete(key);
    if (deleted) {
      this.removeFromAccessOrder(key);
    }
    return deleted;
  }

  async clear(): Promise<void> {
    this.cache.clear();
    this.accessOrder = [];
  }

  async size(): Promise<number> {
    return this.cache.size;
  }

  async keys(): Promise<string[]> {
    return Array.from(this.cache.keys());
  }

  private updateAccessOrder(key: string): void {
    this.removeFromAccessOrder(key);
    this.accessOrder.push(key);
  }

  private removeFromAccessOrder(key: string): void {
    const index = this.accessOrder.indexOf(key);
    if (index > -1) {
      this.accessOrder.splice(index, 1);
    }
  }

  private calculateSize(value: any): number {
    return JSON.stringify(value).length;
  }
}

class DiskCache implements CacheLayer {
  private cacheDir: string;
  private compressionEnabled: boolean;

  constructor(cacheDir: string, compressionEnabled: boolean = false) {
    this.cacheDir = cacheDir;
    this.compressionEnabled = compressionEnabled;
  }

  async get<T>(key: string): Promise<CacheItem<T> | null> {
    try {
      const filePath = this.getFilePath(key);
      const data = await fs.readFile(filePath, 'utf8');
      const item: CacheItem<T> = JSON.parse(data);

      // Check expiration
      if (new Date(item.expiresAt) < new Date()) {
        await this.delete(key);
        return null;
      }

      // Update access statistics
      item.accessCount++;
      item.lastAccessed = new Date();
      
      // Write back updated statistics
      await fs.writeFile(filePath, JSON.stringify(item));

      return item;
    } catch (error) {
      return null;
    }
  }

  async set<T>(key: string, value: T, ttl: number = 300000): Promise<void> {
    try {
      const filePath = this.getFilePath(key);
      await this.ensureDirectoryExists(dirname(filePath));

      const item: CacheItem<T> = {
        key,
        value,
        createdAt: new Date(),
        expiresAt: new Date(Date.now() + ttl),
        accessCount: 0,
        lastAccessed: new Date(),
        size: JSON.stringify(value).length,
        compressed: this.compressionEnabled
      };

      await fs.writeFile(filePath, JSON.stringify(item));
    } catch (error) {
      console.error('Error writing to disk cache:', error);
    }
  }

  async delete(key: string): Promise<boolean> {
    try {
      const filePath = this.getFilePath(key);
      await fs.unlink(filePath);
      return true;
    } catch (error) {
      return false;
    }
  }

  async clear(): Promise<void> {
    try {
      const files = await fs.readdir(this.cacheDir);
      await Promise.all(
        files
          .filter(file => file.endsWith('.cache'))
          .map(file => fs.unlink(join(this.cacheDir, file)))
      );
    } catch (error) {
      console.error('Error clearing disk cache:', error);
    }
  }

  async size(): Promise<number> {
    try {
      const files = await fs.readdir(this.cacheDir);
      return files.filter(file => file.endsWith('.cache')).length;
    } catch (error) {
      return 0;
    }
  }

  async keys(): Promise<string[]> {
    try {
      const files = await fs.readdir(this.cacheDir);
      return files
        .filter(file => file.endsWith('.cache'))
        .map(file => file.replace('.cache', ''));
    } catch (error) {
      return [];
    }
  }

  private getFilePath(key: string): string {
    const hashedKey = createHash('md5').update(key).digest('hex');
    return join(this.cacheDir, `${hashedKey}.cache`);
  }

  private async ensureDirectoryExists(dir: string): Promise<void> {
    try {
      await fs.mkdir(dir, { recursive: true });
    } catch (error) {
      // Directory might already exist
    }
  }
}

class DatabaseCache implements CacheLayer {
  private supabaseService: SupabaseService;

  constructor(supabaseService: SupabaseService) {
    this.supabaseService = supabaseService;
  }

  async get<T>(key: string): Promise<CacheItem<T> | null> {
    // For now, disable database cache until we create the cache_items table
    return null;
  }

  async set<T>(key: string, value: T, ttl: number = 300000): Promise<void> {
    // For now, disable database cache until we create the cache_items table
  }

  async delete(key: string): Promise<boolean> {
    // For now, disable database cache until we create the cache_items table
    return false;
  }

  async clear(): Promise<void> {
    // For now, disable database cache until we create the cache_items table
  }

  async size(): Promise<number> {
    // For now, disable database cache until we create the cache_items table
    return 0;
  }

  async keys(): Promise<string[]> {
    // For now, disable database cache until we create the cache_items table
    return [];
  }
}

export class CacheManager extends BaseService {
  private cacheConfig: CacheConfig;
  private memoryCache: MemoryCache;
  private diskCache?: DiskCache;
  private databaseCache?: DatabaseCache;
  private supabaseService?: SupabaseService;
  private cleanupTimer?: NodeJS.Timeout;

  constructor(config: CacheConfig) {
    super(config);
    this.cacheConfig = config;
    this.memoryCache = new MemoryCache(config.memoryLimit);
  }

  async initialize(): Promise<void> {
    console.log('Initializing cache manager', { config: this.cacheConfig });

    // Initialize disk cache if enabled
    if (this.cacheConfig.enableDiskCache) {
      this.diskCache = new DiskCache(
        this.cacheConfig.diskCacheDir, 
        this.cacheConfig.compressionEnabled
      );
    }

    // Initialize database cache if enabled
    if (this.cacheConfig.enableDatabaseCache) {
      this.supabaseService = new SupabaseService();
      this.databaseCache = new DatabaseCache(this.supabaseService);
    }

    // Start cleanup timer
    this.startCleanupTimer();

    console.log('Cache manager initialized');
  }

  async shutdown(): Promise<void> {
    console.log('Shutting down cache manager');

    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
    }

    // Clear memory cache
    await this.memoryCache.clear();

    console.log('Cache manager shutdown complete');
  }

  async healthCheck(): Promise<boolean> {
    try {
      // Test memory cache
      const testKey = 'health_check_' + Date.now();
      await this.memoryCache.set(testKey, 'test', 1000);
      const result = await this.memoryCache.get(testKey);
      await this.memoryCache.delete(testKey);
      
      return result !== null;
    } catch (error) {
      this.handleError(error, 'health_check');
      return false;
    }
  }

  // High-level cache operations
  async get<T>(key: string, category: string = 'default'): Promise<T | null> {
    const fullKey = this.generateKey(key, category);

    // Try memory cache first
    let item = await this.memoryCache.get<T>(fullKey);
    if (item) {
      return item.value;
    }

    // Try disk cache second
    if (this.diskCache) {
      item = await this.diskCache.get<T>(fullKey);
      if (item) {
        // Promote to memory cache
        await this.memoryCache.set(fullKey, item.value, this.getRemainingTTL(item));
        return item.value;
      }
    }

    // Try database cache last
    if (this.databaseCache) {
      item = await this.databaseCache.get<T>(fullKey);
      if (item) {
        // Promote to higher level caches
        const remainingTTL = this.getRemainingTTL(item);
        await this.memoryCache.set(fullKey, item.value, remainingTTL);
        if (this.diskCache) {
          await this.diskCache.set(fullKey, item.value, remainingTTL);
        }
        return item.value;
      }
    }

    return null;
  }

  async set<T>(key: string, value: T, category: string = 'default', customTTL?: number): Promise<void> {
    const fullKey = this.generateKey(key, category);
    const ttl = customTTL || this.getTTLForCategory(category);

    // Set in all enabled cache layers
    await this.memoryCache.set(fullKey, value, ttl);

    if (this.diskCache) {
      await this.diskCache.set(fullKey, value, ttl);
    }

    if (this.databaseCache) {
      await this.databaseCache.set(fullKey, value, ttl);
    }
  }

  async delete(key: string, category: string = 'default'): Promise<boolean> {
    const fullKey = this.generateKey(key, category);
    
    let deleted = false;
    
    // Delete from all cache layers
    if (await this.memoryCache.delete(fullKey)) {
      deleted = true;
    }

    if (this.diskCache && await this.diskCache.delete(fullKey)) {
      deleted = true;
    }

    if (this.databaseCache && await this.databaseCache.delete(fullKey)) {
      deleted = true;
    }

    return deleted;
  }

  async clear(category?: string): Promise<void> {
    if (category) {
      // Clear specific category
      const keys = await this.memoryCache.keys();
      const categoryKeys = keys.filter(key => key.startsWith(`${category}:`));
      
      for (const key of categoryKeys) {
        await this.memoryCache.delete(key);
        if (this.diskCache) {
          await this.diskCache.delete(key);
        }
        if (this.databaseCache) {
          await this.databaseCache.delete(key);
        }
      }
    } else {
      // Clear all caches
      await this.memoryCache.clear();
      if (this.diskCache) {
        await this.diskCache.clear();
      }
      if (this.databaseCache) {
        await this.databaseCache.clear();
      }
    }
  }

  // Specialized cache methods
  async cacheFormLayout(pageUrl: string, formData: any): Promise<void> {
    const key = this.hashUrl(pageUrl);
    await this.set(key, formData, 'formLayouts');
  }

  async getFormLayout(pageUrl: string): Promise<any | null> {
    const key = this.hashUrl(pageUrl);
    return await this.get(key, 'formLayouts');
  }

  async cacheSearchResult(searchParams: any, result: any): Promise<void> {
    const key = this.hashObject(searchParams);
    await this.set(key, result, 'searchResults');
  }

  async getSearchResult(searchParams: any): Promise<any | null> {
    const key = this.hashObject(searchParams);
    return await this.get(key, 'searchResults');
  }

  async cacheSessionData(sessionId: string, data: any): Promise<void> {
    await this.set(sessionId, data, 'sessionData');
  }

  async getSessionData(sessionId: string): Promise<any | null> {
    return await this.get(sessionId, 'sessionData');
  }

  async cacheDocumentMetadata(documentNumber: string, metadata: any): Promise<void> {
    await this.set(documentNumber, metadata, 'documentMetadata');
  }

  async getDocumentMetadata(documentNumber: string): Promise<any | null> {
    return await this.get(documentNumber, 'documentMetadata');
  }

  // Cache warming and optimization
  async warmCache(warmingPlan: { category: string; keys: string[]; dataLoader: (key: string) => Promise<any> }[]): Promise<void> {
    console.log('Starting cache warming...');
    
    for (const plan of warmingPlan) {
      for (const key of plan.keys) {
        try {
          const cachedValue = await this.get(key, plan.category);
          if (!cachedValue) {
            const data = await plan.dataLoader(key);
            if (data) {
              await this.set(key, data, plan.category);
            }
          }
        } catch (error) {
          console.error(`Error warming cache for key ${key}:`, error);
        }
      }
    }
    
    console.log('Cache warming complete');
  }

  async getCacheStatistics(): Promise<{
    memory: { size: number; hitRate: number };
    disk?: { size: number };
    database?: { size: number };
    totalSize: number;
  }> {
    const memorySize = await this.memoryCache.size();
    
    const stats: any = {
      memory: { size: memorySize, hitRate: 0 }, // Hit rate calculation would need tracking
      totalSize: memorySize
    };

    if (this.diskCache) {
      const diskSize = await this.diskCache.size();
      stats.disk = { size: diskSize };
      stats.totalSize += diskSize;
    }

    if (this.databaseCache) {
      const dbSize = await this.databaseCache.size();
      stats.database = { size: dbSize };
      stats.totalSize += dbSize;
    }

    return stats;
  }

  // Private helper methods
  private generateKey(key: string, category: string): string {
    return `${category}:${key}`;
  }

  private hashUrl(url: string): string {
    return createHash('md5').update(url).digest('hex');
  }

  private hashObject(obj: any): string {
    return createHash('md5').update(JSON.stringify(obj)).digest('hex');
  }

  private getTTLForCategory(category: string): number {
    return this.cacheConfig.ttl[category as keyof typeof this.cacheConfig.ttl] || this.cacheConfig.ttl.default;
  }

  private getRemainingTTL(item: CacheItem): number {
    return Math.max(0, item.expiresAt.getTime() - Date.now());
  }

  private startCleanupTimer(): void {
    this.cleanupTimer = setInterval(async () => {
      await this.performCleanup();
    }, 300000); // Clean up every 5 minutes
  }

  private async performCleanup(): Promise<void> {
    console.log('Performing cache cleanup...');
    
    try {
      // Clean up expired items from disk cache
      if (this.diskCache) {
        const keys = await this.diskCache.keys();
        for (const key of keys) {
          const item = await this.diskCache.get(key);
          if (!item) {
            // Item was expired and automatically deleted
            continue;
          }
        }
      }

      // Clean up expired items from database cache
      if (this.databaseCache && this.supabaseService) {
        // Database cache cleanup will be implemented when cache_items table is created
      }
    } catch (error) {
      console.error('Error during cache cleanup:', error);
    }
  }
}
