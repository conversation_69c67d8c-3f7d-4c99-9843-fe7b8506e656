import { BaseService } from './base-service';
import { PriorityQueue, QueueItem, PriorityQueueConfig } from './priority-queue';
import { SupabaseService } from '../supabase-client';
import { ExtractionQueueItem } from '../config';
import * as fs from 'fs/promises';
import * as path from 'path';

export interface QueueManagerConfig {
  name: string;
  maxQueueSize: number;
  deadLetterMaxRetries: number;
  batchSize: number;
  healthCheckInterval: number;
  persistenceFile?: string;
  deadLetterFile?: string;
}

export interface QueueStats {
  mainQueue: {
    total: number;
    byDocumentType: Record<string, number>;
    averagePriority: number;
    oldestItem?: Date;
    newestItem?: Date;
  };
  deadLetterQueue: {
    total: number;
    byDocumentType: Record<string, number>;
    oldestItem?: Date;
    newestItem?: Date;
  };
  systemHealth: 'healthy' | 'degraded' | 'unhealthy';
}

export class QueueManager extends BaseService {
  private mainQueue: PriorityQueue;
  private deadLetterQueue: PriorityQueue;
  private supabaseService: SupabaseService;
  private queueConfig: QueueManagerConfig;
  private processingBatch: QueueItem[] = [];
  private lastSync: Date = new Date();

  constructor(config: QueueManagerConfig, supabaseService: SupabaseService) {
    super(config);
    this.queueConfig = config;
    this.supabaseService = supabaseService;

    // Initialize queues
    const queueConfig: PriorityQueueConfig = {
      maxSize: config.maxQueueSize,
      enablePersistence: !!config.persistenceFile,
      persistenceFile: config.persistenceFile
    };

    this.mainQueue = new PriorityQueue(queueConfig);
    this.deadLetterQueue = new PriorityQueue({
      maxSize: Math.floor(config.maxQueueSize * 0.1), // 10% of main queue size
      enablePersistence: !!config.deadLetterFile,
      persistenceFile: config.deadLetterFile
    });
  }

  async initialize(): Promise<void> {
    console.log('Initializing Queue Manager...');

    // Load persisted queue state if available
    await this.loadPersistedState();

    // Sync with Supabase to get pending items
    await this.syncWithDatabase();

    // Set up periodic sync
    this.setupPeriodicSync();

    console.log('Queue Manager initialized successfully');
  }

  async shutdown(): Promise<void> {
    console.log('Shutting down Queue Manager...');

    // Save current state
    await this.savePersistedState();

    // Return any processing items to the queue
    if (this.processingBatch.length > 0) {
      this.mainQueue.returnBatch(this.processingBatch);
      this.processingBatch = [];
    }

    console.log('Queue Manager shutdown complete');
  }

  async healthCheck(): Promise<boolean> {
    try {
      // Check if we can connect to Supabase
      await this.supabaseService.getItemsForProcessing(1);
      
      // Check queue health
      const stats = this.getQueueStats();
      
      // Queue is unhealthy if main queue is at 90% capacity
      const mainQueueCapacity = stats.mainQueue.total / this.queueConfig.maxQueueSize;
      if (mainQueueCapacity > 0.9) {
        return false;
      }

      // Dead letter queue shouldn't be growing too fast
      const deadLetterCapacity = stats.deadLetterQueue.total / (this.queueConfig.maxQueueSize * 0.1);
      if (deadLetterCapacity > 0.8) {
        return false;
      }

      return true;
    } catch (error) {
      return false;
    }
  }

  /**
   * Get the next batch of items for processing
   */
  getNextBatch(batchSize?: number): QueueItem[] {
    const size = batchSize || this.queueConfig.batchSize;
    
    // Get batch prioritizing Index documents
    const batch = this.mainQueue.getBatch(size);
    this.processingBatch = [...this.processingBatch, ...batch];
    
    this.emit('batch:retrieved', { 
      batchSize: batch.length,
      indexCount: batch.filter(item => item.data.document_source === 'index').length,
      acteCount: batch.filter(item => item.data.document_source === 'acte').length
    });

    return batch;
  }

  /**
   * Mark items as successfully processed
   */
  async markBatchCompleted(items: QueueItem[]): Promise<void> {
    for (const item of items) {
      // Remove from processing batch
      const processingIndex = this.processingBatch.findIndex(p => p.id === item.id);
      if (processingIndex !== -1) {
        this.processingBatch.splice(processingIndex, 1);
      }

      // Update in database
      await this.supabaseService.markItemAsDownloaded(item.id, item.data.local_file_path || '');
    }

    this.emit('batch:completed', { count: items.length });
  }

  /**
   * Handle failed items from processing
   */
  async markBatchFailed(items: QueueItem[], errors: Record<string, string>): Promise<void> {
    for (const item of items) {
      const error = errors[item.id] || 'Unknown error';
      const newRetryCount = item.retryCount + 1;

      // Remove from processing batch
      const processingIndex = this.processingBatch.findIndex(p => p.id === item.id);
      if (processingIndex !== -1) {
        this.processingBatch.splice(processingIndex, 1);
      }

      // Check if item should go to dead letter queue
      if (newRetryCount >= this.queueConfig.deadLetterMaxRetries) {
        // Move to dead letter queue
        this.deadLetterQueue.enqueue(item.data);
        
        // Update database status
        await this.supabaseService.updateItemWithError(
          item.id,
          3, // All strategies tried
          'max_retries_exceeded',
          error,
          newRetryCount
        );

        this.emit('item:dead_letter', { itemId: item.id, error });
      } else {
        // Return to main queue with updated retry count
        item.retryCount = newRetryCount;
        item.lastError = error;
        this.mainQueue.returnBatch([item]);

        // Update database with retry info
        await this.supabaseService.updateItemWithError(
          item.id,
          1, // Will retry
          'processing_failed',
          error,
          newRetryCount
        );

        this.emit('item:retry', { itemId: item.id, retryCount: newRetryCount, error });
      }
    }

    this.emit('batch:failed', { count: items.length });
  }

  /**
   * Add new items to the queue
   */
  async addItems(items: ExtractionQueueItem[]): Promise<void> {
    for (const item of items) {
      try {
        this.mainQueue.enqueue(item);
        this.emit('item:added', { itemId: item.id, priority: this.calculateDisplayPriority(item) });
      } catch (error) {
        console.error(`Failed to add item ${item.id} to queue:`, error);
        this.emit('item:add_failed', { itemId: item.id, error });
      }
    }
  }

  /**
   * Get comprehensive queue statistics
   */
  getQueueStats(): QueueStats {
    const mainStats = this.mainQueue.getStats();
    const deadStats = this.deadLetterQueue.getStats();

    // Determine system health
    let systemHealth: 'healthy' | 'degraded' | 'unhealthy' = 'healthy';
    const mainCapacity = mainStats.total / this.queueConfig.maxQueueSize;
    const deadCapacity = deadStats.total / (this.queueConfig.maxQueueSize * 0.1);

    if (mainCapacity > 0.9 || deadCapacity > 0.8) {
      systemHealth = 'unhealthy';
    } else if (mainCapacity > 0.7 || deadCapacity > 0.5) {
      systemHealth = 'degraded';
    }

    return {
      mainQueue: mainStats,
      deadLetterQueue: deadStats,
      systemHealth
    };
  }

  /**
   * Sync with database to get new pending items
   */
  async syncWithDatabase(): Promise<void> {
    try {
      // Get all pending items from database
      const pendingItems = await this.supabaseService.getItemsForProcessing(100);
      
      if (pendingItems.length > 0) {
        await this.addItems(pendingItems);
        console.log(`Synced ${pendingItems.length} pending items from database`);
      }

      this.lastSync = new Date();
      this.emit('sync:completed', { itemCount: pendingItems.length });
    } catch (error) {
      console.error('Failed to sync with database:', error);
      this.emit('sync:failed', { error });
      throw error;
    }
  }

  /**
   * Get items from dead letter queue for manual review
   */
  getDeadLetterItems(): QueueItem[] {
    return this.deadLetterQueue.getStats().total > 0 
      ? this.deadLetterQueue.getBatch(this.deadLetterQueue.size())
      : [];
  }

  /**
   * Manually retry items from dead letter queue
   */
  retryDeadLetterItems(itemIds: string[]): void {
    const deadItems = this.getDeadLetterItems();
    const itemsToRetry = deadItems.filter(item => itemIds.includes(item.id));

    for (const item of itemsToRetry) {
      // Reset retry count and move back to main queue
      item.retryCount = 0;
      delete item.lastError;
      this.mainQueue.enqueue(item.data);
      this.deadLetterQueue.remove(item.id);
      
      this.emit('item:manual_retry', { itemId: item.id });
    }

    console.log(`Manually retried ${itemsToRetry.length} items from dead letter queue`);
  }

  /**
   * Clear dead letter queue
   */
  clearDeadLetterQueue(): void {
    const count = this.deadLetterQueue.size();
    this.deadLetterQueue.clear();
    this.emit('dead_letter:cleared', { count });
    console.log(`Cleared ${count} items from dead letter queue`);
  }

  /**
   * Get optimal batch size based on current queue state
   */
  getOptimalBatchSize(): number {
    const stats = this.getQueueStats();
    const baseSize = this.queueConfig.batchSize;

    // Increase batch size if queue is growing
    if (stats.mainQueue.total > this.queueConfig.maxQueueSize * 0.5) {
      return Math.min(baseSize * 2, this.queueConfig.maxQueueSize * 0.1);
    }

    // Decrease batch size if queue is small
    if (stats.mainQueue.total < baseSize) {
      return Math.max(1, Math.floor(stats.mainQueue.total / 2));
    }

    return baseSize;
  }

  /**
   * Load persisted queue state
   */
  private async loadPersistedState(): Promise<void> {
    try {
      if (this.queueConfig.persistenceFile) {
        const mainQueueFile = this.queueConfig.persistenceFile;
        if (await this.fileExists(mainQueueFile)) {
          const data = await fs.readFile(mainQueueFile, 'utf-8');
          this.mainQueue.importState(JSON.parse(data));
        }
      }

      if (this.queueConfig.deadLetterFile) {
        const deadLetterFile = this.queueConfig.deadLetterFile;
        if (await this.fileExists(deadLetterFile)) {
          const data = await fs.readFile(deadLetterFile, 'utf-8');
          this.deadLetterQueue.importState(JSON.parse(data));
        }
      }
    } catch (error) {
      console.warn('Failed to load persisted queue state:', error);
    }
  }

  /**
   * Save current queue state
   */
  private async savePersistedState(): Promise<void> {
    try {
      if (this.queueConfig.persistenceFile) {
        const data = JSON.stringify(this.mainQueue.exportState(), null, 2);
        await fs.writeFile(this.queueConfig.persistenceFile, data);
      }

      if (this.queueConfig.deadLetterFile) {
        const data = JSON.stringify(this.deadLetterQueue.exportState(), null, 2);
        await fs.writeFile(this.queueConfig.deadLetterFile, data);
      }
    } catch (error) {
      console.error('Failed to save queue state:', error);
    }
  }

  /**
   * Setup periodic sync with database
   */
  private setupPeriodicSync(): void {
    setInterval(async () => {
      try {
        await this.syncWithDatabase();
      } catch (error) {
        console.error('Periodic sync failed:', error);
      }
    }, this.queueConfig.healthCheckInterval);
  }

  /**
   * Check if file exists
   */
  private async fileExists(filePath: string): Promise<boolean> {
    try {
      await fs.access(filePath);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Calculate priority for display purposes
   */
  private calculateDisplayPriority(item: ExtractionQueueItem): number {
    return item.document_source === 'index' ? 1000 : 100;
  }
}
