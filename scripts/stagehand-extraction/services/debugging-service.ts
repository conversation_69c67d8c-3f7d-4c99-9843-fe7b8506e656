import { BaseService, ServiceConfig } from './base-service.js';
import { <PERSON><PERSON><PERSON>, <PERSON> } from 'playwright';
import * as fs from 'fs/promises';
import * as path from 'path';

export interface DebugSession {
  id: string;
  startTime: Date;
  documentType: string;
  searchParams: any;
  screenshots: string[];
  domSnapshots: any[];
  networkRequests: any[];
  errors: any[];
  currentStep: string;
  status: 'active' | 'completed' | 'failed';
}

export interface InterventionAction {
  type: 'retry' | 'skip' | 'modify_params' | 'manual_override';
  params?: any;
  reason: string;
  timestamp: Date;
}

export class DebuggingService extends BaseService {
  private activeSessions: Map<string, DebugSession> = new Map();
  private debugDirectory: string;
  private interventionCallbacks: Map<string, (action: InterventionAction) => Promise<void>> = new Map();

  constructor() {
    const config: ServiceConfig = {
      name: 'DebuggingService',
      retryAttempts: 3,
      retryDelay: 1000
    };
    super(config);
    this.debugDirectory = path.join(process.cwd(), 'debug-sessions');
  }

  async initialize(): Promise<void> {
    await this.initializeDebugDirectory();
    console.log('DebuggingService initialized');
  }

  async shutdown(): Promise<void> {
    // Clean up active sessions
    for (const [sessionId, session] of this.activeSessions) {
      await this.endDebugSession(sessionId, 'failed');
    }
    console.log('DebuggingService shutdown complete');
  }

  async healthCheck(): Promise<boolean> {
    try {
      // Check if debug directory exists and is writable
      await fs.access(this.debugDirectory);
      return true;
    } catch {
      return false;
    }
  }

  private async initializeDebugDirectory(): Promise<void> {
    try {
      await fs.mkdir(this.debugDirectory, { recursive: true });
    } catch (error) {
      console.error('Failed to create debug directory:', error);
    }
  }

  /**
   * Start a new debugging session for an extraction attempt
   */
  async startDebugSession(
    documentType: string,
    searchParams: any,
    interventionCallback?: (action: InterventionAction) => Promise<void>
  ): Promise<string> {
    const sessionId = `debug_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    const session: DebugSession = {
      id: sessionId,
      startTime: new Date(),
      documentType,
      searchParams,
      screenshots: [],
      domSnapshots: [],
      networkRequests: [],
      errors: [],
      currentStep: 'initialization',
      status: 'active'
    };

    this.activeSessions.set(sessionId, session);
    
    if (interventionCallback) {
      this.interventionCallbacks.set(sessionId, interventionCallback);
    }

    // Create session directory
    const sessionDir = path.join(this.debugDirectory, sessionId);
    await fs.mkdir(sessionDir, { recursive: true });

    console.log(`Started debug session: ${sessionId}`);
    this.emit('debug_session_started', { sessionId, session });

    return sessionId;
  }

  /**
   * Update the current step in a debug session
   */
  async updateDebugStep(sessionId: string, step: string): Promise<void> {
    const session = this.activeSessions.get(sessionId);
    if (!session) return;

    session.currentStep = step;
    console.debug(`Debug session ${sessionId} - Step: ${step}`);
    this.emit('debug_step_updated', { sessionId, step });
  }

  /**
   * Capture screenshot during debugging
   */
  async captureScreenshot(sessionId: string, page: Page, description: string): Promise<string> {
    const session = this.activeSessions.get(sessionId);
    if (!session) return '';

    try {
      const timestamp = Date.now();
      const filename = `screenshot_${timestamp}_${description.replace(/[^a-zA-Z0-9]/g, '_')}.png`;
      const filepath = path.join(this.debugDirectory, sessionId, filename);
      
      await page.screenshot({ 
        path: filepath, 
        fullPage: true,
        quality: 80
      });

      session.screenshots.push(filename);
      console.debug(`Screenshot captured: ${filename}`);
      this.emit('screenshot_captured', { sessionId, filename, description });

      return filepath;
    } catch (error) {
      console.error(`Failed to capture screenshot for session ${sessionId}:`, error);
      return '';
    }
  }

  /**
   * Capture DOM snapshot for analysis
   */
  async captureDOMSnapshot(sessionId: string, page: Page, description: string): Promise<void> {
    const session = this.activeSessions.get(sessionId);
    if (!session) return;

    try {
      const content = await page.content();
      const url = page.url();
      const timestamp = Date.now();

      const snapshot = {
        timestamp,
        description,
        url,
        content,
        title: await page.title().catch(() => 'Unknown'),
        viewport: await page.viewportSize()
      };

      session.domSnapshots.push(snapshot);

      // Save DOM snapshot to file
      const filename = `dom_snapshot_${timestamp}_${description.replace(/[^a-zA-Z0-9]/g, '_')}.html`;
      const filepath = path.join(this.debugDirectory, sessionId, filename);
      await fs.writeFile(filepath, content, 'utf-8');

      console.debug(`DOM snapshot captured: ${filename}`);
      this.emit('dom_snapshot_captured', { sessionId, filename, description });
    } catch (error) {
      console.error(`Failed to capture DOM snapshot for session ${sessionId}:`, error);
    }
  }

  /**
   * Monitor network requests during debugging
   */
  async startNetworkMonitoring(sessionId: string, page: Page): Promise<void> {
    const session = this.activeSessions.get(sessionId);
    if (!session) return;

    page.on('request', (request) => {
      const requestData = {
        timestamp: Date.now(),
        url: request.url(),
        method: request.method(),
        headers: request.headers(),
        resourceType: request.resourceType()
      };
      session.networkRequests.push(requestData);
    });

    page.on('response', (response) => {
      const responseData = {
        timestamp: Date.now(),
        url: response.url(),
        status: response.status(),
        statusText: response.statusText(),
        headers: response.headers()
      };
      // Find corresponding request and update it
      const requestIndex = session.networkRequests.findIndex(
        req => req.url === response.url() && !req.response
      );
      if (requestIndex >= 0) {
        session.networkRequests[requestIndex].response = responseData;
      }
    });

    console.debug(`Network monitoring started for session ${sessionId}`);
  }

  /**
   * Log error with context in debugging session
   */
  async logError(sessionId: string, error: any, context?: any): Promise<void> {
    const session = this.activeSessions.get(sessionId);
    if (!session) return;

    const errorData = {
      timestamp: Date.now(),
      message: error.message || error.toString(),
      stack: error.stack || null,
      context: context || {},
      currentStep: session.currentStep
    };

    session.errors.push(errorData);
    console.error(`Debug session ${sessionId} error:`, errorData);
    this.emit('debug_error_logged', { sessionId, error: errorData });
  }

  /**
   * Request manual intervention
   */
  async requestIntervention(
    sessionId: string, 
    reason: string, 
    suggestedActions: InterventionAction[]
  ): Promise<InterventionAction | null> {
    const session = this.activeSessions.get(sessionId);
    const callback = this.interventionCallbacks.get(sessionId);
    
    if (!session || !callback) {
      console.warn(`No intervention callback for session ${sessionId}`);
      return null;
    }

    console.log(`Intervention requested for session ${sessionId}: ${reason}`);
    this.emit('intervention_requested', { 
      sessionId, 
      reason, 
      suggestedActions,
      currentStep: session.currentStep
    });

    // For now, auto-select the first suggested action
    // In a full implementation, this would wait for user input
    const selectedAction = suggestedActions[0] || {
      type: 'retry',
      reason: 'Automatic retry after manual intervention request',
      timestamp: new Date()
    };

    await callback(selectedAction);
    return selectedAction;
  }

  /**
   * Analyze current page for debugging information
   */
  async analyzePage(sessionId: string, page: Page): Promise<any> {
    const session = this.activeSessions.get(sessionId);
    if (!session) return null;

    try {
      const analysis = await page.evaluate(() => {
        const forms = Array.from((document as any).forms).map((form: any) => ({
          action: form.action,
          method: form.method,
          elements: Array.from(form.elements).map((el: any) => ({
            type: el.type,
            name: el.name,
            id: el.id,
            tagName: el.tagName,
            className: el.className
          }))
        }));

        const inputs = Array.from((document as any).querySelectorAll('input, select, textarea, button')).map((el: any) => ({
          type: el.type,
          name: el.name,
          id: el.id,
          tagName: el.tagName,
          className: el.className,
          placeholder: el.placeholder || '',
          value: el.value || '',
          visible: (window as any).getComputedStyle(el).display !== 'none'
        }));

        const errors = Array.from((document as any).querySelectorAll('.error, .alert, .warning, [class*="error"], [class*="alert"]')).map((el: any) => ({
          text: el.textContent?.trim(),
          className: el.className,
          tagName: el.tagName
        }));

        return {
          url: (window as any).location.href,
          title: (document as any).title,
          forms,
          inputs,
          errors,
          hasJavaScript: true,
          isLoaded: (document as any).readyState === 'complete'
        };
      });

      console.debug(`Page analysis completed for session ${sessionId}`);
      this.emit('page_analyzed', { sessionId, analysis });

      return analysis;
    } catch (error) {
      console.error(`Failed to analyze page for session ${sessionId}:`, error);
      await this.logError(sessionId, error, { step: 'page_analysis' });
      return null;
    }
  }

  /**
   * End debug session and generate report
   */
  async endDebugSession(sessionId: string, status: 'completed' | 'failed'): Promise<void> {
    const session = this.activeSessions.get(sessionId);
    if (!session) return;

    session.status = status;
    const endTime = new Date();
    const duration = endTime.getTime() - session.startTime.getTime();

    // Generate comprehensive debug report
    const report = {
      sessionId,
      startTime: session.startTime,
      endTime,
      duration,
      documentType: session.documentType,
      searchParams: session.searchParams,
      status,
      steps: session.currentStep,
      screenshots: session.screenshots,
      domSnapshots: session.domSnapshots.length,
      networkRequests: session.networkRequests.length,
      errors: session.errors,
      summary: this.generateSessionSummary(session, duration)
    };

    // Save report to file
    const reportPath = path.join(this.debugDirectory, sessionId, 'debug_report.json');
    await fs.writeFile(reportPath, JSON.stringify(report, null, 2), 'utf-8');

    this.activeSessions.delete(sessionId);
    this.interventionCallbacks.delete(sessionId);

    console.log(`Debug session ${sessionId} ended with status: ${status}`);
    this.emit('debug_session_ended', { sessionId, status, report });
  }

  /**
   * Generate session summary
   */
  private generateSessionSummary(session: DebugSession, duration: number): any {
    return {
      totalDuration: duration,
      screenshotCount: session.screenshots.length,
      errorCount: session.errors.length,
      networkRequestCount: session.networkRequests.length,
      domSnapshotCount: session.domSnapshots.length,
      finalStep: session.currentStep,
      errorTypes: session.errors.map(e => e.message).slice(0, 5),
      successRate: session.errors.length === 0 ? 100 : 0
    };
  }

  /**
   * Get debugging information for active sessions
   */
  getActiveDebugSessions(): DebugSession[] {
    return Array.from(this.activeSessions.values());
  }

  /**
   * Get detailed session information
   */
  getDebugSession(sessionId: string): DebugSession | null {
    return this.activeSessions.get(sessionId) || null;
  }

  /**
   * Cleanup old debug sessions (older than 24 hours)
   */
  async cleanupOldSessions(): Promise<void> {
    const cutoffTime = Date.now() - (24 * 60 * 60 * 1000); // 24 hours ago
    
    try {
      const dirs = await fs.readdir(this.debugDirectory);
      
      for (const dir of dirs) {
        const dirPath = path.join(this.debugDirectory, dir);
        const stat = await fs.stat(dirPath);
        
        if (stat.isDirectory() && stat.mtime.getTime() < cutoffTime) {
          await fs.rmdir(dirPath, { recursive: true });
          console.debug(`Cleaned up old debug session: ${dir}`);
        }
      }
    } catch (error) {
      console.error('Failed to cleanup old debug sessions:', error);
    }
  }
}
