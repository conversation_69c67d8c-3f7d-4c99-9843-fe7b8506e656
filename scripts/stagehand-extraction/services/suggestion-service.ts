import { BaseService, ServiceConfig } from './base-service.js';
import { SupabaseClient } from '@supabase/supabase-js';

export interface ExtractionSuggestion {
  id: string;
  type: 'search_parameter' | 'field_mapping' | 'strategy' | 'timing';
  confidence: number;
  description: string;
  suggestedChange: any;
  reasoning: string;
  expectedImprovement: string;
  timestamp: Date;
  applied: boolean;
  effectiveness?: number;
}

export interface UserFeedback {
  suggestionId: string;
  accepted: boolean;
  actualImprovement?: number;
  userNotes?: string;
  timestamp: Date;
}

export interface LearningPattern {
  patternType: string;
  context: any;
  successRate: number;
  sampleSize: number;
  lastUpdated: Date;
}

export class SuggestionService extends BaseService {
  private supabase: SupabaseClient;
  private learningPatterns: Map<string, LearningPattern> = new Map();
  private suggestionHistory: Map<string, ExtractionSuggestion> = new Map();

  constructor(supabase: SupabaseClient) {
    const config: ServiceConfig = {
      name: 'SuggestionService',
      retryAttempts: 3,
      retryDelay: 1000
    };
    super(config);
    this.supabase = supabase;
  }

  async initialize(): Promise<void> {
    await this.loadLearningPatterns();
    console.log('SuggestionService initialized');
  }

  async shutdown(): Promise<void> {
    // Save any pending patterns to database
    await this.saveLearningPatterns();
    console.log('SuggestionService shutdown complete');
  }

  async healthCheck(): Promise<boolean> {
    try {
      // Check if we can query the database
      const { error } = await this.supabase.from('suggestion_patterns').select('count').limit(1);
      return !error;
    } catch {
      return false;
    }
  }

  /**
   * Load learning patterns from database
   */
  private async loadLearningPatterns(): Promise<void> {
    try {
      const { data, error } = await this.supabase
        .from('suggestion_patterns')
        .select('*');

      if (error) {
        console.error('Failed to load learning patterns:', error);
        return;
      }

      if (data) {
        data.forEach(pattern => {
          this.learningPatterns.set(pattern.pattern_type, {
            patternType: pattern.pattern_type,
            context: pattern.context,
            successRate: pattern.success_rate,
            sampleSize: pattern.sample_size,
            lastUpdated: new Date(pattern.last_updated)
          });
        });
      }

      console.log(`Loaded ${this.learningPatterns.size} learning patterns`);
    } catch (error) {
      console.error('Error loading learning patterns:', error);
    }
  }

  /**
   * Save learning patterns to database
   */
  private async saveLearningPatterns(): Promise<void> {
    try {
      const patterns = Array.from(this.learningPatterns.values()).map(pattern => ({
        pattern_type: pattern.patternType,
        context: pattern.context,
        success_rate: pattern.successRate,
        sample_size: pattern.sampleSize,
        last_updated: pattern.lastUpdated
      }));

      for (const pattern of patterns) {
        await this.supabase
          .from('suggestion_patterns')
          .upsert(pattern, { onConflict: 'pattern_type' });
      }
    } catch (error) {
      console.error('Error saving learning patterns:', error);
    }
  }

  /**
   * Analyze extraction attempt and generate suggestions
   */
  async generateSuggestions(context: {
    documentType: string;
    searchParams: any;
    extractionResult: any;
    errorContext?: any;
    processingTime?: number;
  }): Promise<ExtractionSuggestion[]> {
    const suggestions: ExtractionSuggestion[] = [];

    // Analyze search parameters
    const searchSuggestions = await this.analyzeSearchParameters(context);
    suggestions.push(...searchSuggestions);

    // Analyze field mapping issues
    const fieldSuggestions = await this.analyzeFieldMapping(context);
    suggestions.push(...fieldSuggestions);

    // Analyze extraction strategy
    const strategySuggestions = await this.analyzeExtractionStrategy(context);
    suggestions.push(...strategySuggestions);

    // Analyze timing issues
    const timingSuggestions = await this.analyzeTimingIssues(context);
    suggestions.push(...timingSuggestions);

    // Sort by confidence and expected impact
    suggestions.sort((a, b) => b.confidence - a.confidence);

    // Store suggestions
    suggestions.forEach(suggestion => {
      this.suggestionHistory.set(suggestion.id, suggestion);
    });

    console.log(`Generated ${suggestions.length} suggestions for ${context.documentType}`);
    this.emit('suggestions_generated', { context, suggestions });

    return suggestions;
  }

  /**
   * Analyze search parameters for optimization
   */
  private async analyzeSearchParameters(context: any): Promise<ExtractionSuggestion[]> {
    const suggestions: ExtractionSuggestion[] = [];
    const { documentType, searchParams, extractionResult } = context;

    // Check for date range optimization
    if (searchParams.date_debut && searchParams.date_fin) {
      const dateRange = new Date(searchParams.date_fin).getTime() - new Date(searchParams.date_debut).getTime();
      const daysDiff = dateRange / (1000 * 60 * 60 * 24);

      if (daysDiff > 365 && extractionResult?.success === false) {
        suggestions.push({
          id: `search_param_${Date.now()}_1`,
          type: 'search_parameter',
          confidence: 0.8,
          description: 'Large date range may be causing timeouts',
          suggestedChange: {
            strategy: 'split_date_range',
            maxDaysPerBatch: 180
          },
          reasoning: 'Date ranges over 1 year often cause server timeouts. Splitting into smaller batches improves success rate.',
          expectedImprovement: '40% improvement in success rate for large date ranges',
          timestamp: new Date(),
          applied: false
        });
      }
    }

    // Check for location specificity
    if (searchParams.lieu && typeof searchParams.lieu === 'string') {
      const pattern = this.learningPatterns.get(`location_${documentType}`);
      if (pattern && pattern.successRate < 0.6) {
        suggestions.push({
          id: `search_param_${Date.now()}_2`,
          type: 'search_parameter',
          confidence: 0.7,
          description: 'Location parameter may be too specific or incorrect',
          suggestedChange: {
            strategy: 'broaden_location',
            alternatives: this.generateLocationAlternatives(searchParams.lieu)
          },
          reasoning: 'Historical data shows low success rate for this location format. Broader terms may work better.',
          expectedImprovement: '25% improvement in document discovery',
          timestamp: new Date(),
          applied: false
        });
      }
    }

    // Check for name variations
    if (searchParams.nom_vendeur || searchParams.nom_acquereur) {
      const namePattern = this.learningPatterns.get(`names_${documentType}`);
      if (namePattern && namePattern.successRate < 0.7) {
        suggestions.push({
          id: `search_param_${Date.now()}_3`,
          type: 'search_parameter',
          confidence: 0.75,
          description: 'Name variations might improve search results',
          suggestedChange: {
            strategy: 'name_variations',
            variations: this.generateNameVariations(searchParams.nom_vendeur || searchParams.nom_acquereur)
          },
          reasoning: 'Different name formats and abbreviations often yield better results.',
          expectedImprovement: '30% improvement in name-based searches',
          timestamp: new Date(),
          applied: false
        });
      }
    }

    return suggestions;
  }

  /**
   * Analyze field mapping for improvements
   */
  private async analyzeFieldMapping(context: any): Promise<ExtractionSuggestion[]> {
    const suggestions: ExtractionSuggestion[] = [];
    const { extractionResult, errorContext } = context;

    if (errorContext?.fieldMappingErrors) {
      for (const error of errorContext.fieldMappingErrors) {
        suggestions.push({
          id: `field_mapping_${Date.now()}_${Math.random().toString(36).substr(2, 4)}`,
          type: 'field_mapping',
          confidence: 0.85,
          description: `Field mapping failed for: ${error.fieldName}`,
          suggestedChange: {
            strategy: 'alternative_selector',
            fieldName: error.fieldName,
            alternatives: this.generateSelectorAlternatives(error.selector)
          },
          reasoning: 'Form layout may have changed. Alternative selectors based on successful patterns.',
          expectedImprovement: '60% reduction in field mapping failures',
          timestamp: new Date(),
          applied: false
        });
      }
    }

    return suggestions;
  }

  /**
   * Analyze extraction strategy effectiveness
   */
  private async analyzeExtractionStrategy(context: any): Promise<ExtractionSuggestion[]> {
    const suggestions: ExtractionSuggestion[] = [];
    const { documentType, extractionResult, processingTime } = context;

    // Suggest strategy change based on performance
    if (processingTime && processingTime > 300000) { // 5 minutes
      suggestions.push({
        id: `strategy_${Date.now()}_1`,
        type: 'strategy',
        confidence: 0.7,
        description: 'Current strategy is taking too long',
        suggestedChange: {
          strategy: 'switch_to_faster',
          recommended: documentType === 'Index' ? 'exact-match' : 'intelligent-match'
        },
        reasoning: 'Processing time exceeds optimal thresholds. Faster strategies available.',
        expectedImprovement: '50% reduction in processing time',
        timestamp: new Date(),
        applied: false
      });
    }

    // Suggest parallel processing for bulk operations
    const pattern = this.learningPatterns.get(`bulk_${documentType}`);
    if (pattern && pattern.successRate > 0.8) {
      suggestions.push({
        id: `strategy_${Date.now()}_2`,
        type: 'strategy',
        confidence: 0.8,
        description: 'Bulk processing could improve efficiency',
        suggestedChange: {
          strategy: 'enable_bulk_processing',
          batchSize: 5,
          maxConcurrent: 3
        },
        reasoning: 'Historical data shows high success rate for bulk operations of this type.',
        expectedImprovement: '3x faster processing for multiple documents',
        timestamp: new Date(),
        applied: false
      });
    }

    return suggestions;
  }

  /**
   * Analyze timing issues and suggest improvements
   */
  private async analyzeTimingIssues(context: any): Promise<ExtractionSuggestion[]> {
    const suggestions: ExtractionSuggestion[] = [];
    const { errorContext } = context;

    if (errorContext?.timeoutErrors) {
      suggestions.push({
        id: `timing_${Date.now()}_1`,
        type: 'timing',
        confidence: 0.9,
        description: 'Timeout errors detected',
        suggestedChange: {
          strategy: 'increase_timeouts',
          pageTimeout: 60000,
          navigationTimeout: 45000,
          retryDelay: 5000
        },
        reasoning: 'Server appears slow. Increased timeouts and retry delays should improve success rate.',
        expectedImprovement: '70% reduction in timeout failures',
        timestamp: new Date(),
        applied: false
      });
    }

    return suggestions;
  }

  /**
   * Apply suggestion automatically or with user approval
   */
  async applySuggestion(
    suggestionId: string, 
    autoApply: boolean = false,
    userApproval?: boolean
  ): Promise<boolean> {
    const suggestion = this.suggestionHistory.get(suggestionId);
    if (!suggestion) {
      console.error(`Suggestion not found: ${suggestionId}`);
      return false;
    }

    if (!autoApply && !userApproval) {
      console.warn(`Suggestion ${suggestionId} requires user approval`);
      return false;
    }

    try {
      // Apply the suggestion based on type
      let applied = false;
      
      switch (suggestion.type) {
        case 'search_parameter':
          applied = await this.applySearchParameterSuggestion(suggestion);
          break;
        case 'field_mapping':
          applied = await this.applyFieldMappingSuggestion(suggestion);
          break;
        case 'strategy':
          applied = await this.applyStrategySuggestion(suggestion);
          break;
        case 'timing':
          applied = await this.applyTimingSuggestion(suggestion);
          break;
      }

      if (applied) {
        suggestion.applied = true;
        this.emit('suggestion_applied', { suggestionId, suggestion });
        console.log(`Applied suggestion: ${suggestionId}`);
      }

      return applied;
    } catch (error) {
      console.error(`Failed to apply suggestion ${suggestionId}:`, error);
      return false;
    }
  }

  /**
   * Record user feedback on suggestions
   */
  async recordFeedback(feedback: UserFeedback): Promise<void> {
    try {
      // Store feedback in database
      const { error } = await this.supabase
        .from('suggestion_feedback')
        .insert({
          suggestion_id: feedback.suggestionId,
          accepted: feedback.accepted,
          actual_improvement: feedback.actualImprovement,
          user_notes: feedback.userNotes,
          created_at: feedback.timestamp
        });

      if (error) {
        console.error('Failed to store suggestion feedback:', error);
        return;
      }

      // Update learning patterns based on feedback
      await this.updateLearningPatterns(feedback);

      this.emit('feedback_recorded', feedback);
      console.log(`Recorded feedback for suggestion: ${feedback.suggestionId}`);
    } catch (error) {
      console.error('Error recording suggestion feedback:', error);
    }
  }

  /**
   * Update learning patterns based on feedback
   */
  private async updateLearningPatterns(feedback: UserFeedback): Promise<void> {
    const suggestion = this.suggestionHistory.get(feedback.suggestionId);
    if (!suggestion) return;

    const patternKey = `${suggestion.type}_feedback`;
    const pattern = this.learningPatterns.get(patternKey) || {
      patternType: patternKey,
      context: {},
      successRate: 0.5,
      sampleSize: 0,
      lastUpdated: new Date()
    };

    // Update success rate using exponential moving average
    const alpha = 0.1; // Learning rate
    const newSuccess = feedback.accepted ? 1 : 0;
    pattern.successRate = (1 - alpha) * pattern.successRate + alpha * newSuccess;
    pattern.sampleSize += 1;
    pattern.lastUpdated = new Date();

    this.learningPatterns.set(patternKey, pattern);
  }

  /**
   * Generate location alternatives for suggestions
   */
  private generateLocationAlternatives(originalLocation: string): string[] {
    const alternatives = [];
    
    // Remove common prefixes/suffixes
    const cleaned = originalLocation.replace(/^(commune|ville) /i, '').replace(/ (commune|ville)$/i, '');
    alternatives.push(cleaned);
    
    // Add broader regional terms
    if (originalLocation.includes(',')) {
      const parts = originalLocation.split(',');
      alternatives.push(parts[0].trim()); // First part only
      alternatives.push(parts[parts.length - 1].trim()); // Last part only
    }
    
    // Add wildcard version
    alternatives.push(`${cleaned}*`);
    
    return alternatives;
  }

  /**
   * Generate name variations for suggestions
   */
  private generateNameVariations(originalName: string): string[] {
    const variations = [];
    
    // Common abbreviations
    const abbreviations = {
      'Jean': ['J.', 'J'],
      'Pierre': ['P.', 'P'],
      'Marie': ['M.', 'M'],
      'François': ['F.', 'F']
    };
    
    // Add variations with different formats
    variations.push(originalName.toUpperCase());
    variations.push(originalName.toLowerCase());
    
    // Split names and create variations
    const parts = originalName.split(' ');
    if (parts.length > 1) {
      variations.push(parts.join(', ')); // Last, First format
      variations.push(`${parts[0]} ${parts[parts.length - 1]}`); // First Last only
    }
    
    return variations;
  }

  /**
   * Generate selector alternatives for field mapping
   */
  private generateSelectorAlternatives(originalSelector: string): string[] {
    const alternatives = [];
    
    // If it's an ID selector, try name and class alternatives
    if (originalSelector.startsWith('#')) {
      const id = originalSelector.substring(1);
      alternatives.push(`[name="${id}"]`);
      alternatives.push(`[id*="${id}"]`);
      alternatives.push(`.${id}`);
    }
    
    // If it's a class selector, try ID and attribute alternatives
    if (originalSelector.startsWith('.')) {
      const className = originalSelector.substring(1);
      alternatives.push(`#${className}`);
      alternatives.push(`[class*="${className}"]`);
    }
    
    // Add generic fallbacks
    alternatives.push(`input[type="text"]`);
    alternatives.push(`select`);
    alternatives.push(`button[type="submit"]`);
    
    return alternatives;
  }

  /**
   * Implementation stubs for applying different types of suggestions
   */
  private async applySearchParameterSuggestion(suggestion: ExtractionSuggestion): Promise<boolean> {
    // Implementation would integrate with the extraction processor
    console.log('Applying search parameter suggestion:', suggestion.suggestedChange);
    return true;
  }

  private async applyFieldMappingSuggestion(suggestion: ExtractionSuggestion): Promise<boolean> {
    // Implementation would update field mapper service
    console.log('Applying field mapping suggestion:', suggestion.suggestedChange);
    return true;
  }

  private async applyStrategySuggestion(suggestion: ExtractionSuggestion): Promise<boolean> {
    // Implementation would update extraction strategy
    console.log('Applying strategy suggestion:', suggestion.suggestedChange);
    return true;
  }

  private async applyTimingSuggestion(suggestion: ExtractionSuggestion): Promise<boolean> {
    // Implementation would update timeout configurations
    console.log('Applying timing suggestion:', suggestion.suggestedChange);
    return true;
  }

  /**
   * Get all suggestions for a specific context
   */
  getSuggestions(filter?: Partial<ExtractionSuggestion>): ExtractionSuggestion[] {
    let suggestions = Array.from(this.suggestionHistory.values());
    
    if (filter) {
      suggestions = suggestions.filter(suggestion => {
        return Object.keys(filter).every(key => 
          suggestion[key as keyof ExtractionSuggestion] === filter[key as keyof ExtractionSuggestion]
        );
      });
    }
    
    return suggestions.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());
  }

  /**
   * Get learning patterns for analysis
   */
  getLearningPatterns(): LearningPattern[] {
    return Array.from(this.learningPatterns.values());
  }

  /**
   * Clear old suggestions (older than 30 days)
   */
  async cleanupOldSuggestions(): Promise<void> {
    const cutoffTime = Date.now() - (30 * 24 * 60 * 60 * 1000); // 30 days ago
    
    for (const [id, suggestion] of this.suggestionHistory) {
      if (suggestion.timestamp.getTime() < cutoffTime) {
        this.suggestionHistory.delete(id);
      }
    }
    
    console.log('Cleaned up old suggestions');
  }
}
