import { config as dotenvConfig } from 'dotenv';
import { ExtractionProcessor } from './extraction-processor';

// Load environment variables
dotenvConfig();

/**
 * Main entry point for the Stage Hand extraction processor
 */
async function main() {
  console.log('=================================');
  console.log('Stage Hand Document Extraction');
  console.log('=================================');
  console.log(`Mode: ${process.env.NODE_ENV || 'development'}`);
  console.log(`Debug: ${process.argv.includes('--debug') ? 'Enabled' : 'Disabled'}`);
  console.log('');

  // Override debug mode if --debug flag is passed
  if (process.argv.includes('--debug')) {
    process.env.DEBUG = 'true';
  }

  const processor = new ExtractionProcessor();

  try {
    // Run the extraction process
    await processor.run();
    
    // Clean up old downloads if requested
    if (process.argv.includes('--cleanup')) {
      console.log('\nCleaning up old downloads...');
      await processor.cleanupOldDownloads();
    }
    
    console.log('\nExtraction process completed successfully!');
    process.exit(0);
  } catch (error) {
    console.error('\nExtraction process failed:', error);
    process.exit(1);
  }
}

// Handle uncaught errors
process.on('unhandledRejection', (error) => {
  console.error('Unhandled Promise Rejection:', error);
  process.exit(1);
});

process.on('uncaughtException', (error) => {
  console.error('Uncaught Exception:', error);
  process.exit(1);
});

// Run main function
main().catch((error) => {
  console.error('Fatal error:', error);
  process.exit(1);
});
