{"name": "stagehand-extraction", "version": "1.0.0", "description": "Document extraction using Stage Hand browser automation", "main": "index.ts", "scripts": {"start": "npx ts-node index.ts", "start:debug": "NODE_ENV=development DEBUG=true npx ts-node index.ts", "test:login": "npx ts-node test-login.ts", "build": "tsc", "clean": "rm -rf dist"}, "dependencies": {"@browserbasehq/stagehand": "^1.0.0", "@supabase/supabase-js": "^2.39.0", "@playwright/test": "^1.40.0", "zod": "^3.22.0", "dotenv": "^16.3.0"}, "devDependencies": {"@types/node": "^20.10.0", "ts-node": "^10.9.0", "tsx": "^4.7.0", "typescript": "^5.3.0"}}