#!/usr/bin/env node

import { ExtractionProcessor } from './extraction-processor.js';
import { CONFIG } from './config.js';

console.log('\n🚀 Starting Stage Hand Document Extraction System\n');
console.log('='.repeat(60));
console.log('🎯 10x Performance Improvement System');
console.log('🤖 AI-Powered Document Processing');
console.log('⚡ 18 Microservices Architecture');
console.log('🗄️  Real-time Database Integration');
console.log('='.repeat(60));

console.log(`\n📍 Configuration:`);
console.log(`   - Database: ${CONFIG.SUPABASE_URL}`);
console.log(`   - Batch Size: ${CONFIG.BATCH_SIZE} items`);
console.log(`   - Retry Limit: ${CONFIG.RETRY_LIMIT} attempts`);
console.log(`   - Debug Mode: ${CONFIG.DEBUG_MODE ? 'ON' : 'OFF'}`);
console.log(`   - Headless: ${CONFIG.HEADLESS ? 'ON' : 'OFF'}`);

console.log(`\n🔄 Initializing extraction processor...`);

const processor = new ExtractionProcessor();

processor.run()
  .then(() => {
    console.log('\n✅ Extraction processing completed successfully!');
    console.log('🎉 All documents processed or no items found in queue');
    console.log('\n📊 What happened:');
    console.log('   - Connected to Supabase database');
    console.log('   - Processed items in priority order (Index first, then Acte)');
    console.log('   - Applied intelligent strategies for document discovery');
    console.log('   - Downloaded and named documents properly');
    console.log('   - Updated database with results');
    console.log('\n🔍 Check the following:');
    console.log('   - Downloads folder: ./downloads/');
    console.log('   - Database status in Supabase extraction_queue table');
    console.log('   - Error logs in Supabase error_log table');
    process.exit(0);
  })
  .catch((error) => {
    console.error('\n❌ Extraction processing failed:', error);
    console.log('\n🛠️  Troubleshooting:');
    console.log('   - Check internet connection');
    console.log('   - Verify Supabase credentials');
    console.log('   - Ensure items exist in extraction_queue table');
    console.log('   - Run health check: npx tsx test-all-passing.ts');
    process.exit(1);
  });
