#!/usr/bin/env node

import { PriorityQueue } from './services/priority-queue.js';
import { DebuggingService } from './services/debugging-service.js';
import { SuggestionService } from './services/suggestion-service.js';
import { DocumentEnhancementService } from './services/document-enhancement-service.js';
import { createClient } from '@supabase/supabase-js';

// Mock Supabase client for testing
const mockSupabase = createClient('https://mock.supabase.co', 'mock-key');

interface TestResult {
  test: string;
  passed: boolean;
  message: string;
  duration: number;
}

class BasicFunctionalityTest {
  private results: TestResult[] = [];

  private async runTest(testName: string, testFn: () => Promise<void>): Promise<void> {
    const startTime = Date.now();
    try {
      await testFn();
      const duration = Date.now() - startTime;
      this.results.push({
        test: testName,
        passed: true,
        message: 'Test passed successfully',
        duration
      });
      console.log(`✅ ${testName} - PASSED (${duration}ms)`);
    } catch (error) {
      const duration = Date.now() - startTime;
      this.results.push({
        test: testName,
        passed: false,
        message: error instanceof Error ? error.message : 'Unknown error',
        duration
      });
      console.log(`❌ ${testName} - FAILED (${duration}ms): ${error}`);
    }
  }

  async testPriorityQueue(): Promise<void> {
    await this.runTest('Priority Queue Basic Operations', async () => {
      const queue = new PriorityQueue<{ id: string; type: string }>();

      // Test enqueue and dequeue
      queue.enqueue({ id: 'test-1', type: 'Acte' }, 100);
      queue.enqueue({ id: 'test-2', type: 'Index' }, 1000);
      queue.enqueue({ id: 'test-3', type: 'Acte' }, 100);

      // Check that Index has higher priority
      const firstItem = queue.dequeue();
      if (!firstItem || firstItem.type !== 'Index') {
        throw new Error('Priority queue not working correctly - Index should have highest priority');
      }

      // Check size
      if (queue.size() !== 2) {
        throw new Error('Queue size incorrect after dequeue');
      }

      // Check isEmpty
      queue.dequeue();
      queue.dequeue();
      if (!queue.isEmpty()) {
        throw new Error('Queue should be empty after dequeuing all items');
      }
    });
  }

  async testDebuggingService(): Promise<void> {
    await this.runTest('Debugging Service', async () => {
      const debugService = new DebuggingService();
      await debugService.initialize();

      // Test debug session creation
      const sessionId = await debugService.startDebugSession(
        'Index',
        { test: true }
      );

      if (!sessionId) {
        throw new Error('Failed to create debug session');
      }

      // Test session retrieval
      const session = debugService.getDebugSession(sessionId);
      if (!session || session.documentType !== 'Index') {
        throw new Error('Failed to retrieve debug session');
      }

      // Test updating debug step
      await debugService.updateDebugStep(sessionId, 'testing');
      const updatedSession = debugService.getDebugSession(sessionId);
      if (updatedSession?.currentStep !== 'testing') {
        throw new Error('Failed to update debug step');
      }

      // Test ending session
      await debugService.endDebugSession(sessionId, 'completed');
      await debugService.shutdown();
    });
  }

  async testSuggestionService(): Promise<void> {
    await this.runTest('Suggestion Service', async () => {
      const suggestionService = new SuggestionService(mockSupabase);
      await suggestionService.initialize();

      // Test suggestion generation
      const suggestions = await suggestionService.generateSuggestions({
        documentType: 'Index',
        searchParams: {
          date_debut: '2020-01-01',
          date_fin: '2023-12-31'
        },
        extractionResult: { success: false },
        processingTime: 350000
      });

      if (!Array.isArray(suggestions)) {
        throw new Error('Suggestions should be an array');
      }

      // Test getting suggestions with filter
      const filteredSuggestions = suggestionService.getSuggestions({ type: 'search_parameter' });
      if (!Array.isArray(filteredSuggestions)) {
        throw new Error('Filtered suggestions should be an array');
      }

      // Test learning patterns
      const patterns = suggestionService.getLearningPatterns();
      if (!Array.isArray(patterns)) {
        throw new Error('Learning patterns should be an array');
      }

      await suggestionService.shutdown();
    });
  }

  async testDocumentEnhancement(): Promise<void> {
    await this.runTest('Document Enhancement Service', async () => {
      const enhancementService = new DocumentEnhancementService();
      await enhancementService.initialize();

      // Test document number extraction
      const documentNumber = await enhancementService.extractDocumentNumber('Index', {
        filename: 'Index_2023-12-01-12345.pdf',
        url: 'https://example.com/doc/12345',
        pageContent: 'Document number: 2023-12-01-12345'
      });

      if (!documentNumber) {
        throw new Error('Failed to extract document number');
      }

      console.log(`   - Extracted document number: ${documentNumber}`);

      // Test filename generation
      const namingResult = await enhancementService.generateFilename('Index', {
        filename: 'old_document.pdf',
        pageContent: 'Document: 2023-12-01-67890'
      });

      if (!namingResult.finalName) {
        throw new Error('Failed to generate filename');
      }

      console.log(`   - Generated filename: ${namingResult.finalName}`);

      // Test filename validation
      const validation = enhancementService.validateFilename('2023-12-01-12345.pdf', 'Index');
      if (typeof validation.isValid !== 'boolean') {
        throw new Error('Filename validation not working');
      }

      // Test extraction testing
      const testResult = enhancementService.testExtraction('Index', 'Index_2023-12-01-12345.pdf');
      if (!testResult.documentNumber) {
        throw new Error('Test extraction failed');
      }

      console.log(`   - Test extraction confidence: ${testResult.confidence}`);

      // Test naming statistics
      const stats = enhancementService.getNamingStatistics();
      if (typeof stats.totalRules !== 'number') {
        throw new Error('Naming statistics not working');
      }

      console.log(`   - Total naming rules: ${stats.totalRules}`);

      await enhancementService.shutdown();
    });
  }

  async testServiceHealthChecks(): Promise<void> {
    await this.runTest('Service Health Checks', async () => {
      const services = [
        new DebuggingService(),
        new SuggestionService(mockSupabase),
        new DocumentEnhancementService()
      ];

      for (const service of services) {
        await service.initialize();
        const isHealthy = await service.healthCheck();
        if (!isHealthy) {
          throw new Error(`Service ${service.constructor.name} health check failed`);
        }
        await service.shutdown();
      }
    });
  }

  async testIntegrationScenario(): Promise<void> {
    await this.runTest('Integration Scenario', async () => {
      // Test a realistic workflow
      const queue = new PriorityQueue<{ id: string; type: string; data: any }>();
      const debugService = new DebuggingService();
      const suggestionService = new SuggestionService(mockSupabase);
      const enhancementService = new DocumentEnhancementService();

      await debugService.initialize();
      await suggestionService.initialize();
      await enhancementService.initialize();

      // Simulate processing workflow
      const extractionItem = {
        id: 'integration-test-1',
        type: 'Index',
        data: {
          searchParams: {
            nom_vendeur: 'TEST VENDOR',
            date_debut: '2023-01-01'
          }
        }
      };

      // Add to queue
      queue.enqueue(extractionItem, 1000);

      // Start debugging session
      const sessionId = await debugService.startDebugSession(
        extractionItem.type,
        extractionItem.data.searchParams
      );

      // Process item
      const item = queue.dequeue();
      if (!item || item.id !== extractionItem.id) {
        throw new Error('Queue processing failed');
      }

      // Generate suggestions for failed extraction
      const suggestions = await suggestionService.generateSuggestions({
        documentType: item.type,
        searchParams: item.data.searchParams,
        extractionResult: { success: false },
        processingTime: 45000
      });

      // Simulate document found and name it
      const namingResult = await enhancementService.generateFilename(item.type, {
        filename: 'downloaded_document.pdf',
        pageContent: 'Document number: 2023-01-01-54321'
      });

      // End debugging session
      await debugService.endDebugSession(sessionId, 'completed');

      // Verify workflow
      if (suggestions.length === 0) {
        throw new Error('No suggestions generated');
      }

      if (!namingResult.success) {
        throw new Error('Document naming failed');
      }

      console.log(`   - Processed item: ${item.id}`);
      console.log(`   - Generated ${suggestions.length} suggestions`);
      console.log(`   - Document named: ${namingResult.finalName}`);

      // Cleanup
      await debugService.shutdown();
      await suggestionService.shutdown();
      await enhancementService.shutdown();
    });
  }

  async runAllTests(): Promise<void> {
    console.log('\n🧪 Starting Basic Functionality Test Suite\n');
    console.log('='.repeat(50));

    await this.testPriorityQueue();
    await this.testDebuggingService();
    await this.testSuggestionService();
    await this.testDocumentEnhancement();
    await this.testServiceHealthChecks();
    await this.testIntegrationScenario();

    this.printResults();
  }

  private printResults(): void {
    console.log('\n' + '='.repeat(50));
    console.log('🎯 TEST RESULTS SUMMARY');
    console.log('='.repeat(50));

    const passed = this.results.filter(r => r.passed).length;
    const failed = this.results.filter(r => !r.passed).length;
    const total = this.results.length;

    console.log(`\n📊 Overall Results: ${passed}/${total} tests passed`);
    console.log(`✅ Passed: ${passed}`);
    console.log(`❌ Failed: ${failed}`);
    console.log(`📈 Success Rate: ${((passed / total) * 100).toFixed(1)}%`);

    if (failed > 0) {
      console.log('\n❌ Failed Tests:');
      this.results
        .filter(r => !r.passed)
        .forEach(r => {
          console.log(`   - ${r.test}: ${r.message}`);
        });
    }

    console.log('\n⏱️  Performance Summary:');
    const totalTime = this.results.reduce((sum, r) => sum + r.duration, 0);
    const avgTime = totalTime / this.results.length;
    console.log(`   - Total Test Time: ${totalTime}ms`);
    console.log(`   - Average Test Time: ${avgTime.toFixed(1)}ms`);
    console.log(`   - Fastest Test: ${Math.min(...this.results.map(r => r.duration))}ms`);
    console.log(`   - Slowest Test: ${Math.max(...this.results.map(r => r.duration))}ms`);

    console.log('\n🎉 Basic Functionality Test Complete!');
    
    if (passed === total) {
      console.log('🌟 ALL BASIC TESTS PASSED - Core functionality working! 🌟');
    } else {
      console.log('⚠️  Some tests failed - Review core implementation');
    }

    console.log('\n📋 Test Coverage:');
    console.log('   ✅ Priority Queue Operations');
    console.log('   ✅ Debug Session Management');
    console.log('   ✅ Suggestion Generation');
    console.log('   ✅ Document Enhancement');
    console.log('   ✅ Service Health Checks');
    console.log('   ✅ Integration Workflow');
  }
}

// Run the basic test suite
const testSuite = new BasicFunctionalityTest();
testSuite.runAllTests().catch(console.error);
