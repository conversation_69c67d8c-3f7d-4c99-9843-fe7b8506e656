import { z } from 'zod';
import { ActeSearchParams, IndexSearchParams } from '../config';
import { ErrorLogger } from '../utils/error-logger';
import { StagehandPage } from '../types';

export class ExactMatchStrategy {
  private errorLogger: <PERSON>rrorLogger;

  constructor(errorLogger: <PERSON>rrorLogger) {
    this.errorLogger = errorLogger;
  }

  /**
   * Execute exact match strategy for Acte documents
   */
  async executeActeSearch(
    page: StagehandPage,
    params: ActeSearchParams,
    _itemId: string
  ): Promise<void> {
    // Handle circonscription selection if provided
    if (params.circonscription) {
      this.errorLogger.logAttempt(params.inscriptionNumber, 1, 'select_circonscription', true);
      await page.act(`select '${params.circonscription}' from the dropdown with id 'selCircnFoncr'`);
      await page.waitForTimeout(500);
    }

    // Handle acte type selection
    const acteType = params.acteType || 'Acte';
    switch (acteType) {
      case 'Radiation':
        this.errorLogger.logAttempt(params.inscriptionNumber, 1, 'select_radiation', true);
        await page.act("click on the radio button with id 'radRdrtn'");
        break;
      case 'Avis d\'adresse':
        this.errorLogger.logAttempt(params.inscriptionNumber, 1, 'select_avis_adresse', true);
        await page.act("click on the radio button with id 'radAvis'");
        break;
      case 'Acte divers':
      case 'Acte':
      default:
        this.errorLogger.logAttempt(params.inscriptionNumber, 1, 'select_acte', true);
        await page.act("click on the radio button with id 'radAct'");
        break;
    }
    
    await page.waitForTimeout(500);

    // Fill inscription number
    this.errorLogger.logAttempt(params.inscriptionNumber, 1, 'fill_inscription_number', true);
    await page.act(`click on the 'Numéro d'inscription' text field and fill it with '${params.inscriptionNumber}'`);
    await page.waitForTimeout(1000);

    // Submit the form
    this.errorLogger.logAttempt(params.inscriptionNumber, 1, 'submit_form', true);
    await page.act("click the 'Rechercher' button");
  }

  /**
   * Execute exact match strategy for Index documents
   */
  async executeIndexSearch(
    page: StagehandPage,
    params: IndexSearchParams,
    _itemId: string
  ): Promise<void> {
    // Handle circonscription selection if provided
    if (params.circonscription) {
      this.errorLogger.logAttempt(params.lotNumber, 1, 'select_circonscription', true);
      await page.act(`select '${params.circonscription}' from the dropdown with id 'selCircnFoncr'`);
      await page.waitForTimeout(500);
    }

    // Handle cadastre selection if provided
    if (params.cadastre) {
      this.errorLogger.logAttempt(params.lotNumber, 1, 'select_cadastre', true);
      await page.act(`select '${params.cadastre}' from the 'Cadastre' dropdown`);
      await page.waitForTimeout(500);
    }

    // Fill designation secondaire if provided
    if (params.designationSecondaire) {
      this.errorLogger.logAttempt(params.lotNumber, 1, 'fill_designation_secondaire', true);
      await page.act(`fill the 'Désignation secondaire' field with '${params.designationSecondaire}'`);
      await page.waitForTimeout(500);
    }

    // Fill lot number
    this.errorLogger.logAttempt(params.lotNumber, 1, 'fill_lot_number', true);
    await page.act(`click on the 'Numéro de lot' text field and fill it with '${params.lotNumber}'`);
    await page.waitForTimeout(1000);

    // Submit the form
    this.errorLogger.logAttempt(params.lotNumber, 1, 'submit_form', true);
    await page.act("click the 'Soumettre' button");
  }

  /**
   * Verify if we're on the results page
   */
  async verifyResultsPage(page: StagehandPage, documentNumber: string): Promise<boolean> {
    try {
      // Wait for frames to load
      await page.waitForTimeout(3000);

      // Check for error messages that indicate document not found
      const errorCheck = await page.extract({
        instruction: "Check for error messages like 'Aucun document', 'introuvable', 'n'existe pas', 'Aucun lot', 'Erreur', or 'Invalid'",
        schema: z.object({
          hasError: z.boolean(),
          errorMessage: z.string().optional(),
          errorKeywords: z.array(z.string()).optional()
        })
      });

      if (errorCheck.hasError) {
        this.errorLogger.logAttempt(documentNumber, 1, 'verify_results', false, errorCheck);
        return false;
      }

      // Enhanced frame checking based on working playwright script
      const frameCheck = await page.extract({
        instruction: "Check if there are frames named 'page' and 'frmNavgt' which indicate results are loaded. Look specifically for nested frames where the 'page' frame contains a 'frmNavgt' frame.",
        schema: z.object({
          hasPageFrame: z.boolean(),
          hasNavFrame: z.boolean(),
          frameNames: z.array(z.string()).optional(),
          hasNestedFrames: z.boolean().optional()
        })
      });

      // Enhanced print button check with frame context
      const printButtonCheck = await page.extract({
        instruction: "Check if there is an 'Imprimer' (Print) button on the page or inside the nested frames (page -> frmNavgt). Look for links or buttons with text 'Imprimer'.",
        schema: z.object({
          hasPrintButton: z.boolean(),
          buttonText: z.string().optional(),
          buttonLocation: z.string().optional(),
          inFrames: z.boolean().optional()
        })
      });

      // Additional check for document content that indicates successful load
      const contentCheck = await page.extract({
        instruction: "Check if there is document content visible, such as document text, PDF viewer, or document-related interface elements",
        schema: z.object({
          hasDocumentContent: z.boolean(),
          contentType: z.string().optional()
        })
      });

      const hasResults = frameCheck.hasPageFrame || frameCheck.hasNavFrame || printButtonCheck.hasPrintButton || contentCheck.hasDocumentContent;
      
      console.log('🔍 Results verification:', {
        hasPageFrame: frameCheck.hasPageFrame,
        hasNavFrame: frameCheck.hasNavFrame,
        hasPrintButton: printButtonCheck.hasPrintButton,
        hasDocumentContent: contentCheck.hasDocumentContent,
        finalResult: hasResults
      });

      this.errorLogger.logAttempt(documentNumber, 1, 'verify_results', hasResults, { 
        frameCheck, 
        printButtonCheck, 
        contentCheck 
      });
      
      return hasResults;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.log('❌ Error verifying results page:', errorMessage);
      this.errorLogger.logAttempt(documentNumber, 1, 'verify_results', false, { error: errorMessage });
      return false;
    }
  }

  /**
   * Click the print button to download the document
   */
  async clickPrintButton(page: StagehandPage, documentNumber: string): Promise<void> {
    this.errorLogger.logAttempt(documentNumber, 1, 'click_print', true);
    
    // Wait for frames to load
    await page.waitForTimeout(3000);
    
    // Try multiple specific approaches based on the working playwright script
    try {
      // First attempt: Try the exact path from the working playwright script
      console.log('🎯 Attempting to click Imprimer button in nested frames...');
      await page.act("in the frame named 'page', then in its child frame named 'frmNavgt', click the 'Imprimer' link");
      console.log('✅ Successfully clicked Imprimer button using nested frame path');
      return;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.log('❌ First attempt failed:', errorMessage);
    }

    try {
      // Second attempt: More specific frame navigation
      console.log('🎯 Trying alternative frame navigation...');
      await page.act("navigate to frame 'page', then to frame 'frmNavgt', and click the link with text 'Imprimer'");
      console.log('✅ Successfully clicked Imprimer button using alternative frame navigation');
      return;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.log('❌ Second attempt failed:', errorMessage);
    }

    try {
      // Third attempt: Look for frames and then print button
      console.log('🎯 Trying frame detection and print button click...');
      await page.act("find the frame containing navigation controls, then click the 'Imprimer' button inside it");
      console.log('✅ Successfully clicked Imprimer button using frame detection');
      return;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.log('❌ Third attempt failed:', errorMessage);
    }

    try {
      // Fourth attempt: Direct search for Imprimer in any frame
      console.log('🎯 Trying direct search for Imprimer in any frame...');
      await page.act("search all frames on the page for a link or button with text 'Imprimer' and click it");
      console.log('✅ Successfully clicked Imprimer button using direct frame search');
      return;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.log('❌ Fourth attempt failed:', errorMessage);
    }

    try {
      // Fifth attempt: Look for any print-related element
      console.log('🎯 Trying broad print button search...');
      await page.act("look for any clickable element with text containing 'Imprimer', 'Print', or 'print' and click it");
      console.log('✅ Successfully clicked print element using broad search');
      return;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.log('❌ Fifth attempt failed:', errorMessage);
    }

    // If all attempts fail, throw error with context
    const errorMessage = `Failed to find and click Imprimer button after 5 attempts. The button should be located in nested frames: page -> frmNavgt -> Imprimer link.`;
    this.errorLogger.logAttempt(documentNumber, 1, 'click_print', false, { error: errorMessage });
    throw new Error(errorMessage);
  }
}
