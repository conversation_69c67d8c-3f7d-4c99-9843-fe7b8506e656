import { z } from 'zod';
import { ActeSearchParams, IndexSearchParams } from '../config';
import { <PERSON><PERSON>rLogger } from '../utils/error-logger';
import { StagehandPage } from '../types';

export class IntelligentMatchStrategy {
  private errorLogger: <PERSON><PERSON><PERSON>Logger;

  constructor(errorLogger: <PERSON><PERSON>rLogger) {
    this.errorLogger = errorLogger;
  }

  /**
   * Calculate Levenshtein distance between two strings
   */
  private levenshteinDistance(str1: string, str2: string): number {
    const matrix: number[][] = [];
    
    // Initialize the matrix
    for (let i = 0; i <= str2.length; i++) {
      matrix[i] = [i];
    }
    
    for (let j = 0; j <= str1.length; j++) {
      matrix[0][j] = j;
    }
    
    // Calculate distances
    for (let i = 1; i <= str2.length; i++) {
      for (let j = 1; j <= str1.length; j++) {
        if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
          matrix[i][j] = matrix[i - 1][j - 1];
        } else {
          matrix[i][j] = Math.min(
            matrix[i - 1][j - 1] + 1, // substitution
            matrix[i][j - 1] + 1,     // insertion
            matrix[i - 1][j] + 1      // deletion
          );
        }
      }
    }
    
    return matrix[str2.length][str1.length];
  }

  /**
   * Find the best matching option from a list
   */
  private findBestMatch(target: string, options: string[]): string | null {
    if (options.length === 0) return null;
    
    const normalizedTarget = target.toLowerCase().trim();
    let bestMatch = options[0];
    let bestScore = this.levenshteinDistance(normalizedTarget, options[0].toLowerCase().trim());
    
    for (const option of options) {
      const normalizedOption = option.toLowerCase().trim();
      const score = this.levenshteinDistance(normalizedTarget, normalizedOption);
      
      if (score < bestScore) {
        bestScore = score;
        bestMatch = option;
      }
    }
    
    // Only return if the match is reasonably close
    if (bestScore <= normalizedTarget.length * 0.3) {
      return bestMatch;
    }
    
    return null;
  }

  /**
   * Generate variations of a string for flexible matching
   */
  private generateVariations(input: string): string[] {
    const variations = [input];
    
    // Remove spaces
    variations.push(input.replace(/\s+/g, ''));
    
    // Replace spaces with hyphens
    variations.push(input.replace(/\s+/g, '-'));
    
    // Remove special characters
    variations.push(input.replace(/[^a-zA-Z0-9]/g, ''));
    
    // Common abbreviations and expansions
    if (input.includes('Rang')) {
      variations.push(input.replace('Rang t', 'Rang '));
      variations.push(input.replace('Rang', 'R'));
    }
    
    if (input.includes('St-') || input.includes('Saint-')) {
      variations.push(input.replace('St-', 'Saint-'));
      variations.push(input.replace('Saint-', 'St-'));
    }
    
    if (input.includes('Ste-') || input.includes('Sainte-')) {
      variations.push(input.replace('Ste-', 'Sainte-'));
      variations.push(input.replace('Sainte-', 'Ste-'));
    }
    
    // Remove duplicates
    return [...new Set(variations)];
  }

  /**
   * Extract dropdown options from the page
   */
  private async extractDropdownOptions(
    page: StagehandPage,
    dropdownLabel: string
  ): Promise<string[]> {
    try {
      const options = await page.extract({
        instruction: `Extract all available options from the '${dropdownLabel}' dropdown menu`,
        schema: z.object({
          options: z.array(z.string())
        })
      });
      
      return options.options;
    } catch (error) {
      this.errorLogger.logAttempt('', 2, `extract_${dropdownLabel}_options`, false, { error });
      return [];
    }
  }

  /**
   * Execute intelligent match strategy for Acte documents
   */
  async executeActeSearch(
    page: StagehandPage,
    params: ActeSearchParams,
    _itemId: string
  ): Promise<void> {
    // Handle circonscription with fuzzy matching
    if (params.circonscription) {
      this.errorLogger.logAttempt(params.inscriptionNumber, 2, 'extract_circonscription_options', true);
      const options = await this.extractDropdownOptions(page, 'Circonscription foncière');
      
      if (options.length > 0) {
        const bestMatch = this.findBestMatch(params.circonscription, options);
        
        if (bestMatch) {
          this.errorLogger.logAttempt(params.inscriptionNumber, 2, 'select_circonscription_fuzzy', true, { 
            original: params.circonscription, 
            matched: bestMatch 
          });
          await page.act(`select '${bestMatch}' from the 'Circonscription foncière' dropdown`);
        } else {
          this.errorLogger.logAttempt(params.inscriptionNumber, 2, 'select_circonscription_fuzzy', false, { 
            reason: 'No suitable match found' 
          });
        }
      }
    }

    // Handle acte type (usually exact enough)
    if (params.acteType) {
      await page.act(`click the '${params.acteType}' radio button or similar option`);
    }

    // Try variations of inscription number
    const variations = this.generateVariations(params.inscriptionNumber);
    let filled = false;
    
    for (const variation of variations) {
      try {
        this.errorLogger.logAttempt(params.inscriptionNumber, 2, 'fill_inscription_variation', true, { variation });
        await page.act(`fill the inscription number or 'Numéro d'inscription' field with '${variation}'`);
        filled = true;
        break;
      } catch (error) {
        // Continue with next variation
      }
    }
    
    if (!filled) {
      // Try a more generic approach
      await page.act(`fill any number field that looks like it accepts inscription numbers with '${params.inscriptionNumber}'`);
    }

    // Submit with flexible button matching
    await page.act("click the search or 'Rechercher' button");
  }

  /**
   * Execute intelligent match strategy for Index documents
   */
  async executeIndexSearch(
    page: StagehandPage,
    params: IndexSearchParams,
    _itemId: string
  ): Promise<void> {
    // Handle circonscription with fuzzy matching
    if (params.circonscription) {
      const options = await this.extractDropdownOptions(page, 'Circonscription foncière');
      
      if (options.length > 0) {
        const bestMatch = this.findBestMatch(params.circonscription, options);
        
        if (bestMatch) {
          await page.act(`select '${bestMatch}' from the 'Circonscription foncière' dropdown`);
        }
      }
    }

    // Handle cadastre with fuzzy matching
    if (params.cadastre) {
      const options = await this.extractDropdownOptions(page, 'Cadastre');
      
      if (options.length > 0) {
        const bestMatch = this.findBestMatch(params.cadastre, options);
        
        if (bestMatch) {
          await page.act(`select '${bestMatch}' from the 'Cadastre' dropdown`);
        }
      }
    }

    // Fill designation secondaire with variations
    if (params.designationSecondaire) {
      const variations = this.generateVariations(params.designationSecondaire);
      
      for (const variation of variations) {
        try {
          await page.act(`fill the 'Désignation secondaire' or secondary designation field with '${variation}'`);
          break;
        } catch (error) {
          // Continue with next variation
        }
      }
    }

    // Try variations of lot number
    const lotVariations = this.generateVariations(params.lotNumber);
    let filled = false;
    
    for (const variation of lotVariations) {
      try {
        this.errorLogger.logAttempt(params.lotNumber, 2, 'fill_lot_variation', true, { variation });
        await page.act(`fill the lot number or 'Numéro de lot' field with '${variation}'`);
        filled = true;
        break;
      } catch (error) {
        // Continue with next variation
      }
    }
    
    if (!filled) {
      // Try a more generic approach
      await page.act(`fill any field that accepts lot numbers with '${params.lotNumber}'`);
    }

    // Submit with flexible button matching
    await page.act("click the submit or 'Soumettre' button");
  }

  /**
   * Try alternative form submission methods
   */
  async tryAlternativeSubmission(page: StagehandPage, documentNumber: string): Promise<boolean> {
    try {
      // Try pressing Enter
      this.errorLogger.logAttempt(documentNumber, 2, 'submit_with_enter', true);
      await page.act("press Enter key to submit the form");
      return true;
    } catch (error) {
      try {
        // Try finding any submit-like button
        this.errorLogger.logAttempt(documentNumber, 2, 'submit_any_button', true);
        await page.act("click any button that looks like it would submit or search");
        return true;
      } catch (error2) {
        this.errorLogger.logAttempt(documentNumber, 2, 'alternative_submission', false, { error: error2 });
        return false;
      }
    }
  }

  /**
   * Verify if we're on the results page with intelligent frame detection
   */
  async verifyResultsPage(page: StagehandPage, documentNumber: string): Promise<boolean> {
    try {
      // Wait for frames to load
      await page.waitForTimeout(3000);

      // Check for error messages with fuzzy matching
      const errorCheck = await page.extract({
        instruction: "Check for error messages like 'Aucun document', 'introuvable', 'n'existe pas', 'Aucun lot', 'Erreur', 'Invalid', or similar error indicators",
        schema: z.object({
          hasError: z.boolean(),
          errorMessage: z.string().optional(),
          errorType: z.string().optional(),
          confidence: z.number().optional()
        })
      });

      if (errorCheck.hasError) {
        this.errorLogger.logAttempt(documentNumber, 2, 'verify_results_intelligent', false, errorCheck);
        return false;
      }

      // Intelligent frame detection
      const frameAnalysis = await page.extract({
        instruction: "Analyze the page structure for frames. Look for frames named 'page', 'frmNavgt', or similar navigation frames. Also check for iframe elements or nested document structures.",
        schema: z.object({
          hasFrames: z.boolean(),
          frameNames: z.array(z.string()).optional(),
          hasNestedStructure: z.boolean().optional(),
          frameStructure: z.string().optional()
        })
      });

      // Intelligent print button detection
      const printButtonAnalysis = await page.extract({
        instruction: "Look for print functionality. This could be an 'Imprimer' button, 'Print' button, print icon, or any element that would trigger document printing. Check both main page and any frames.",
        schema: z.object({
          hasPrintOption: z.boolean(),
          printElementType: z.string().optional(),
          printElementText: z.string().optional(),
          printLocation: z.string().optional(),
          inFrame: z.boolean().optional()
        })
      });

      // Content analysis for document presence
      const contentAnalysis = await page.extract({
        instruction: "Check if there is document content visible, such as PDF viewer, document text, tables with document information, or any indication that a document was successfully loaded",
        schema: z.object({
          hasDocumentContent: z.boolean(),
          contentType: z.string().optional(),
          documentIndicators: z.array(z.string()).optional()
        })
      });

      // Calculate confidence score
      const indicators = [
        frameAnalysis.hasFrames,
        frameAnalysis.hasNestedStructure,
        printButtonAnalysis.hasPrintOption,
        contentAnalysis.hasDocumentContent
      ];

      const successCount = indicators.filter(Boolean).length;
      const hasResults = successCount >= 2; // Need at least 2 positive indicators

      console.log('🧠 Intelligent results verification:', {
        hasFrames: frameAnalysis.hasFrames,
        hasPrintOption: printButtonAnalysis.hasPrintOption,
        hasDocumentContent: contentAnalysis.hasDocumentContent,
        successCount,
        finalResult: hasResults
      });

      this.errorLogger.logAttempt(documentNumber, 2, 'verify_results_intelligent', hasResults, { 
        frameAnalysis, 
        printButtonAnalysis, 
        contentAnalysis,
        successCount 
      });
      
      return hasResults;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.log('❌ Error in intelligent results verification:', errorMessage);
      this.errorLogger.logAttempt(documentNumber, 2, 'verify_results_intelligent', false, { error: errorMessage });
      return false;
    }
  }

  /**
   * Intelligently click the print button with multiple strategies
   */
  async clickPrintButton(page: StagehandPage, documentNumber: string): Promise<void> {
    this.errorLogger.logAttempt(documentNumber, 2, 'click_print_intelligent', true);
    
    // Wait for frames and content to load
    await page.waitForTimeout(5000);

    // Strategy 1: Frame-aware extraction and navigation (most reliable)
    try {
      console.log('🧠 Intelligent Strategy 1: Frame-aware extraction...');

      // First, extract frame structure to understand the page layout
      const frameAnalysis = await page.extract({
        instruction: "Analyze the page structure and identify all frames, particularly looking for 'page' frame and 'frmNavgt' frame. Also check if there's an 'Imprimer' link visible anywhere.",
        schema: z.object({
          hasPageFrame: z.boolean(),
          hasNavFrame: z.boolean(),
          frameStructure: z.string().optional(),
          imprimerVisible: z.boolean(),
          imprimerLocation: z.string().optional()
        })
      });

      console.log('Intelligent frame analysis result:', frameAnalysis);

      if (frameAnalysis.hasPageFrame && frameAnalysis.hasNavFrame) {
        // Use the known working pattern from Playwright scripts
        await page.act("wait for the frame named 'page' to be fully loaded");
        await page.waitForTimeout(2000);

        await page.act("within the frame named 'page', wait for the nested frame named 'frmNavgt' to be available");
        await page.waitForTimeout(2000);

        await page.act("in the frame 'page', then in its nested frame 'frmNavgt', click the link with text 'Imprimer'");
        console.log('✅ Successfully clicked Imprimer using intelligent frame-aware navigation');
        return;
      }
    } catch (error) {
      console.log('❌ Intelligent Strategy 1 failed:', error instanceof Error ? error.message : String(error));
    }

    // Strategy 2: Direct frame navigation
    try {
      console.log('🧠 Intelligent Strategy 2: Direct frame navigation...');
      await page.act("navigate to the frame named 'page', then to its nested frame 'frmNavgt', and click the 'Imprimer' link");
      console.log('✅ Successfully clicked Imprimer using intelligent frame navigation');
      return;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.log('❌ Intelligent Strategy 2 failed:', errorMessage);
    }

    // Strategy 3: Semantic search for print functionality
    try {
      console.log('🧠 Intelligent Strategy 3: Semantic print search...');
      await page.act("find any element with print-related functionality (text like 'Imprimer', 'Print', 'Impression' or print icons) and click it");
      console.log('✅ Successfully clicked print element using semantic search');
      return;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.log('❌ Intelligent Strategy 3 failed:', errorMessage);
    }

    // Strategy 4: Context-aware frame search
    try {
      console.log('🧠 Intelligent Strategy 4: Context-aware frame search...');
      await page.act("search all frames and nested documents for navigation controls, then look for print or 'Imprimer' functionality within those controls");
      console.log('✅ Successfully clicked using context-aware search');
      return;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.log('❌ Intelligent Strategy 4 failed:', errorMessage);
    }

    // Strategy 5: Pattern recognition approach
    try {
      console.log('🧠 Intelligent Strategy 5: Pattern recognition...');
      await page.act("look for typical document viewer patterns: toolbar with print option, right-click menu, keyboard shortcut trigger, or any standard print interface element");
      console.log('✅ Successfully clicked using pattern recognition');
      return;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.log('❌ Intelligent Strategy 5 failed:', errorMessage);
    }

    // Strategy 6: Exhaustive search
    try {
      console.log('🧠 Intelligent Strategy 6: Exhaustive search...');
      await page.act("perform an exhaustive search of all clickable elements on the page and in all frames for anything related to printing, downloading, or document export");
      console.log('✅ Successfully clicked using exhaustive search');
      return;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.log('❌ Intelligent Strategy 6 failed:', errorMessage);
    }

    // If all intelligent strategies fail
    const errorMessage = `Intelligent print button detection failed after 6 strategies. Expected to find 'Imprimer' link in nested frames (page -> frmNavgt).`;
    this.errorLogger.logAttempt(documentNumber, 2, 'click_print_intelligent', false, { error: errorMessage });
    throw new Error(errorMessage);
  }
}
