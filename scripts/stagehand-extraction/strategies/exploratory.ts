import { z } from 'zod';
import { ActeSearchParams, IndexSearchParams } from '../config';
import { ErrorLogger } from '../utils/error-logger';
import { StagehandPage } from '../types';

export class ExploratoryStrategy {
  private errorLogger: <PERSON>rrorLogger;

  constructor(errorLogger: <PERSON>rrorLogger) {
    this.errorLogger = errorLogger;
  }

  /**
   * Find alternative search methods on the page
   */
  private async findAlternativeSearchMethods(
    page: StagehandPage,
    documentNumber: string
  ): Promise<any> {
    try {
      const alternatives = await page.extract({
        instruction: `Find any alternative search methods, advanced search links, or different ways to search for documents. Look for links containing words like "avancée", "autre méthode", "recherche différente", "options", etc.`,
        schema: z.object({
          links: z.array(z.object({
            text: z.string(),
            description: z.string().optional()
          })),
          forms: z.array(z.object({
            description: z.string(),
            fields: z.array(z.string())
          })).optional()
        })
      });

      this.errorLogger.logAttempt(documentNumber, 3, 'find_alternatives', true, alternatives);
      return alternatives;
    } catch (error) {
      this.errorLogger.logAttempt(documentNumber, 3, 'find_alternatives', false, { error });
      return { links: [], forms: [] };
    }
  }

  /**
   * Analyze page structure to understand available fields
   */
  private async analyzePageStructure(
    page: StagehandPage,
    documentNumber: string
  ): Promise<any> {
    try {
      const structure = await page.extract({
        instruction: `Analyze the page to find all form fields, their labels, and any helpful information about what data they accept. Include input fields, dropdowns, radio buttons, and checkboxes.`,
        schema: z.object({
          fields: z.array(z.object({
            type: z.string(),
            label: z.string(),
            required: z.boolean().optional(),
            placeholder: z.string().optional(),
            options: z.array(z.string()).optional()
          })),
          hints: z.array(z.string()).optional()
        })
      });

      this.errorLogger.logAttempt(documentNumber, 3, 'analyze_structure', true, structure);
      return structure;
    } catch (error) {
      this.errorLogger.logAttempt(documentNumber, 3, 'analyze_structure', false, { error });
      return { fields: [], hints: [] };
    }
  }

  /**
   * Try to use contextual clues to complete the search
   */
  private async useContextualClues(
    page: StagehandPage,
    documentNumber: string,
    documentType: 'acte' | 'index'
  ): Promise<boolean> {
    try {
      // Look for any field that might accept the document number
      this.errorLogger.logAttempt(documentNumber, 3, 'contextual_fill', true);
      
      if (documentType === 'acte') {
        await page.act(`fill any field that looks like it could accept an inscription number or document number with '${documentNumber}'`);
      } else {
        await page.act(`fill any field that looks like it could accept a lot number with '${documentNumber}'`);
      }

      // Try to submit in any way possible
      await page.act(`click any button that looks like it would search or submit the form`);
      
      return true;
    } catch (error) {
      this.errorLogger.logAttempt(documentNumber, 3, 'contextual_fill', false, { error });
      return false;
    }
  }

  /**
   * Execute exploratory strategy for Acte documents
   */
  async executeActeSearch(
    page: StagehandPage,
    params: ActeSearchParams,
    _itemId: string
  ): Promise<void> {
    this.errorLogger.logAttempt(params.inscriptionNumber, 3, 'exploratory_start', true);

    // First, try to find alternative search methods
    const alternatives = await this.findAlternativeSearchMethods(page, params.inscriptionNumber);

    // Try clicking on alternative search links
    for (const link of alternatives.links || []) {
      if (link.text.toLowerCase().includes('avancé') || 
          link.text.toLowerCase().includes('autre') ||
          link.text.toLowerCase().includes('option')) {
        try {
          this.errorLogger.logAttempt(params.inscriptionNumber, 3, 'click_alternative_link', true, { link: link.text });
          await page.act(`click on the '${link.text}' link`);
          
          // Wait for new page to load
          await page.waitForTimeout(2000);
          
          // Analyze the new page structure
          const structure = await this.analyzePageStructure(page, params.inscriptionNumber);
          
          // Try to fill any relevant fields
          for (const field of structure.fields) {
            if (field.label.toLowerCase().includes('inscription') ||
                field.label.toLowerCase().includes('numéro') ||
                field.label.toLowerCase().includes('document')) {
              try {
                await page.act(`fill the '${field.label}' field with '${params.inscriptionNumber}'`);
              } catch (error) {
                // Continue with other fields
              }
            }
          }
          
          // Try to submit
          await page.act(`click any submit or search button`);
          return;
        } catch (error) {
          this.errorLogger.logAttempt(params.inscriptionNumber, 3, 'alternative_approach', false, { error });
        }
      }
    }

    // If no alternatives found, try contextual approach
    await this.useContextualClues(page, params.inscriptionNumber, 'acte');
    
    // Last resort: try to navigate through menus
    try {
      this.errorLogger.logAttempt(params.inscriptionNumber, 3, 'menu_navigation', true);
      await page.act(`look for and click on any menu items related to document search, actes, or registrations`);
      await page.waitForTimeout(2000);
      await this.useContextualClues(page, params.inscriptionNumber, 'acte');
    } catch (error) {
      this.errorLogger.logAttempt(params.inscriptionNumber, 3, 'menu_navigation', false, { error });
    }
  }

  /**
   * Execute exploratory strategy for Index documents
   */
  async executeIndexSearch(
    page: StagehandPage,
    params: IndexSearchParams,
    _itemId: string
  ): Promise<void> {
    this.errorLogger.logAttempt(params.lotNumber, 3, 'exploratory_start', true);

    // First, try to find alternative search methods
    const alternatives = await this.findAlternativeSearchMethods(page, params.lotNumber);

    // Try clicking on alternative search links
    for (const link of alternatives.links || []) {
      if (link.text.toLowerCase().includes('avancé') || 
          link.text.toLowerCase().includes('autre') ||
          link.text.toLowerCase().includes('cadastr')) {
        try {
          this.errorLogger.logAttempt(params.lotNumber, 3, 'click_alternative_link', true, { link: link.text });
          await page.act(`click on the '${link.text}' link`);
          
          // Wait for new page to load
          await page.waitForTimeout(2000);
          
          // Analyze the new page structure
          const structure = await this.analyzePageStructure(page, params.lotNumber);
          
          // Try to fill any relevant fields
          for (const field of structure.fields) {
            if (field.label.toLowerCase().includes('lot') ||
                field.label.toLowerCase().includes('cadastr') ||
                field.label.toLowerCase().includes('parcelle')) {
              try {
                await page.act(`fill the '${field.label}' field with '${params.lotNumber}'`);
              } catch (error) {
                // Continue with other fields
              }
            }
          }
          
          // Try to submit
          await page.act(`click any submit or search button`);
          return;
        } catch (error) {
          this.errorLogger.logAttempt(params.lotNumber, 3, 'alternative_approach', false, { error });
        }
      }
    }

    // If no alternatives found, try contextual approach
    await this.useContextualClues(page, params.lotNumber, 'index');
    
    // Last resort: try to navigate through property/cadastre menus
    try {
      this.errorLogger.logAttempt(params.lotNumber, 3, 'cadastre_navigation', true);
      await page.act(`look for and click on any menu items related to cadastre, lots, or property search`);
      await page.waitForTimeout(2000);
      await this.useContextualClues(page, params.lotNumber, 'index');
    } catch (error) {
      this.errorLogger.logAttempt(params.lotNumber, 3, 'cadastre_navigation', false, { error });
    }
  }

  /**
   * Check if we accidentally navigated away from search
   */
  async checkIfStillOnSearchPage(page: StagehandPage): Promise<boolean> {
    try {
      const pageInfo = await page.extract({
        instruction: "Check if this is still a search page with forms, or if we navigated to a different section",
        schema: z.object({
          isSearchPage: z.boolean(),
          pageType: z.string().optional()
        })
      });

      return pageInfo.isSearchPage;
    } catch (error) {
      return false;
    }
  }

  /**
   * Try to recover and get back to search
   */
  async recoverToSearchPage(page: StagehandPage, documentType: 'acte' | 'index'): Promise<void> {
    try {
      if (documentType === 'acte') {
        await page.act("navigate back to the acte search page or click on 'Recherche d'actes'");
      } else {
        await page.act("navigate back to the index search page or click on 'Index des immeubles'");
      }
    } catch (error) {
      // Try using browser back button
      await page.act("go back to the previous page");
    }
  }

  /**
   * Verify if we're on the results page with exploratory detection
   */
  async verifyResultsPage(page: StagehandPage, documentNumber: string): Promise<boolean> {
    try {
      // Wait for frames to load
      await page.waitForTimeout(3000);

      // Exploratory error detection
      const errorAnalysis = await page.extract({
        instruction: "Search exhaustively for any error messages, warning text, or indicators that the document was not found. Look for words like 'Aucun', 'introuvable', 'n'existe pas', 'Erreur', 'Invalid', 'not found', etc.",
        schema: z.object({
          hasError: z.boolean(),
          errorIndicators: z.array(z.string()).optional(),
          errorContext: z.string().optional()
        })
      });

      if (errorAnalysis.hasError) {
        this.errorLogger.logAttempt(documentNumber, 3, 'verify_results_exploratory', false, errorAnalysis);
        return false;
      }

      // Comprehensive page structure analysis
      const structureAnalysis = await page.extract({
        instruction: "Analyze the entire page structure. Look for frames, iframes, nested documents, popup windows, or any container that might hold document content. Also check for navigation elements that would indicate successful document loading.",
        schema: z.object({
          hasFrames: z.boolean(),
          frameTypes: z.array(z.string()).optional(),
          hasNavigationElements: z.boolean().optional(),
          hasDocumentContainer: z.boolean().optional(),
          pageStructure: z.string().optional()
        })
      });

      // Exploratory print detection - look everywhere
      const printAnalysis = await page.extract({
        instruction: "Search everywhere for print functionality. Look in toolbars, menus, context menus, frames, floating panels, or anywhere there might be a print option. Check for 'Imprimer', 'Print', print icons, or download options.",
        schema: z.object({
          hasPrintOption: z.boolean(),
          printLocations: z.array(z.string()).optional(),
          printTypes: z.array(z.string()).optional(),
          printContext: z.string().optional()
        })
      });

      // Document content detection
      const contentAnalysis = await page.extract({
        instruction: "Look for any signs that a document was successfully loaded. This could be text content, PDF viewer, document metadata, file information, or any visual indication that content is available.",
        schema: z.object({
          hasContent: z.boolean(),
          contentTypes: z.array(z.string()).optional(),
          contentDescription: z.string().optional()
        })
      });

      // Success indicators analysis
      const successIndicators = [
        structureAnalysis.hasFrames,
        structureAnalysis.hasNavigationElements,
        structureAnalysis.hasDocumentContainer,
        printAnalysis.hasPrintOption,
        contentAnalysis.hasContent
      ];

      const positiveCount = successIndicators.filter(Boolean).length;
      const hasResults = positiveCount >= 1; // More lenient for exploratory

      console.log('🔍 Exploratory results verification:', {
        hasFrames: structureAnalysis.hasFrames,
        hasNavigationElements: structureAnalysis.hasNavigationElements,
        hasPrintOption: printAnalysis.hasPrintOption,
        hasContent: contentAnalysis.hasContent,
        positiveCount,
        finalResult: hasResults
      });

      this.errorLogger.logAttempt(documentNumber, 3, 'verify_results_exploratory', hasResults, { 
        structureAnalysis, 
        printAnalysis, 
        contentAnalysis,
        positiveCount 
      });
      
      return hasResults;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.log('❌ Error in exploratory results verification:', errorMessage);
      this.errorLogger.logAttempt(documentNumber, 3, 'verify_results_exploratory', false, { error: errorMessage });
      return false;
    }
  }

  /**
   * Exploratory print button click with exhaustive search
   */
  async clickPrintButton(page: StagehandPage, documentNumber: string): Promise<void> {
    this.errorLogger.logAttempt(documentNumber, 3, 'click_print_exploratory', true);
    
    // Wait for everything to load
    await page.waitForTimeout(5000);

    // Strategy 1: Frame-aware extraction and navigation (most reliable)
    try {
      console.log('🔍 Exploratory Strategy 1: Frame-aware extraction...');

      // First, extract frame structure to understand the page layout
      const frameAnalysis = await page.extract({
        instruction: "Analyze the page structure and identify all frames, particularly looking for 'page' frame and 'frmNavgt' frame. Also check if there's an 'Imprimer' link visible anywhere.",
        schema: z.object({
          hasPageFrame: z.boolean(),
          hasNavFrame: z.boolean(),
          frameStructure: z.string().optional(),
          imprimerVisible: z.boolean(),
          imprimerLocation: z.string().optional()
        })
      });

      console.log('Exploratory frame analysis result:', frameAnalysis);

      if (frameAnalysis.hasPageFrame && frameAnalysis.hasNavFrame) {
        // Use the known working pattern from Playwright scripts
        await page.act("wait for the frame named 'page' to be fully loaded");
        await page.waitForTimeout(2000);

        await page.act("within the frame named 'page', wait for the nested frame named 'frmNavgt' to be available");
        await page.waitForTimeout(2000);

        await page.act("in the frame 'page', then in its nested frame 'frmNavgt', click the link with text 'Imprimer'");
        console.log('✅ Successfully clicked Imprimer using exploratory frame-aware navigation');
        return;
      }
    } catch (error) {
      console.log('❌ Exploratory Strategy 1 failed:', error instanceof Error ? error.message : String(error));
    }

    // Strategy 2: Known frame structure approach
    try {
      console.log('🔍 Exploratory Strategy 2: Known frame structure...');
      await page.act("navigate to frame 'page', then to nested frame 'frmNavgt', then click the 'Imprimer' link");
      console.log('✅ Successfully clicked Imprimer using known frame structure');
      return;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.log('❌ Exploratory Strategy 2 failed:', errorMessage);
    }

    // Strategy 3: Comprehensive frame exploration
    try {
      console.log('🔍 Exploratory Strategy 3: Frame exploration...');
      await page.act("explore all frames on the page, find any frame that contains navigation or document controls, and look for an 'Imprimer' or print button within it");
      console.log('✅ Successfully clicked using frame exploration');
      return;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.log('❌ Exploratory Strategy 3 failed:', errorMessage);
    }

    // Strategy 4: Text-based exhaustive search
    try {
      console.log('🔍 Exploratory Strategy 4: Text-based search...');
      await page.act("search the entire page and all its frames for any text containing 'Imprimer' and try to click it");
      console.log('✅ Successfully clicked using text-based search');
      return;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.log('❌ Exploratory Strategy 4 failed:', errorMessage);
    }

    // Strategy 5: Element type exploration
    try {
      console.log('🔍 Exploratory Strategy 5: Element type exploration...');
      await page.act("find all links, buttons, and clickable elements on the page and in frames, then click any that might trigger printing or document download");
      console.log('✅ Successfully clicked using element exploration');
      return;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.log('❌ Exploratory Strategy 5 failed:', errorMessage);
    }

    // Strategy 6: Keyboard shortcuts and context menus
    try {
      console.log('🔍 Exploratory Strategy 6: Alternative interaction methods...');
      await page.act("try keyboard shortcuts like Ctrl+P or right-click for context menu with print options");
      console.log('✅ Successfully clicked using alternative methods');
      return;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.log('❌ Exploratory Strategy 6 failed:', errorMessage);
    }

    // Strategy 7: Visual-based search for print icons
    try {
      console.log('🔍 Exploratory Strategy 7: Visual icon search...');
      await page.act("look for any print icons, printer symbols, or visual elements that might represent printing functionality and click them");
      console.log('✅ Successfully clicked using visual icon search');
      return;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.log('❌ Exploratory Strategy 7 failed:', errorMessage);
    }

    // If all exploratory strategies fail
    const errorMessage = `Exploratory print button search failed after 7 comprehensive strategies. Could not locate 'Imprimer' button in expected frames or anywhere else on the page.`;
    this.errorLogger.logAttempt(documentNumber, 3, 'click_print_exploratory', false, { error: errorMessage });
    throw new Error(errorMessage);
  }
}
