import { Page as PlaywrightPage } from '@playwright/test';
import { z } from 'zod';

// Extend Playwright's Page type with <PERSON>hand's methods
export interface StagehandPage extends PlaywrightPage {
  act: (instruction: string) => Promise<void>;
  extract: <T>(options: {
    instruction: string;
    schema: z.ZodSchema<T>;
  }) => Promise<T>;
  observe: (instruction: string) => Promise<any[]>;
}

// Return type for extraction functions
export interface ExtractionResult {
  success: boolean;
  filePath?: string;
  error?: string;
  failedStrategyAttempt?: number;
}

// Step definition for strategies
export interface ExtractionStep {
  name: string;
  action: string;
}

export interface Alert {
  id: string;
  timestamp: Date;
  severity: 'low' | 'medium' | 'high' | 'critical';
  category: 'system' | 'performance' | 'quality' | 'security' | 'business';
  title: string;
  description: string;
  source: string;
  tags: string[];
  metadata: Record<string, any>;
  status: 'active' | 'acknowledged' | 'resolved' | 'suppressed';
  acknowledgedBy?: string;
  acknowledgedAt?: Date;
  resolvedAt?: Date;
  escalationLevel: number;
  correlationId?: string;
  type: string; // Added type property, as per original requirement
  message: string; // Added for notification-service
  context: { // Added for notification-service
    itemId?: string;
    documentNumber?: string;
    service?: string;
    errorType?: string;
    metrics?: Record<string, any>;
    ruleId?: string; // Added for alert-manager
    triggerMetrics?: Record<string, any>; // Added for alert-manager
  };
  acknowledged: boolean; // Added for notification-service
  resolved: boolean; // Added for notification-service
  suppressUntil?: Date; // Added for notification-service
}
