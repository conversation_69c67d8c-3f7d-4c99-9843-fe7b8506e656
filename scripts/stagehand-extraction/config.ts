// Configuration for Stage Hand Document Extraction

export const CONFIG = {
  // Supabase Configuration
  SUPABASE_URL: process.env.SUPABASE_URL || "https://sqzqvxqcybghcgrpubsy.supabase.co",
  SUPABASE_KEY: process.env.SUPABASE_SERVICE_KEY || "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InNxenF2eHFjeWJnaGNncnB1YnN5Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0MTA5NTUxOCwiZXhwIjoyMDU2NjcxNTE4fQ.YN8lGh9PurkS8dq7a7zzlrgoETGyWltcJrLnOBI7t6M",
  
  // Registre Foncier Credentials
  USER_CODE: process.env.RFQ_USER_CODE || "30F3315",
  PASSWORD: process.env.RFQ_PASSWORD || "Sainte-Clara1504!",
  
  // URLs
  BASE_URL: "https://www.registrefoncier.gouv.qc.ca/Sirf/",
  ACTE_SEARCH_URL: "https://www.registrefoncier.gouv.qc.ca/Sirf/Script/13_01_11/pf_13_01_11_08_reqst.asp",
  INDEX_SEARCH_URL: "https://www.registrefoncier.gouv.qc.ca/Sirf/Script/13_01_11/pf_13_01_11_02_indx_immbl.asp",
  
  // Stage Hand Configuration
  MODEL_NAME: process.env.LLM_MODEL || "gpt-4o",
  MODEL_API_KEY: process.env.OPENAI_API_KEY,
  HEADLESS: process.env.NODE_ENV === 'production',
  ENABLE_CACHING: true,
  SELF_HEAL: true,
  VERBOSE: process.env.DEBUG === 'true' ? 2 : 1,
  
  // Processing Configuration
  BATCH_SIZE: parseInt(process.env.BATCH_SIZE || '10'),
  RETRY_LIMIT: 3,
  
  // Timeouts (in milliseconds)
  NAVIGATION_TIMEOUT: 60000,
  DOWNLOAD_TIMEOUT: 30000,
  DOM_SETTLE_TIMEOUT: 30000,
  ACTION_DELAY: 2000, // Delay between actions to avoid overwhelming the site
  
  // File Management
  BASE_DOWNLOAD_DIR: process.env.DOWNLOAD_DIR || "./scripts/stagehand-extraction/downloads",
  
  // Debug Mode
  DEBUG_MODE: process.env.DEBUG === 'true'
};

// Type definitions for extraction queue items
export interface ExtractionQueueItem {
  id: string;
  document_source: 'acte' | 'index';
  document_number: string;
  circonscription_fonciere?: string;
  acte_type?: 'Acte' | 'Avis d\'adresse' | 'Radiation' | 'Acte divers';
  cadastre?: string;
  designation_secondaire?: string;
  status: 'En_attente' | 'En_traitement' | 'Telecharge' | 'Disponible_sur_Drive' | 'Document_introuvable';
  retry_count: number;
  local_file_path?: string;
  error_message?: string;
  processing_started_at?: string;
  request_id?: string;
  acte_id?: string;
  index_id?: string;
}

// Error tracking structure
export interface ErrorLog {
  itemId: string;
  documentNumber: string;
  option: 1 | 2 | 3;
  step: string;
  error: string;
  screenshot?: string;
  timestamp: string;
}

// Search parameters for documents
export interface ActeSearchParams {
  inscriptionNumber: string;
  circonscription?: string;
  acteType?: string;
}

export interface IndexSearchParams {
  lotNumber: string;
  circonscription?: string;
  cadastre?: string;
  designationSecondaire?: string;
}
