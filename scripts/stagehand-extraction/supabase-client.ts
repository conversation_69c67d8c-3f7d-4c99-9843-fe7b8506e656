import { createClient, SupabaseClient } from '@supabase/supabase-js';
import { CONFIG, ExtractionQueueItem } from './config';

export class SupabaseService {
  private supabase: SupabaseClient;

  constructor() {
    this.supabase = createClient(CONFIG.SUPABASE_URL, CONFIG.SUPABASE_KEY);
  }

  /**
   * Get the next pending item from the queue
   */
  async getNextEnAttenteItem(): Promise<ExtractionQueueItem | null> {
    try {
      const { data, error } = await this.supabase
        .from('extraction_queue')
        .select('*')
        .eq('status', 'En_attente')
        .order('created_at', { ascending: true })
        .limit(1)
        .single();
      
      if (error) {
        if (error.code === 'PGRST116') {
          // No rows returned
          return null;
        }
        throw error;
      }
      
      return data as ExtractionQueueItem;
    } catch (error: any) {
      if (error.code === 'PGRST116') {
        return null;
      }
      console.error('Error fetching next pending item:', error);
      throw error;
    }
  }

  /**
   * Update an item's status in the extraction queue
   */
  async updateItemStatus(
    itemId: string, 
    updates: Partial<ExtractionQueueItem>
  ): Promise<void> {
    try {
      const { error } = await this.supabase
        .from('extraction_queue')
        .update({
          ...updates,
          updated_at: new Date().toISOString()
        })
        .eq('id', itemId);
      
      if (error) throw error;
    } catch (error) {
      console.error(`Error updating status for item ${itemId}:`, error);
      throw error;
    }
  }

  /**
   * Update item with error information
   */
  async updateItemWithError(
    itemId: string,
    option: number,
    step: string,
    errorMessage: string,
    retryCount: number
  ): Promise<void> {
    const errorData = {
      option_failed: option,
      step_failed: step,
      error: errorMessage,
      timestamp: new Date().toISOString()
    };

    // Use only valid status values
    const status = retryCount >= CONFIG.RETRY_LIMIT ? 'Document_introuvable' : 'En_attente';

    try {
      // First log the error to the error_log table
      await this.logError('stagehand_extraction', `Item ${itemId}: ${errorMessage}`);
      
      // Then update the item status
      const { error } = await this.supabase
        .from('extraction_queue')
        .update({
          status: status,
          error_message: JSON.stringify(errorData),
          retry_count: retryCount,
          updated_at: new Date().toISOString()
        })
        .eq('id', itemId);

      if (error) {
        console.error('Database error updating item:', error);
        // Log this error too but don't throw - continue processing
        await this.logError('stagehand_extraction_db_error', `Failed to update item ${itemId}: ${JSON.stringify(error)}`);
      } else {
        console.log(`Successfully updated item ${itemId} with status ${status} and retry count ${retryCount}`);
      }
    } catch (error) {
      console.error('Failed to update item with error - continuing processing:', error);
      // Log this error but continue processing
      await this.logError('stagehand_extraction_critical', `Critical error updating item ${itemId}: ${JSON.stringify(error)}`);
    }
  }

  /**
   * Mark item as successfully downloaded
   */
  async markItemAsDownloaded(
    itemId: string,
    localFilePath: string
  ): Promise<void> {
    await this.updateItemStatus(itemId, {
      status: 'Telecharge',
      local_file_path: localFilePath,
      error_message: undefined // Clear any previous errors
    });
  }

  /**
   * Get batch of items for processing
   */
  async getItemsForProcessing(limit: number = CONFIG.BATCH_SIZE): Promise<ExtractionQueueItem[]> {
    try {
      const { data, error } = await this.supabase
        .from('extraction_queue')
        .select('*')
        .eq('status', 'En_attente')
        .order('created_at', { ascending: true })
        .limit(limit);
      
      if (error) throw error;
      
      return (data || []) as ExtractionQueueItem[];
    } catch (error) {
      console.error('Error fetching items for processing:', error);
      throw error;
    }
  }

  /**
   * Log error to error_log table
   */
  async logError(flowName: string, error: string): Promise<void> {
    try {
      const { error: insertError } = await this.supabase
        .from('error_log')
        .insert({
          flow_name: flowName,
          error: error,
          created_at: new Date().toISOString()
        });
      
      if (insertError) {
        console.error('Failed to log error to error_log table:', insertError);
      } else {
        console.log(`Logged error to database: ${flowName} - ${error.substring(0, 100)}...`);
      }
    } catch (err) {
      console.error('Error logging to error_log table:', err);
    }
  }
}
