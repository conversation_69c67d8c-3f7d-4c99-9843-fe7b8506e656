#!/usr/bin/env ts-node

/**
 * Simple runner script for the extraction processor
 */

import { config as dotenvConfig } from 'dotenv';
import { ExtractionProcessor } from './extraction-processor';

// Load environment variables
dotenvConfig();

async function runExtraction() {
  console.log('🚀 Starting Stage Hand Document Extraction...');
  console.log(`📁 Downloads will be saved to: ${process.env.DOWNLOAD_DIR || './downloads'}`);
  console.log(`🔧 Mode: ${process.env.NODE_ENV || 'development'}`);
  console.log(`🐛 Debug: ${process.env.DEBUG === 'true' ? 'Enabled' : 'Disabled'}`);
  console.log('');

  const processor = new ExtractionProcessor();

  try {
    await processor.run();
    console.log('✅ Extraction process completed successfully!');
    process.exit(0);
  } catch (error) {
    console.error('❌ Extraction process failed:', error);
    process.exit(1);
  }
}

// Handle process signals
process.on('SIGINT', () => {
  console.log('\n🛑 Received SIGINT, shutting down gracefully...');
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('\n🛑 Received SIGTERM, shutting down gracefully...');
  process.exit(0);
});

// Run extraction
runExtraction().catch((error) => {
  console.error('💥 Fatal error:', error);
  process.exit(1);
});
