#!/usr/bin/env node

import { ServiceRegistry } from './services/service-registry.js';
import { QueueManager } from './services/queue-manager.js';
import { DocumentDiscoveryService } from './services/document-discovery-service.js';
import { ExtractionEngineService } from './services/extraction-engine-service.js';
import { ValidationService } from './services/validation-service.js';
import { AnalyticsService } from './services/analytics-service.js';
import { FieldMapperService } from './services/field-mapper-service.js';
import { RetryManager } from './services/retry-manager.js';
import { MLDocumentDiscoveryService } from './services/ml-document-discovery.js';
import { PredictiveAnalyticsService } from './services/predictive-analytics.js';
import { AlertManager } from './services/alert-manager.js';
import { WorkerPool } from './services/worker-pool.js';
import { CacheManager } from './services/cache-manager.js';
import { SessionManager } from './services/session-manager.js';
import { DebuggingService } from './services/debugging-service.js';
import { SuggestionService } from './services/suggestion-service.js';
import { DocumentEnhancementService } from './services/document-enhancement-service.js';
import { createClient } from '@supabase/supabase-js';
import { ServiceConfig } from './services/base-service'; // Import ServiceConfig
import { ErrorLogger } from './utils/error-logger.js'; // Import ErrorLogger
// Removed duplicate import: import { NotificationService } from './services/notification-service.js';

// Mock Supabase client for testing
const mockSupabase = createClient('https://mock.supabase.co', 'mock-key');

// Mock ServiceConfig for direct instantiation of services
const mockServiceConfig: ServiceConfig = {
  name: 'mockService',
  dependencies: [],
};

interface TestResult {
  service: string;
  passed: boolean;
  message: string;
  duration: number;
}

class FullImplementationTest {
  private registry: ServiceRegistry;
  private results: TestResult[] = [];

  constructor() {
    this.registry = new ServiceRegistry();
  }

  private async runTest(testName: string, testFn: () => Promise<void>): Promise<void> {
    const startTime = Date.now();
    try {
      await testFn();
      const duration = Date.now() - startTime;
      this.results.push({
        service: testName,
        passed: true,
        message: 'Test passed successfully',
        duration
      });
      console.log(`✅ ${testName} - PASSED (${duration}ms)`);
    } catch (error) {
      const duration = Date.now() - startTime;
      this.results.push({
        service: testName,
        passed: false,
        message: error instanceof Error ? error.message : 'Unknown error',
        duration
      });
      console.log(`❌ ${testName} - FAILED (${duration}ms): ${error}`);
    }
  }

  async testServiceRegistry(): Promise<void> {
    await this.runTest('ServiceRegistry', async () => {
      // Test service registration
      this.registry.register('queueManager', (config) => new QueueManager(config), mockServiceConfig);
      
      const retrieved = this.registry.get('queueManager');
      if (!retrieved) throw new Error('Failed to retrieve registered service');
      
      // Test dependency management
      this.registry.register('analytics', (config) => new AnalyticsService(config, mockSupabase), mockServiceConfig);
      
      // Test health check
      const health = await this.registry.getSystemHealth();
      if (health.overall !== 'healthy') throw new Error('Registry health check failed');
    });
  }

  async testPriorityQueue(): Promise<void> {
    await this.runTest('PriorityQueue', async () => {
      const queueManager = new QueueManager(mockServiceConfig);
      await queueManager.initialize();

      // Test Index priority over Acte
      await queueManager.enqueue({
        id: 'test-acte-1',
        type: 'Acte',
        searchParams: {},
        priority: 100,
        timestamp: new Date()
      });

      await queueManager.enqueue({
        id: 'test-index-1',
        type: 'Index',
        searchParams: {},
        priority: 1000,
        timestamp: new Date()
      });

      const nextItem = await queueManager.dequeue();
      if (!nextItem || nextItem.type !== 'Index') {
        throw new Error('Index document not prioritized correctly');
      }

      await queueManager.shutdown();
    });
  }

  async testFieldMapping(): Promise<void> {
    await this.runTest('FieldMapping', async () => {
      const fieldMapper = new FieldMapperService(mockServiceConfig, mockSupabase);
      await fieldMapper.initialize();

      // Test field detection
      const mockFormHtml = `
        <form>
          <input type="text" name="nom_vendeur" id="seller_name" />
          <input type="date" name="date_debut" id="start_date" />
          <select name="lieu" id="location">
            <option value="paris">Paris</option>
          </select>
          <button type="submit">Search</button>
        </form>
      `;

      const fields = await fieldMapper.detectPageFields(mockFormHtml, 'https://test.com');
      if (fields.length < 4) {
        throw new Error('Failed to detect all form fields');
      }

      // Test mapping generation
      const mapping = await fieldMapper.generateFieldMapping(fields, 'Index');
      if (!mapping || Object.keys(mapping).length === 0) {
        throw new Error('Failed to generate field mapping');
      }

      await fieldMapper.shutdown();
    });
  }

  async testRetryLogic(): Promise<void> {
    await this.runTest('RetryLogic', async () => {
      const retryManager = new RetryManager(mockServiceConfig, mockSupabase);
      await retryManager.initialize();

      let attemptCount = 0;
      const testOperation = async () => {
        attemptCount++;
        if (attemptCount < 3) {
          throw new Error('Simulated failure');
        }
        return 'success';
      };

      const result = await retryManager.executeWithRetry(
        'test-operation',
        testOperation,
        { maxAttempts: 3, backoffMs: 100 }
      );

      if (result !== 'success' || attemptCount !== 3) {
        throw new Error('Retry logic not working correctly');
      }

      await retryManager.shutdown();
    });
  }

  async testMLDocumentDiscovery(): Promise<void> {
    await this.runTest('MLDocumentDiscovery', async () => {
      const mlService = new MLDocumentDiscoveryService(mockServiceConfig);
      await mlService.initialize();

      // Test prediction
      const prediction = await mlService.predictDocument({
        documentSource: 'index',
        documentNumber: 'TEST',
        circonscriptionFonciere: 'Paris'
      });

      if (typeof prediction.confidence !== 'number' || 
          prediction.confidence < 0 || 
          prediction.confidence > 1) {
        throw new Error('Invalid prediction confidence');
      }

      await mlService.shutdown();
    });
  }

  async testPredictiveAnalytics(): Promise<void> {
    await this.runTest('PredictiveAnalytics', async () => {
      const analytics = new PredictiveAnalyticsService(mockServiceConfig);
      await analytics.initialize();

      // Test processing time prediction
      const timePrediction = await analytics.predictProcessingTime(
        'index',
        'Paris',
        'medium'
      );

      if (typeof timePrediction.estimatedTime !== 'number') {
        throw new Error('Invalid processing time prediction');
      }

      // Test bottleneck detection
      const bottlenecks = await analytics.predictBottlenecks();
      if (!Array.isArray(bottlenecks)) {
        throw new Error('Bottleneck detection failed');
      }

      await analytics.shutdown();
    });
  }

  async testAlertSystem(): Promise<void> {
    await this.runTest('AlertSystem', async () => {
      const errorLogger = new ErrorLogger(mockSupabase);
      const notificationService = new NotificationService(mockServiceConfig, mockSupabase);
      const alertManager = new AlertManager(mockServiceConfig, mockSupabase, errorLogger, notificationService);
      await alertManager.initialize();

      // Test alert creation
      const alertId = await alertManager.createAlert({
        severity: 'high',
        title: 'Test Alert',
        description: 'Testing alert system',
        source: 'test-service',
        metadata: { test: true },
        message: 'This is a test alert message.', // Added message
        context: { testContext: 'value' } // Added context
      });

      if (!alertId) {
        throw new Error('Failed to create alert');
      }

      // Test alert retrieval
      const alert = await alertManager.getAlert(alertId);
      if (!alert || alert.title !== 'Test Alert') {
        throw new Error('Failed to retrieve alert');
      }

      await alertManager.shutdown();
    });
  }

  async testWorkerPool(): Promise<void> {
    await this.runTest('WorkerPool', async () => {
      const workerPool = new WorkerPool();
      await workerPool.initialize();

      // Test worker scaling
      const initialWorkers = workerPool.getWorkerCount();
      
      // Add some tasks to trigger scaling
      const tasks = [];
      for (let i = 0; i < 5; i++) {
        tasks.push(workerPool.executeTask(async () => {
          await new Promise(resolve => setTimeout(resolve, 100));
          return `Task ${i} completed`;
        }));
      }

      const results = await Promise.all(tasks);
      if (results.length !== 5) {
        throw new Error('Not all tasks completed');
      }

      await workerPool.shutdown();
    });
  }

  async testCaching(): Promise<void> {
    await this.runTest('Caching', async () => {
      const cacheManager = new CacheManager();
      await cacheManager.initialize();

      // Test cache operations
      await cacheManager.set('test-key', 'test-value', 1000);
      const value = await cacheManager.get('test-key');
      
      if (value !== 'test-value') {
        throw new Error('Cache get/set failed');
      }

      // Test cache stats
      const stats = await cacheManager.getStats();
      if (typeof stats.hitRate !== 'number') {
        throw new Error('Cache stats not working');
      }

      await cacheManager.shutdown();
    });
  }

  async testSessionManagement(): Promise<void> {
    await this.runTest('SessionManagement', async () => {
      const sessionManager = new SessionManager();
      await sessionManager.initialize();

      // Test session creation
      const sessionId = await sessionManager.createSession();
      if (!sessionId) {
        throw new Error('Failed to create session');
      }

      // Test session validation
      const isValid = await sessionManager.validateSession(sessionId);
      if (!isValid) {
        throw new Error('Session validation failed');
      }

      await sessionManager.shutdown();
    });
  }

  async testDebugging(): Promise<void> {
    await this.runTest('Debugging', async () => {
      const debugService = new DebuggingService();
      await debugService.initialize();

      // Test debug session creation
      const sessionId = await debugService.startDebugSession(
        'Index',
        { test: true },
        async (action) => {
          console.log('Intervention requested:', action.type);
        }
      );

      if (!sessionId) {
        throw new Error('Failed to create debug session');
      }

      // Test session retrieval
      const session = debugService.getDebugSession(sessionId);
      if (!session || session.documentType !== 'Index') {
        throw new Error('Failed to retrieve debug session');
      }

      await debugService.endDebugSession(sessionId, 'completed');
      await debugService.shutdown();
    });
  }

  async testSuggestions(): Promise<void> {
    await this.runTest('Suggestions', async () => {
      const suggestionService = new SuggestionService(mockSupabase);
      await suggestionService.initialize();

      // Test suggestion generation
      const suggestions = await suggestionService.generateSuggestions({
        documentType: 'Index',
        searchParams: {
          date_debut: '2020-01-01',
          date_fin: '2023-12-31'
        },
        extractionResult: { success: false },
        processingTime: 350000
      });

      if (!Array.isArray(suggestions) || suggestions.length === 0) {
        throw new Error('Failed to generate suggestions');
      }

      // Test suggestion application
      const applied = await suggestionService.applySuggestion(suggestions[0].id, true);
      if (!applied) {
        throw new Error('Failed to apply suggestion');
      }

      await suggestionService.shutdown();
    });
  }

  async testDocumentEnhancement(): Promise<void> {
    await this.runTest('DocumentEnhancement', async () => {
      const enhancementService = new DocumentEnhancementService();
      await enhancementService.initialize();

      // Test document number extraction
      const documentNumber = await enhancementService.extractDocumentNumber('Index', {
        filename: 'Index_2023-12-01-12345.pdf',
        url: 'https://example.com/doc/12345',
        pageContent: 'Document number: 2023-12-01-12345'
      });

      if (!documentNumber) {
        throw new Error('Failed to extract document number');
      }

      // Test filename generation
      const namingResult = await enhancementService.generateFilename('Index', {
        filename: 'old_document.pdf',
        pageContent: 'Document: 2023-12-01-67890'
      });

      if (!namingResult.success || !namingResult.finalName.includes('67890')) {
        throw new Error('Failed to generate proper filename');
      }

      await enhancementService.shutdown();
    });
  }

  async testIntegration(): Promise<void> {
    await this.runTest('Integration', async () => {
      // Test full workflow integration
      const queueManager = new QueueManager();
      const analytics = new AnalyticsService(mockSupabase);
      const fieldMapper = new FieldMapperService(mockSupabase);
      
      await queueManager.initialize();
      await analytics.initialize();
      await fieldMapper.initialize();

      // Create a complete extraction request
      const extractionItem = {
        id: 'integration-test-1',
        type: 'Index' as const,
        searchParams: {
          nom_vendeur: 'TEST VENDOR',
          date_debut: '2023-01-01',
          date_fin: '2023-12-31',
          lieu: 'Paris'
        },
        priority: 1000,
        timestamp: new Date()
      };

      // Test queue processing
      await queueManager.enqueue(extractionItem);
      const dequeuedItem = await queueManager.dequeue();
      
      if (!dequeuedItem || dequeuedItem.id !== extractionItem.id) {
        throw new Error('Queue integration failed');
      }

      // Test analytics recording
      await analytics.recordExecution({
        id: extractionItem.id,
        documentType: extractionItem.type,
        startTime: new Date(),
        endTime: new Date(Date.now() + 5000),
        success: true,
        documentsFound: 1,
        processingTimeMs: 5000
      });

      const stats = await analytics.getStats();
      if (stats.totalExecutions === 0) {
        throw new Error('Analytics integration failed');
      }

      await queueManager.shutdown();
      await analytics.shutdown();
      await fieldMapper.shutdown();
    });
  }

  async runAllTests(): Promise<void> {
    console.log('\n🚀 Starting Full Implementation Test Suite\n');
    console.log('='.repeat(50));

    // Core Foundation Tests
    console.log('\n📋 Phase 1: Foundation Tests');
    await this.testServiceRegistry();
    await this.testPriorityQueue();
    await this.testFieldMapping();
    await this.testRetryLogic();

    // Intelligence Tests
    console.log('\n🧠 Phase 2: Intelligence Tests');
    await this.testMLDocumentDiscovery();
    await this.testPredictiveAnalytics();
    await this.testAlertSystem();

    // Performance Tests
    console.log('\n⚡ Phase 3: Performance Tests');
    await this.testWorkerPool();
    await this.testCaching();
    await this.testSessionManagement();

    // User Experience Tests
    console.log('\n👤 Phase 4: User Experience Tests');
    await this.testDebugging();
    await this.testSuggestions();
    await this.testDocumentEnhancement();

    // Integration Test
    console.log('\n🔗 Integration Tests');
    await this.testIntegration();

    this.printResults();
  }

  private printResults(): void {
    console.log('\n' + '='.repeat(50));
    console.log('🎯 TEST RESULTS SUMMARY');
    console.log('='.repeat(50));

    const passed = this.results.filter(r => r.passed).length;
    const failed = this.results.filter(r => !r.passed).length;
    const total = this.results.length;

    console.log(`\n📊 Overall Results: ${passed}/${total} tests passed`);
    console.log(`✅ Passed: ${passed}`);
    console.log(`❌ Failed: ${failed}`);
    console.log(`📈 Success Rate: ${((passed / total) * 100).toFixed(1)}%`);

    if (failed > 0) {
      console.log('\n❌ Failed Tests:');
      this.results
        .filter(r => !r.passed)
        .forEach(r => {
          console.log(`   - ${r.service}: ${r.message}`);
        });
    }

    console.log('\n⏱️  Performance Summary:');
    const totalTime = this.results.reduce((sum, r) => sum + r.duration, 0);
    const avgTime = totalTime / this.results.length;
    console.log(`   - Total Test Time: ${totalTime}ms`);
    console.log(`   - Average Test Time: ${avgTime.toFixed(1)}ms`);
    console.log(`   - Fastest Test: ${Math.min(...this.results.map(r => r.duration))}ms`);
    console.log(`   - Slowest Test: ${Math.max(...this.results.map(r => r.duration))}ms`);

    console.log('\n🎉 Full Implementation Test Suite Complete!');
    
    if (passed === total) {
      console.log('🌟 ALL TESTS PASSED - System ready for production! 🌟');
    } else {
      console.log('⚠️  Some tests failed - Review implementation before deployment');
    }
  }
}

// Run the test suite
const testSuite = new FullImplementationTest();
testSuite.runAllTests().catch(console.error);
