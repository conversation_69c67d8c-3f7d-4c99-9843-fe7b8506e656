#!/usr/bin/env ts-node

/**
 * Test script for the improved print button detection
 * This script tests the robust print button detection without running the full extraction pipeline
 */

import { Stagehand } from '@browserbasehq/stagehand';
import { CONFIG } from './config';
import { StagehandPage } from './types';

class PrintButtonTester {
  private stagehand: Stagehand;

  constructor() {
    this.stagehand = new Stagehand({
      env: 'LOCAL', // Force local for testing
      modelName: CONFIG.MODEL_NAME as any,
      modelClientOptions: CONFIG.MODEL_API_KEY ? { apiKey: CONFIG.MODEL_API_KEY } : undefined,
      enableCaching: false, // Disable caching for testing
      selfHeal: CONFIG.SELF_HEAL,
      verbose: 2, // Maximum verbosity for testing
      domSettleTimeoutMs: CONFIG.DOM_SETTLE_TIMEOUT,
      localBrowserLaunchOptions: {
        headless: false, // Show browser for testing
        downloadsPath: CONFIG.BASE_DOWNLOAD_DIR,
        viewport: { width: 1280, height: 720 }
      }
    });
  }

  async testPrintButtonDetection(): Promise<void> {
    console.log('🧪 Starting print button detection test...');
    
    try {
      // Initialize Stagehand
      await this.stagehand.init();
      const page = this.stagehand.page as unknown as StagehandPage;
      
      // Login to the website
      console.log('🔐 Logging in...');
      await this.login(page);
      
      // Navigate to a test document page
      console.log('🔍 Navigating to test document...');
      await this.navigateToTestDocument(page);
      
      // Test the robust print button detection
      console.log('🎯 Testing robust print button detection...');
      await this.testRobustPrintButtonDetection(page);
      
      console.log('✅ Print button detection test completed successfully!');
      
    } catch (error) {
      console.error('❌ Print button detection test failed:', error);
      throw error;
    } finally {
      await this.stagehand.close();
    }
  }

  private async login(page: StagehandPage): Promise<void> {
    try {
      // Navigate to the main page first
      await page.goto(CONFIG.BASE_URL);
      await page.waitForTimeout(1000);
      
      // Click on the first 'Entrée du site' link
      await page.act("click on the link with href '/Sirf/pf_acces.asp'");
      await page.waitForTimeout(2000);
      
      // Fill the login form
      await page.act(`type '${CONFIG.USER_CODE}' in the 'Code d'utilisateur' input field`);
      await page.act(`type '${CONFIG.PASSWORD}' in the 'Mot de passe' input field`);
      await page.act("click the 'Soumettre' button");
      
      // Wait for login to complete
      await page.waitForTimeout(3000);
      
      console.log('✅ Login successful');
    } catch (error) {
      console.error('❌ Login error:', error);
      throw error;
    }
  }

  private async navigateToTestDocument(page: StagehandPage): Promise<void> {
    try {
      // Navigate to Acte search page
      await page.goto(CONFIG.ACTE_SEARCH_URL);
      await page.waitForTimeout(2000);
      
      // Fill in a test inscription number (you may need to adjust this)
      const testInscriptionNumber = '123456'; // Replace with a known working number
      
      // Select Acte type
      await page.act("click on the radio button with id 'radAct'");
      await page.waitForTimeout(500);
      
      // Fill inscription number
      await page.act(`click on the 'Numéro d'inscription' text field and fill it with '${testInscriptionNumber}'`);
      await page.waitForTimeout(1000);
      
      // Submit the form
      await page.act("click the 'Rechercher' button");
      await page.waitForTimeout(5000);
      
      console.log('✅ Navigated to test document page');
    } catch (error) {
      console.error('❌ Navigation error:', error);
      throw error;
    }
  }

  private async testRobustPrintButtonDetection(page: StagehandPage): Promise<void> {
    try {
      // Import the robust print button detection method from the main processor
      const { z } = await import('zod');
      
      console.log('🎯 Starting robust print button detection...');
      
      // Wait for frames to fully load
      await page.waitForTimeout(5000);
      
      // Strategy 1: Frame-aware extraction and navigation (most reliable)
      try {
        console.log('📄 Test Strategy 1: Frame-aware extraction and navigation...');
        
        // First, extract frame structure to understand the page layout
        const frameAnalysis = await page.extract({
          instruction: "Analyze the page structure and identify all frames, particularly looking for 'page' frame and 'frmNavgt' frame. Also check if there's an 'Imprimer' link visible anywhere.",
          schema: z.object({
            hasPageFrame: z.boolean(),
            hasNavFrame: z.boolean(),
            frameStructure: z.string().optional(),
            imprimerVisible: z.boolean(),
            imprimerLocation: z.string().optional()
          })
        });
        
        console.log('Frame analysis result:', frameAnalysis);
        
        if (frameAnalysis.hasPageFrame && frameAnalysis.hasNavFrame) {
          // Use the known working pattern from Playwright scripts
          await page.act("wait for the frame named 'page' to be fully loaded");
          await page.waitForTimeout(2000);
          
          await page.act("within the frame named 'page', wait for the nested frame named 'frmNavgt' to be available");
          await page.waitForTimeout(2000);
          
          await page.act("in the frame 'page', then in its nested frame 'frmNavgt', click the link with text 'Imprimer'");
          console.log('✅ Successfully clicked print button using frame-aware navigation');
          return;
        }
      } catch (error) {
        console.log('❌ Test Strategy 1 failed:', error instanceof Error ? error.message : String(error));
      }
      
      // If we get here, the print button detection failed
      console.log('⚠️ Print button detection test completed - button not found or not clickable');
      
    } catch (error) {
      console.error('❌ Robust print button detection test failed:', error);
      throw error;
    }
  }
}

// Run the test if this file is executed directly
if (require.main === module) {
  const tester = new PrintButtonTester();
  tester.testPrintButtonDetection()
    .then(() => {
      console.log('🎉 Test completed successfully!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Test failed:', error);
      process.exit(1);
    });
}

export { PrintButtonTester };
