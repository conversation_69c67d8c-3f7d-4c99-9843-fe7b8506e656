#!/usr/bin/env ts-node

/**
 * Test script to verify session management and download functionality
 */

import { ExtractionProcessor } from './extraction-processor';
import { CONFIG } from './config';
import { promises as fs } from 'fs';
import * as path from 'path';

class SessionAndDownloadTester {
  private processor: ExtractionProcessor;

  constructor() {
    this.processor = new ExtractionProcessor();
  }

  async testSessionAndDownloads(): Promise<void> {
    console.log('🧪 Testing session management and download functionality...');
    console.log(`📁 Download directory: ${CONFIG.BASE_DOWNLOAD_DIR}`);
    
    try {
      // Check if download directory exists and create if needed
      await this.ensureDownloadDirectory();
      
      // List current queue items
      await this.listQueueItems();
      
      // Run the extraction processor
      console.log('🚀 Starting extraction processor...');
      await this.processor.run();
      
      // Check download results
      await this.checkDownloadResults();
      
    } catch (error) {
      console.error('❌ Test failed:', error);
      throw error;
    }
  }

  private async ensureDownloadDirectory(): Promise<void> {
    try {
      await fs.mkdir(CONFIG.BASE_DOWNLOAD_DIR, { recursive: true });
      console.log(`✅ Download directory ensured: ${CONFIG.BASE_DOWNLOAD_DIR}`);
      
      // List existing files
      const files = await fs.readdir(CONFIG.BASE_DOWNLOAD_DIR);
      console.log(`📂 Existing files in download directory: ${files.length > 0 ? files.join(', ') : 'none'}`);
    } catch (error) {
      console.error('❌ Error ensuring download directory:', error);
      throw error;
    }
  }

  private async listQueueItems(): Promise<void> {
    try {
      const { SupabaseService } = await import('./supabase-client');
      const supabase = new SupabaseService();
      
      // Get pending items
      const pendingItem = await supabase.getNextEnAttenteItem();
      if (pendingItem) {
        console.log(`📋 Found pending item: ${pendingItem.id} (${pendingItem.document_source}: ${pendingItem.document_number})`);
      } else {
        console.log('📋 No pending items found in queue');
      }
    } catch (error) {
      console.error('❌ Error checking queue:', error);
    }
  }

  private async checkDownloadResults(): Promise<void> {
    try {
      console.log('🔍 Checking download results...');
      
      // Check main download directory
      const files = await fs.readdir(CONFIG.BASE_DOWNLOAD_DIR);
      console.log(`📂 Files in main download directory: ${files.length > 0 ? files.join(', ') : 'none'}`);
      
      // Check subdirectories
      for (const file of files) {
        const filePath = path.join(CONFIG.BASE_DOWNLOAD_DIR, file);
        const stats = await fs.stat(filePath);
        
        if (stats.isDirectory()) {
          console.log(`📁 Checking subdirectory: ${file}`);
          const subFiles = await fs.readdir(filePath);
          console.log(`  📄 Files: ${subFiles.length > 0 ? subFiles.join(', ') : 'none'}`);
          
          // Check file sizes
          for (const subFile of subFiles) {
            if (subFile.endsWith('.pdf')) {
              const subFilePath = path.join(filePath, subFile);
              const subStats = await fs.stat(subFilePath);
              console.log(`  📊 ${subFile}: ${subStats.size} bytes`);
            }
          }
        } else if (file.endsWith('.pdf')) {
          console.log(`📊 ${file}: ${stats.size} bytes`);
        }
      }
    } catch (error) {
      console.error('❌ Error checking download results:', error);
    }
  }
}

// Run the test if this file is executed directly
if (require.main === module) {
  const tester = new SessionAndDownloadTester();
  tester.testSessionAndDownloads()
    .then(() => {
      console.log('🎉 Session and download test completed!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Session and download test failed:', error);
      process.exit(1);
    });
}

export { SessionAndDownloadTester };
