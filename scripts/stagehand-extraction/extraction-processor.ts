import { Stagehand } from '@browserbasehq/stagehand';
import { z } from 'zod';
import { CONFIG, ExtractionQueueItem, ActeSearchParams, IndexSearchParams } from './config';
import { SupabaseService } from './supabase-client';
import { FileManager } from './utils/file-manager';
import { ErrorLogger } from './utils/error-logger';
import { ExactMatchStrategy } from './strategies/exact-match';
import { IntelligentMatchStrategy } from './strategies/intelligent-match';
import { ExploratoryStrategy } from './strategies/exploratory';
import { StagehandPage, ExtractionResult } from './types';
import { cleanupCacheLock } from './utils/cleanup';

export class ExtractionProcessor {
  private stagehand: Stagehand;
  private supabaseService: SupabaseService;
  private fileManager: FileManager;
  private errorLogger: ErrorLogger;
  private exactMatchStrategy: ExactMatchStrategy;
  private intelligentMatchStrategy: IntelligentMatchStrategy;
  private exploratoryStrategy: ExploratoryStrategy;
  private processedCount: number = 0;

  constructor() {
    this.supabaseService = new SupabaseService();
    this.fileManager = new FileManager();
    this.errorLogger = new ErrorLogger(this.supabaseService);
    this.exactMatchStrategy = new ExactMatchStrategy(this.errorLogger);
    this.intelligentMatchStrategy = new IntelligentMatchStrategy(this.errorLogger);
    this.exploratoryStrategy = new ExploratoryStrategy(this.errorLogger);

    // Initialize Stagehand
    this.stagehand = new Stagehand({
      env: CONFIG.HEADLESS ? 'BROWSERBASE' : 'LOCAL',
      modelName: CONFIG.MODEL_NAME as any,
      modelClientOptions: CONFIG.MODEL_API_KEY ? { apiKey: CONFIG.MODEL_API_KEY } : undefined,
      enableCaching: CONFIG.ENABLE_CACHING,
      selfHeal: CONFIG.SELF_HEAL,
      verbose: (CONFIG.VERBOSE as 0 | 1 | 2) || 1,
      domSettleTimeoutMs: CONFIG.DOM_SETTLE_TIMEOUT,
      localBrowserLaunchOptions: {
        headless: CONFIG.HEADLESS,
        downloadsPath: CONFIG.BASE_DOWNLOAD_DIR,
        viewport: { width: 1280, height: 720 }
      }
    });
  }

  /**
   * Main run method
   */
  async run(): Promise<void> {
    console.log('Starting Stage Hand extraction processor...');
    
    // Clean up any stale cache lock files
    await cleanupCacheLock();
    
    try {
      // Initialize Stage Hand
      await this.stagehand.init();
      const page = this.stagehand.page as unknown as StagehandPage;
      
      // Ensure download directories exist
      await this.fileManager.ensureDownloadDirectory();
      await this.fileManager.ensureDownloadDirectory('screenshots');
      
      // Login once
      console.log('Logging in to Registre Foncier...');
      await this.login(page);
      
      // Process queue items
      let keepProcessing = true;
      
      while (keepProcessing) {
        const queueItem = await this.supabaseService.getNextEnAttenteItem();
        
        if (!queueItem) {
          console.log('No more pending items to process.');
          break;
        }
        
        console.log(`Processing item ${queueItem.id} (${queueItem.document_source}: ${queueItem.document_number})...`);
        
        // Update status to En_traitement
        await this.supabaseService.updateItemStatus(queueItem.id, {
          status: 'En_traitement',
          processing_started_at: new Date().toISOString()
        });
        
        try {
          // Process the item
          const result = await this.processItem(page, queueItem);
          
          if (result.success && result.filePath) {
            await this.supabaseService.markItemAsDownloaded(queueItem.id, result.filePath);
            console.log(`Successfully downloaded document for item ${queueItem.id}`);
          } else {
            throw new Error(result.error || 'Failed to download document');
          }
          
          this.processedCount++;
          
          if (this.processedCount >= CONFIG.BATCH_SIZE) {
            console.log('Reached batch limit. Stopping processing.');
            keepProcessing = false;
          }
          
          // Add delay between items
          await page.waitForTimeout(CONFIG.ACTION_DELAY + Math.random() * 3000);
          
        } catch (error) {
          await this.handleItemError(queueItem, error);
        }
      }
      
    } catch (error) {
      console.error('Critical error in extraction processor:', error);
      throw error;
    } finally {
      await this.stagehand.close();
      console.log('Stage Hand closed. Extraction batch complete.');
    }
  }

  /**
   * Login to the website (optimized for speed)
   */
  private async login(page: StagehandPage): Promise<void> {
    try {
      // Navigate to the main page first
      await page.goto(CONFIG.BASE_URL);
      await page.waitForTimeout(1000);
      
      // Click on the first 'Entrée du site' link (element 0)
      await page.act("click on the link with href '/Sirf/pf_acces.asp'");
      await page.waitForTimeout(2000);
      
      // Now we should be on the login page - fill the form
      await page.act(`type '${CONFIG.USER_CODE}' in the 'Code d'utilisateur' input field`);
      await page.act(`type '${CONFIG.PASSWORD}' in the 'Mot de passe' input field`);
      await page.act("click the 'Soumettre' button");
      
      // Wait for login to complete
      await page.waitForTimeout(3000);
      
      console.log('Login successful');
    } catch (error) {
      console.error('Login error:', error);
      throw error;
    }
  }

  /**
   * Process a single queue item
   */
  private async processItem(
    page: StagehandPage,
    item: ExtractionQueueItem
  ): Promise<ExtractionResult> {
    const itemDownloadDir = await this.fileManager.ensureDownloadDirectory(item.id);
    
    try {
      // Navigate to appropriate search page using direct URLs
      if (item.document_source === 'acte') {
        console.log('Navigating to Acte search page...');
        await page.goto(CONFIG.ACTE_SEARCH_URL);
        await page.waitForTimeout(2000);
        return await this.extractActe(page, item, itemDownloadDir);
      } else {
        console.log('Navigating to Index search page...');
        await page.goto(CONFIG.INDEX_SEARCH_URL);
        await page.waitForTimeout(2000);
        return await this.extractIndex(page, item, itemDownloadDir);
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error),
        failedStrategyAttempt: 0 // General error, not tied to a specific strategy attempt
      };
    }
  }

  /**
   * Extract an Acte document using the three-path strategy
   */
  private async extractActe(
    page: StagehandPage,
    item: ExtractionQueueItem,
    downloadDir: string
  ): Promise<ExtractionResult> {
    const params: ActeSearchParams = {
      inscriptionNumber: item.document_number,
      circonscription: item.circonscription_fonciere,
      acteType: item.acte_type
    };

    let failedStrategyAttempt = 0;

    // Try Option 1: Exact Match
    try {
      console.log(`[Option 1] Attempting exact match for ${item.document_number}`);
      await this.exactMatchStrategy.executeActeSearch(page, params, item.id);
      await page.waitForTimeout(3000);
      
      if (await this.exactMatchStrategy.verifyResultsPage(page, item.document_number)) {
        return await this.downloadDocument(page, item.document_number, downloadDir);
      }
      failedStrategyAttempt = 1;
    } catch (error) {
      const errorLog = this.errorLogger.createErrorLog(
        item.id, item.document_number, 1, 'exact_match', error instanceof Error ? error : new Error(String(error))
      );
      await this.errorLogger.logError(errorLog, page);
      failedStrategyAttempt = 1;
    }

    // Try Option 2: Intelligent Match
    try {
      console.log(`[Option 2] Attempting intelligent match for ${item.document_number}`);
      await page.goto(CONFIG.ACTE_SEARCH_URL); // Reset to search page
      await page.waitForTimeout(2000);
      await this.intelligentMatchStrategy.executeActeSearch(page, params, item.id);
      await page.waitForTimeout(3000);
      
      // Check if we got results
      const hasResults = await this.checkForResults(page);
      if (hasResults) {
        return await this.downloadDocument(page, item.document_number, downloadDir);
      }
      failedStrategyAttempt = 2;
    } catch (error) {
      const errorLog = this.errorLogger.createErrorLog(
        item.id, item.document_number, 2, 'intelligent_match', error instanceof Error ? error : new Error(String(error))
      );
      await this.errorLogger.logError(errorLog, page);
      failedStrategyAttempt = 2;
    }

    // Try Option 3: Exploratory Mode
    try {
      console.log(`[Option 3] Attempting exploratory mode for ${item.document_number}`);
      await page.goto(CONFIG.ACTE_SEARCH_URL); // Reset to search page
      await page.waitForTimeout(2000);
      await this.exploratoryStrategy.executeActeSearch(page, params, item.id);
      await page.waitForTimeout(3000);
      
      const hasResults = await this.checkForResults(page);
      if (hasResults) {
        return await this.downloadDocument(page, item.document_number, downloadDir);
      }
      failedStrategyAttempt = 3;
    } catch (error) {
      const errorLog = this.errorLogger.createErrorLog(
        item.id, item.document_number, 3, 'exploratory_mode', error instanceof Error ? error : new Error(String(error))
      );
      await this.errorLogger.logError(errorLog, page);
      failedStrategyAttempt = 3;
    }

    return {
      success: false,
      error: 'Document not found after trying all strategies',
      failedStrategyAttempt
    };
  }

  /**
   * Extract an Index document using the three-path strategy
   */
  private async extractIndex(
    page: StagehandPage,
    item: ExtractionQueueItem,
    downloadDir: string
  ): Promise<ExtractionResult> {
    const params: IndexSearchParams = {
      lotNumber: item.document_number,
      circonscription: item.circonscription_fonciere,
      cadastre: item.cadastre,
      designationSecondaire: item.designation_secondaire
    };

    let failedStrategyAttempt = 0;

    // Try Option 1: Exact Match
    try {
      console.log(`[Option 1] Attempting exact match for ${item.document_number}`);
      await this.exactMatchStrategy.executeIndexSearch(page, params, item.id);
      await page.waitForTimeout(3000);
      
      if (await this.exactMatchStrategy.verifyResultsPage(page, item.document_number)) {
        return await this.downloadDocument(page, item.document_number, downloadDir);
      }
      failedStrategyAttempt = 1;
    } catch (error) {
      const errorLog = this.errorLogger.createErrorLog(
        item.id, item.document_number, 1, 'exact_match', error instanceof Error ? error : new Error(String(error))
      );
      await this.errorLogger.logError(errorLog, page);
      failedStrategyAttempt = 1;
    }

    // Try Option 2: Intelligent Match
    try {
      console.log(`[Option 2] Attempting intelligent match for ${item.document_number}`);
      await page.goto(CONFIG.INDEX_SEARCH_URL); // Reset to search page
      await page.waitForTimeout(2000);
      await this.intelligentMatchStrategy.executeIndexSearch(page, params, item.id);
      await page.waitForTimeout(3000);
      
      const hasResults = await this.checkForResults(page);
      if (hasResults) {
        return await this.downloadDocument(page, item.document_number, downloadDir);
      }
      failedStrategyAttempt = 2;
    } catch (error) {
      const errorLog = this.errorLogger.createErrorLog(
        item.id, item.document_number, 2, 'intelligent_match', error instanceof Error ? error : new Error(String(error))
      );
      await this.errorLogger.logError(errorLog, page);
      failedStrategyAttempt = 2;
    }

    // Try Option 3: Exploratory Mode
    try {
      console.log(`[Option 3] Attempting exploratory mode for ${item.document_number}`);
      await page.goto(CONFIG.INDEX_SEARCH_URL); // Reset to search page
      await page.waitForTimeout(2000);
      await this.exploratoryStrategy.executeIndexSearch(page, params, item.id);
      await page.waitForTimeout(3000);
      
      const hasResults = await this.checkForResults(page);
      if (hasResults) {
        return await this.downloadDocument(page, item.document_number, downloadDir);
      }
      failedStrategyAttempt = 3;
    } catch (error) {
      const errorLog = this.errorLogger.createErrorLog(
        item.id, item.document_number, 3, 'exploratory_mode', error instanceof Error ? error : new Error(String(error))
      );
      await this.errorLogger.logError(errorLog, page);
      failedStrategyAttempt = 3;
    }

    return {
      success: false,
      error: 'Document not found after trying all strategies',
      failedStrategyAttempt
    };
  }

  /**
   * Check if results are available on the page
   */
  private async checkForResults(page: StagehandPage): Promise<boolean> {
    try {
      // First check for common error messages
      const errorCheck = await page.extract({
        instruction: "Check for error messages like 'Aucun document', 'introuvable', 'n'existe pas', 'Aucun lot', 'Erreur', or 'Invalid'",
        schema: z.object({
          hasError: z.boolean(),
          errorMessage: z.string().optional()
        })
      });

      if (errorCheck.hasError) {
        console.log(`Document not found: ${errorCheck.errorMessage}`);
        return false;
      }

      // Check for frames and print button
      const resultsCheck = await page.extract({
        instruction: "Check if there are frames (page, frmNavgt) or an 'Imprimer' button, which indicate successful results",
        schema: z.object({
          hasFrames: z.boolean(),
          hasPrintButton: z.boolean(),
          hasResults: z.boolean()
        })
      });

      return resultsCheck.hasFrames || resultsCheck.hasPrintButton || resultsCheck.hasResults;
    } catch (error) {
      return false;
    }
  }

  /**
   * Download the document
   */
  private async downloadDocument(
    page: StagehandPage,
    documentNumber: string,
    downloadDir: string
  ): Promise<{ success: boolean; filePath?: string; error?: string }> {
    try {
      console.log('Checking if we need to select a radio button first...');
      
      // Check if we have search results with radio buttons
      const resultsCheck = await page.extract({
        instruction: "Check if there are radio buttons (rdRegst) that need to be selected, or if there's already an 'Imprimer' button available",
        schema: z.object({
          hasRadioButtons: z.boolean(),
          hasPrintButton: z.boolean(),
          radioButtons: z.array(z.string()).optional()
        })
      });
      
      if (resultsCheck.hasRadioButtons && !resultsCheck.hasPrintButton) {
        console.log('Found search results with radio buttons. Selecting first option...');
        
        // Select the first radio button (usually the most relevant)
        await page.act("click on the first radio button in the search results");
        
        // Submit to go to the document
        await page.act("click the 'Soumettre' button to view the selected document");
        
        // Wait for the document page to load
        await page.waitForTimeout(3000);
      }
      
      // Now look for frames or print button
      console.log('Looking for frames and print button...');
      await page.waitForTimeout(2000);
      
      // Enhanced print button handling with proper frame navigation based on working Playwright script
      await page.waitForTimeout(5000); // Wait longer for frames to fully load
      
      // Strategy 1: Try direct frame selector approach (matching Playwright pattern)
      try {
        console.log('📄 Processor attempt 1: Direct frame selector approach...');
        
        // First, wait for the page frame to be available
        await page.act("wait for frame[name='page'] to be available on the page");
        await page.waitForTimeout(2000);
        
        // Then access the nested navigation frame and click Imprimer
        await page.act("in frame[name='page'], wait for frame[name='frmNavgt'] to load, then click the link containing 'Imprimer'");
        console.log('✅ Successfully clicked print button using direct frame selector approach');
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        console.log('❌ Processor attempt 1 failed:', errorMessage);
        
        // Strategy 2: Step-by-step frame navigation
        try {
          console.log('📄 Processor attempt 2: Step-by-step frame navigation...');
          
          // Step 1: Switch to page frame
          await page.act("switch to frame named 'page'");
          await page.waitForTimeout(1000);
          
          // Step 2: Switch to navigation frame within page frame
          await page.act("switch to frame named 'frmNavgt'");
          await page.waitForTimeout(1000);
          
          // Step 3: Click Imprimer link
          await page.act("click the link with text 'Imprimer'");
          console.log('✅ Successfully clicked print button using step-by-step frame navigation');
        } catch (error2) {
          const errorMessage2 = error2 instanceof Error ? error2.message : String(error2);
          console.log('❌ Processor attempt 2 failed:', errorMessage2);
          
          // Strategy 3: Use CSS selectors for nested frames
          try {
            console.log('📄 Processor attempt 3: CSS selector for nested frames...');
            await page.act("click on frame[name='page'] frame[name='frmNavgt'] a[href*='Imprimer'], or any link containing 'Imprimer' in nested frames");
            console.log('✅ Successfully clicked print button using CSS selector approach');
          } catch (error3) {
            const errorMessage3 = error3 instanceof Error ? error3.message : String(error3);
            console.log('❌ Processor attempt 3 failed:', errorMessage3);
            
            // Strategy 4: Look for any Imprimer link anywhere on the page
            try {
              console.log('📄 Processor attempt 4: Global Imprimer search...');
              await page.act("find and click any link or button with text 'Imprimer' anywhere on the page or in any frame");
              console.log('✅ Successfully clicked print button using global search');
            } catch (error4) {
              const errorMessage4 = error4 instanceof Error ? error4.message : String(error4);
              console.log('❌ Processor attempt 4 failed:', errorMessage4);
              
              // Strategy 5: Use extract to find the exact location first
              try {
                console.log('📄 Processor attempt 5: Extract-first approach...');
                
                // First extract frame information
                const frameInfo = await page.extract({
                  instruction: "Find all frames on the page and identify where the 'Imprimer' link is located",
                  schema: z.object({
                    frames: z.array(z.string()),
                    imprimerLocation: z.string().optional()
                  })
                });
                
                console.log('Frame info:', frameInfo);
                
                // Then try to click based on extracted info
                if (frameInfo.imprimerLocation) {
                  await page.act(`click the Imprimer link at: ${frameInfo.imprimerLocation}`);
                } else {
                  await page.act("click any visible link or button containing the text 'Imprimer'");
                }
                console.log('✅ Successfully clicked print button using extract-first approach');
              } catch (error5) {
                const errorMessage5 = error5 instanceof Error ? error5.message : String(error5);
                console.log('❌ Processor attempt 5 failed:', errorMessage5);
                
                // Strategy 6: Fallback - wait longer and try simple approach
                try {
                  console.log('📄 Processor attempt 6: Extended wait fallback...');
                  await page.waitForTimeout(5000); // Wait even longer for dynamic content
                  await page.act("click any element that has the text 'Imprimer' or looks like a print button");
                  console.log('✅ Successfully clicked print button using extended wait fallback');
                } catch (error6) {
                  const errorMessage6 = error6 instanceof Error ? error6.message : String(error6);
                  console.log('❌ Processor attempt 6 failed:', errorMessage6);
                  throw new Error(`Could not find 'Imprimer' button after 6 attempts. The page structure may be different than expected. Last error: ${errorMessage6}`);
                }
              }
            }
          }
        }
      }
      
      // Wait for download to complete
      console.log('Waiting for download to complete...');
      const downloadedFile = await this.fileManager.waitForDownloadComplete(downloadDir);
      
      if (!downloadedFile) {
        throw new Error('Download timeout - no file was downloaded');
      }
      
      // Rename the file
      const targetName = `${documentNumber}.pdf`;
      const finalPath = await this.fileManager.renameFileWithDuplicateHandling(
        downloadDir,
        downloadedFile,
        targetName
      );
      
      // Verify file content
      const isValid = await this.fileManager.verifyFileContent(finalPath);
      if (!isValid) {
        throw new Error('Downloaded file is empty or too small');
      }
      
      console.log(`Document downloaded successfully: ${finalPath}`);
      return {
        success: true,
        filePath: finalPath
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  /**
   * Handle errors for a queue item
   */
  private async handleItemError(
    item: ExtractionQueueItem,
    error: any,
    strategyAttempt: number = 0 // Default to 0 if not specified
  ): Promise<void> {
    const newRetryCount = (item.retry_count || 0) + 1;
    const errorMessage = error instanceof Error ? error.message : String(error);
    
    console.error(`Attempt ${newRetryCount}/${CONFIG.RETRY_LIMIT} failed for item ${item.id}: ${errorMessage}`);
    
    await this.supabaseService.updateItemWithError(
      item.id,
      strategyAttempt,
      'complete_extraction',
      errorMessage,
      newRetryCount
    );
  }

  /**
   * Clean up old downloads periodically
   */
  async cleanupOldDownloads(): Promise<void> {
    await this.fileManager.cleanupOldDownloads(CONFIG.BASE_DOWNLOAD_DIR, 7);
  }
}
