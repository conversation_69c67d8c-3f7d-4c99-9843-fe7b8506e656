import { Stagehand } from '@browserbasehq/stagehand';
import { z } from 'zod';
import { CONFIG, ExtractionQueueItem, ActeSearchParams, IndexSearchParams } from './config';
import { SupabaseService } from './supabase-client';
import { FileManager } from './utils/file-manager';
import { ErrorLogger } from './utils/error-logger';
import { ExactMatchStrategy } from './strategies/exact-match';
import { IntelligentMatchStrategy } from './strategies/intelligent-match';
import { ExploratoryStrategy } from './strategies/exploratory';
import { StagehandPage, ExtractionResult } from './types';
import { cleanupCacheLock } from './utils/cleanup';

export class ExtractionProcessor {
  private stagehand: Stagehand;
  private supabaseService: SupabaseService;
  private fileManager: FileManager;
  private errorLogger: ErrorLogger;
  private exactMatchStrategy: ExactMatchStrategy;
  private intelligentMatchStrategy: IntelligentMatchStrategy;
  private exploratoryStrategy: ExploratoryStrategy;
  private processedCount: number = 0;

  constructor() {
    this.supabaseService = new SupabaseService();
    this.fileManager = new FileManager();
    this.errorLogger = new ErrorLogger(this.supabaseService);
    this.exactMatchStrategy = new ExactMatchStrategy(this.errorLogger);
    this.intelligentMatchStrategy = new IntelligentMatchStrategy(this.errorLogger);
    this.exploratoryStrategy = new ExploratoryStrategy(this.errorLogger);

    // Initialize Stagehand
    this.stagehand = new Stagehand({
      env: CONFIG.HEADLESS ? 'BROWSERBASE' : 'LOCAL',
      modelName: CONFIG.MODEL_NAME as any,
      modelClientOptions: CONFIG.MODEL_API_KEY ? { apiKey: CONFIG.MODEL_API_KEY } : undefined,
      enableCaching: CONFIG.ENABLE_CACHING,
      selfHeal: CONFIG.SELF_HEAL,
      verbose: (CONFIG.VERBOSE as 0 | 1 | 2) || 1,
      domSettleTimeoutMs: CONFIG.DOM_SETTLE_TIMEOUT,
      localBrowserLaunchOptions: {
        headless: CONFIG.HEADLESS,
        downloadsPath: CONFIG.BASE_DOWNLOAD_DIR,
        viewport: { width: 1280, height: 720 }
      }
    });
  }

  /**
   * Main run method
   */
  async run(): Promise<void> {
    console.log('Starting Stage Hand extraction processor...');
    
    // Clean up any stale cache lock files
    await cleanupCacheLock();
    
    try {
      // Initialize Stage Hand
      await this.stagehand.init();
      const page = this.stagehand.page as unknown as StagehandPage;
      
      // Ensure download directories exist
      await this.fileManager.ensureDownloadDirectory();
      await this.fileManager.ensureDownloadDirectory('screenshots');
      
      // Login once
      console.log('Logging in to Registre Foncier...');
      await this.login(page);
      
      // Process queue items
      let keepProcessing = true;
      
      while (keepProcessing) {
        const queueItem = await this.supabaseService.getNextEnAttenteItem();
        
        if (!queueItem) {
          console.log('No more pending items to process.');
          break;
        }
        
        console.log(`Processing item ${queueItem.id} (${queueItem.document_source}: ${queueItem.document_number})...`);
        
        // Update status to En_traitement
        await this.supabaseService.updateItemStatus(queueItem.id, {
          status: 'En_traitement',
          processing_started_at: new Date().toISOString()
        });
        
        try {
          // Process the item
          const result = await this.processItem(page, queueItem);
          
          if (result.success && result.filePath) {
            await this.supabaseService.markItemAsDownloaded(queueItem.id, result.filePath);
            console.log(`Successfully downloaded document for item ${queueItem.id}`);
          } else {
            throw new Error(result.error || 'Failed to download document');
          }
          
          this.processedCount++;
          
          if (this.processedCount >= CONFIG.BATCH_SIZE) {
            console.log('Reached batch limit. Stopping processing.');
            keepProcessing = false;
          }
          
          // Add delay between items
          await page.waitForTimeout(CONFIG.ACTION_DELAY + Math.random() * 3000);
          
        } catch (error) {
          await this.handleItemError(queueItem, error);
        }
      }
      
    } catch (error) {
      console.error('Critical error in extraction processor:', error);
      throw error;
    } finally {
      await this.stagehand.close();
      console.log('Stage Hand closed. Extraction batch complete.');
    }
  }

  /**
   * Login to the website (optimized for speed)
   */
  private async login(page: StagehandPage): Promise<void> {
    try {
      // Navigate to the main page first
      await page.goto(CONFIG.BASE_URL);
      await page.waitForTimeout(1000);
      
      // Click on the first 'Entrée du site' link (element 0)
      await page.act("click on the link with href '/Sirf/pf_acces.asp'");
      await page.waitForTimeout(2000);
      
      // Now we should be on the login page - fill the form
      await page.act(`type '${CONFIG.USER_CODE}' in the 'Code d'utilisateur' input field`);
      await page.act(`type '${CONFIG.PASSWORD}' in the 'Mot de passe' input field`);
      await page.act("click the 'Soumettre' button");
      
      // Wait for login to complete
      await page.waitForTimeout(3000);
      
      console.log('Login successful');
    } catch (error) {
      console.error('Login error:', error);
      throw error;
    }
  }

  /**
   * Process a single queue item
   */
  private async processItem(
    page: StagehandPage,
    item: ExtractionQueueItem
  ): Promise<ExtractionResult> {
    const itemDownloadDir = await this.fileManager.ensureDownloadDirectory(item.id);
    
    try {
      // Navigate to appropriate search page using direct URLs
      if (item.document_source === 'acte') {
        console.log('Navigating to Acte search page...');
        await page.goto(CONFIG.ACTE_SEARCH_URL);
        await page.waitForTimeout(2000);
        return await this.extractActe(page, item, itemDownloadDir);
      } else {
        console.log('Navigating to Index search page...');
        await page.goto(CONFIG.INDEX_SEARCH_URL);
        await page.waitForTimeout(2000);
        return await this.extractIndex(page, item, itemDownloadDir);
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error),
        failedStrategyAttempt: 0 // General error, not tied to a specific strategy attempt
      };
    }
  }

  /**
   * Extract an Acte document using the three-path strategy
   */
  private async extractActe(
    page: StagehandPage,
    item: ExtractionQueueItem,
    downloadDir: string
  ): Promise<ExtractionResult> {
    const params: ActeSearchParams = {
      inscriptionNumber: item.document_number,
      circonscription: item.circonscription_fonciere,
      acteType: item.acte_type
    };

    let failedStrategyAttempt = 0;

    // Try Option 1: Exact Match
    try {
      console.log(`[Option 1] Attempting exact match for ${item.document_number}`);
      await this.exactMatchStrategy.executeActeSearch(page, params, item.id);
      await page.waitForTimeout(3000);
      
      if (await this.exactMatchStrategy.verifyResultsPage(page, item.document_number)) {
        return await this.downloadDocument(page, item.document_number, downloadDir);
      }
      failedStrategyAttempt = 1;
    } catch (error) {
      const errorLog = this.errorLogger.createErrorLog(
        item.id, item.document_number, 1, 'exact_match', error instanceof Error ? error : new Error(String(error))
      );
      await this.errorLogger.logError(errorLog, page);
      failedStrategyAttempt = 1;
    }

    // Try Option 2: Intelligent Match
    try {
      console.log(`[Option 2] Attempting intelligent match for ${item.document_number}`);
      await page.goto(CONFIG.ACTE_SEARCH_URL); // Reset to search page
      await page.waitForTimeout(2000);
      await this.intelligentMatchStrategy.executeActeSearch(page, params, item.id);
      await page.waitForTimeout(3000);
      
      // Check if we got results
      const hasResults = await this.checkForResults(page);
      if (hasResults) {
        return await this.downloadDocument(page, item.document_number, downloadDir);
      }
      failedStrategyAttempt = 2;
    } catch (error) {
      const errorLog = this.errorLogger.createErrorLog(
        item.id, item.document_number, 2, 'intelligent_match', error instanceof Error ? error : new Error(String(error))
      );
      await this.errorLogger.logError(errorLog, page);
      failedStrategyAttempt = 2;
    }

    // Try Option 3: Exploratory Mode
    try {
      console.log(`[Option 3] Attempting exploratory mode for ${item.document_number}`);
      await page.goto(CONFIG.ACTE_SEARCH_URL); // Reset to search page
      await page.waitForTimeout(2000);
      await this.exploratoryStrategy.executeActeSearch(page, params, item.id);
      await page.waitForTimeout(3000);
      
      const hasResults = await this.checkForResults(page);
      if (hasResults) {
        return await this.downloadDocument(page, item.document_number, downloadDir);
      }
      failedStrategyAttempt = 3;
    } catch (error) {
      const errorLog = this.errorLogger.createErrorLog(
        item.id, item.document_number, 3, 'exploratory_mode', error instanceof Error ? error : new Error(String(error))
      );
      await this.errorLogger.logError(errorLog, page);
      failedStrategyAttempt = 3;
    }

    return {
      success: false,
      error: 'Document not found after trying all strategies',
      failedStrategyAttempt
    };
  }

  /**
   * Extract an Index document using the three-path strategy
   */
  private async extractIndex(
    page: StagehandPage,
    item: ExtractionQueueItem,
    downloadDir: string
  ): Promise<ExtractionResult> {
    const params: IndexSearchParams = {
      lotNumber: item.document_number,
      circonscription: item.circonscription_fonciere,
      cadastre: item.cadastre,
      designationSecondaire: item.designation_secondaire
    };

    let failedStrategyAttempt = 0;

    // Try Option 1: Exact Match
    try {
      console.log(`[Option 1] Attempting exact match for ${item.document_number}`);
      await this.exactMatchStrategy.executeIndexSearch(page, params, item.id);
      await page.waitForTimeout(3000);
      
      if (await this.exactMatchStrategy.verifyResultsPage(page, item.document_number)) {
        return await this.downloadDocument(page, item.document_number, downloadDir);
      }
      failedStrategyAttempt = 1;
    } catch (error) {
      const errorLog = this.errorLogger.createErrorLog(
        item.id, item.document_number, 1, 'exact_match', error instanceof Error ? error : new Error(String(error))
      );
      await this.errorLogger.logError(errorLog, page);
      failedStrategyAttempt = 1;
    }

    // Try Option 2: Intelligent Match
    try {
      console.log(`[Option 2] Attempting intelligent match for ${item.document_number}`);
      await page.goto(CONFIG.INDEX_SEARCH_URL); // Reset to search page
      await page.waitForTimeout(2000);
      await this.intelligentMatchStrategy.executeIndexSearch(page, params, item.id);
      await page.waitForTimeout(3000);
      
      const hasResults = await this.checkForResults(page);
      if (hasResults) {
        return await this.downloadDocument(page, item.document_number, downloadDir);
      }
      failedStrategyAttempt = 2;
    } catch (error) {
      const errorLog = this.errorLogger.createErrorLog(
        item.id, item.document_number, 2, 'intelligent_match', error instanceof Error ? error : new Error(String(error))
      );
      await this.errorLogger.logError(errorLog, page);
      failedStrategyAttempt = 2;
    }

    // Try Option 3: Exploratory Mode
    try {
      console.log(`[Option 3] Attempting exploratory mode for ${item.document_number}`);
      await page.goto(CONFIG.INDEX_SEARCH_URL); // Reset to search page
      await page.waitForTimeout(2000);
      await this.exploratoryStrategy.executeIndexSearch(page, params, item.id);
      await page.waitForTimeout(3000);
      
      const hasResults = await this.checkForResults(page);
      if (hasResults) {
        return await this.downloadDocument(page, item.document_number, downloadDir);
      }
      failedStrategyAttempt = 3;
    } catch (error) {
      const errorLog = this.errorLogger.createErrorLog(
        item.id, item.document_number, 3, 'exploratory_mode', error instanceof Error ? error : new Error(String(error))
      );
      await this.errorLogger.logError(errorLog, page);
      failedStrategyAttempt = 3;
    }

    return {
      success: false,
      error: 'Document not found after trying all strategies',
      failedStrategyAttempt
    };
  }

  /**
   * Check if results are available on the page
   */
  private async checkForResults(page: StagehandPage): Promise<boolean> {
    try {
      // First check for common error messages
      const errorCheck = await page.extract({
        instruction: "Check for error messages like 'Aucun document', 'introuvable', 'n'existe pas', 'Aucun lot', 'Erreur', or 'Invalid'",
        schema: z.object({
          hasError: z.boolean(),
          errorMessage: z.string().optional()
        })
      });

      if (errorCheck.hasError) {
        console.log(`Document not found: ${errorCheck.errorMessage}`);
        return false;
      }

      // Check for frames and print button
      const resultsCheck = await page.extract({
        instruction: "Check if there are frames (page, frmNavgt) or an 'Imprimer' button, which indicate successful results",
        schema: z.object({
          hasFrames: z.boolean(),
          hasPrintButton: z.boolean(),
          hasResults: z.boolean()
        })
      });

      return resultsCheck.hasFrames || resultsCheck.hasPrintButton || resultsCheck.hasResults;
    } catch (error) {
      return false;
    }
  }

  /**
   * Download the document
   */
  private async downloadDocument(
    page: StagehandPage,
    documentNumber: string,
    downloadDir: string
  ): Promise<{ success: boolean; filePath?: string; error?: string }> {
    try {
      console.log('Checking if we need to select a radio button first...');

      // Check if we have search results with radio buttons
      const resultsCheck = await page.extract({
        instruction: "Check if there are radio buttons (rdRegst) that need to be selected, or if there's already an 'Imprimer' button available",
        schema: z.object({
          hasRadioButtons: z.boolean(),
          hasPrintButton: z.boolean(),
          radioButtons: z.array(z.string()).optional()
        })
      });

      if (resultsCheck.hasRadioButtons && !resultsCheck.hasPrintButton) {
        console.log('Found search results with radio buttons. Selecting first option...');

        // Select the first radio button (usually the most relevant)
        await page.act("click on the first radio button in the search results");

        // Submit to go to the document
        await page.act("click the 'Soumettre' button to view the selected document");

        // Wait for the document page to load
        await page.waitForTimeout(3000);
      }

      // Now look for frames or print button
      console.log('Looking for frames and print button...');
      await page.waitForTimeout(2000);

      // Use the improved print button detection method
      await this.clickPrintButtonRobust(page, documentNumber);
      
      // Wait for download to complete
      console.log('Waiting for download to complete...');
      const downloadedFile = await this.fileManager.waitForDownloadComplete(downloadDir);
      
      if (!downloadedFile) {
        throw new Error('Download timeout - no file was downloaded');
      }
      
      // Rename the file
      const targetName = `${documentNumber}.pdf`;
      const finalPath = await this.fileManager.renameFileWithDuplicateHandling(
        downloadDir,
        downloadedFile,
        targetName
      );
      
      // Verify file content
      const isValid = await this.fileManager.verifyFileContent(finalPath);
      if (!isValid) {
        throw new Error('Downloaded file is empty or too small');
      }
      
      console.log(`Document downloaded successfully: ${finalPath}`);
      return {
        success: true,
        filePath: finalPath
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  /**
   * Robust print button detection and clicking with multiple strategies
   */
  private async clickPrintButtonRobust(
    page: StagehandPage,
    documentNumber: string
  ): Promise<void> {
    console.log('🎯 Starting robust print button detection...');

    // Wait for frames to fully load
    await page.waitForTimeout(5000);

    // Strategy 1: Frame-aware extraction and navigation (most reliable)
    try {
      console.log('📄 Strategy 1: Frame-aware extraction and navigation...');

      // First, extract frame structure to understand the page layout
      const frameAnalysis = await page.extract({
        instruction: "Analyze the page structure and identify all frames, particularly looking for 'page' frame and 'frmNavgt' frame. Also check if there's an 'Imprimer' link visible anywhere.",
        schema: z.object({
          hasPageFrame: z.boolean(),
          hasNavFrame: z.boolean(),
          frameStructure: z.string().optional(),
          imprimerVisible: z.boolean(),
          imprimerLocation: z.string().optional()
        })
      });

      console.log('Frame analysis result:', frameAnalysis);

      if (frameAnalysis.hasPageFrame && frameAnalysis.hasNavFrame) {
        // Use the known working pattern from Playwright scripts
        await page.act("wait for the frame named 'page' to be fully loaded");
        await page.waitForTimeout(2000);

        await page.act("within the frame named 'page', wait for the nested frame named 'frmNavgt' to be available");
        await page.waitForTimeout(2000);

        await page.act("in the frame 'page', then in its nested frame 'frmNavgt', click the link with text 'Imprimer'");
        console.log('✅ Successfully clicked print button using frame-aware navigation');
        return;
      }
    } catch (error) {
      console.log('❌ Strategy 1 failed:', error instanceof Error ? error.message : String(error));
    }

    // Strategy 2: Direct frame selector approach
    try {
      console.log('📄 Strategy 2: Direct frame selector approach...');

      // Wait for frames to be available
      await page.act("wait for frame[name='page'] to be available on the page");
      await page.waitForTimeout(2000);

      // Navigate to nested frame and click
      await page.act("in frame[name='page'], wait for frame[name='frmNavgt'] to load, then click the link containing 'Imprimer'");
      console.log('✅ Successfully clicked print button using direct frame selector');
      return;
    } catch (error) {
      console.log('❌ Strategy 2 failed:', error instanceof Error ? error.message : String(error));
    }

    // Strategy 3: Step-by-step frame navigation
    try {
      console.log('📄 Strategy 3: Step-by-step frame navigation...');

      // Step 1: Switch to page frame
      await page.act("switch to frame named 'page'");
      await page.waitForTimeout(1500);

      // Step 2: Switch to navigation frame within page frame
      await page.act("switch to frame named 'frmNavgt'");
      await page.waitForTimeout(1500);

      // Step 3: Click Imprimer link
      await page.act("click the link with text 'Imprimer'");
      console.log('✅ Successfully clicked print button using step-by-step navigation');
      return;
    } catch (error) {
      console.log('❌ Strategy 3 failed:', error instanceof Error ? error.message : String(error));
    }

    // Strategy 4: CSS selector approach for nested frames
    try {
      console.log('📄 Strategy 4: CSS selector for nested frames...');
      await page.act("click on the element matching 'frame[name=\"page\"] frame[name=\"frmNavgt\"] a[href*=\"Imprimer\"]' or any link containing 'Imprimer' in nested frames");
      console.log('✅ Successfully clicked print button using CSS selector');
      return;
    } catch (error) {
      console.log('❌ Strategy 4 failed:', error instanceof Error ? error.message : String(error));
    }

    // Strategy 5: Global search with frame context
    try {
      console.log('📄 Strategy 5: Global search with frame context...');
      await page.act("search all frames on the page for any link or button with text 'Imprimer' and click it");
      console.log('✅ Successfully clicked print button using global search');
      return;
    } catch (error) {
      console.log('❌ Strategy 5 failed:', error instanceof Error ? error.message : String(error));
    }

    // Strategy 6: Extract-first approach with detailed analysis
    try {
      console.log('📄 Strategy 6: Extract-first detailed analysis...');

      // Get detailed information about the page structure
      const detailedAnalysis = await page.extract({
        instruction: "Provide a detailed analysis of the page structure, including all frames, iframes, and any elements containing 'Imprimer' text. Include exact selectors and locations.",
        schema: z.object({
          allFrames: z.array(z.string()),
          imprimerElements: z.array(z.string()),
          pageStructure: z.string(),
          recommendedSelector: z.string().optional()
        })
      });

      console.log('Detailed analysis:', detailedAnalysis);

      if (detailedAnalysis.recommendedSelector) {
        await page.act(`click on the element: ${detailedAnalysis.recommendedSelector}`);
      } else if (detailedAnalysis.imprimerElements.length > 0) {
        await page.act(`click on the first available Imprimer element: ${detailedAnalysis.imprimerElements[0]}`);
      } else {
        await page.act("click any visible element containing the text 'Imprimer'");
      }
      console.log('✅ Successfully clicked print button using extract-first analysis');
      return;
    } catch (error) {
      console.log('❌ Strategy 6 failed:', error instanceof Error ? error.message : String(error));
    }

    // Strategy 7: Extended wait and simple approach
    try {
      console.log('📄 Strategy 7: Extended wait and simple approach...');
      await page.waitForTimeout(8000); // Wait even longer for dynamic content
      await page.act("click any element that has the text 'Imprimer' or looks like a print button");
      console.log('✅ Successfully clicked print button using extended wait');
      return;
    } catch (error) {
      console.log('❌ Strategy 7 failed:', error instanceof Error ? error.message : String(error));
    }

    // If all strategies fail, throw comprehensive error
    throw new Error(`Could not find 'Imprimer' button after trying 7 different strategies. The page structure may be different than expected or the button may not be available for document ${documentNumber}.`);
  }

  /**
   * Handle errors for a queue item
   */
  private async handleItemError(
    item: ExtractionQueueItem,
    error: any,
    strategyAttempt: number = 0 // Default to 0 if not specified
  ): Promise<void> {
    const newRetryCount = (item.retry_count || 0) + 1;
    const errorMessage = error instanceof Error ? error.message : String(error);

    console.error(`Attempt ${newRetryCount}/${CONFIG.RETRY_LIMIT} failed for item ${item.id}: ${errorMessage}`);

    await this.supabaseService.updateItemWithError(
      item.id,
      strategyAttempt,
      'complete_extraction',
      errorMessage,
      newRetryCount
    );
  }

  /**
   * Clean up old downloads periodically
   */
  async cleanupOldDownloads(): Promise<void> {
    await this.fileManager.cleanupOldDownloads(CONFIG.BASE_DOWNLOAD_DIR, 7);
  }
}
