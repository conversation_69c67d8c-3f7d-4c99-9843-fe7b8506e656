import { chromium } from "playwright";
import { rename, readdir, readFile, unlink, writeFile } from "fs/promises";
import { argv } from "process";
import { mkdir } from "fs/promises";
import { existsSync } from "fs";

interface AppConfig {
  targetUrl: string;
  defaultTimeout: number;
  userCode: string;
  password: string;
  lotNumber: string;
}

interface RetryState {
  attempts: number;
  lastError?: string;
  status: 'en_attente' | 'en_cours' | 'termine' | 'Document_introuvable';
}

const MAX_RETRIES = 3;

async function loadConfig(): Promise<AppConfig> {
  try {
    const configFile = await readFile("config.json", "utf-8");
    const config = JSON.parse(configFile);
    if (
      typeof config.targetUrl !== "string" ||
      typeof config.defaultTimeout !== "number"
    ) {
      throw new Error("Invalid config.json structure");
    }
    return config as AppConfig;
  } catch (error) {
    console.error("Error loading or parsing config.json:", error);
    return {
      targetUrl: "https://example.com",
      defaultTimeout: 30000,
      userCode: "",
      password: "",
      lotNumber: "",
    };
  }
}

let downloadPath: string;
let lotNumber: string;

async function loadRetryState(lotNumber: string): Promise<RetryState> {
  const stateFile = `./retry-state-index-${lotNumber}.json`;
  if (existsSync(stateFile)) {
    try {
      const data = await readFile(stateFile, 'utf-8');
      return JSON.parse(data);
    } catch (error) {
      console.log(`📝 Could not load retry state: ${error.message}`);
    }
  }
  return { attempts: 0, status: 'en_attente' };
}

async function saveRetryState(lotNumber: string, state: RetryState): Promise<void> {
  const stateFile = `./retry-state-index-${lotNumber}.json`;
  await writeFile(stateFile, JSON.stringify(state, null, 2));
}

async function extractDocument(config: AppConfig, isHeaded: boolean): Promise<void> {
  const browser = await chromium.launch({
    headless: !isHeaded,
    downloadsPath: downloadPath,
  });
  
  try {
    const page = await browser.newPage();
    page.setDefaultTimeout(config.defaultTimeout);

    await page.goto(config.targetUrl);

    // enter website
    await page.goto("https://www.registrefoncier.gouv.qc.ca/Sirf/");
    await page.getByRole("link", { name: "Entrée du site" }).nth(1).click();

    // login
    await page.getByRole("textbox", { name: "Code d'utilisateur" }).click();
    await page
      .getByRole("textbox", { name: "Code d'utilisateur" })
      .fill(config.userCode);
    await page.getByRole("textbox", { name: "Mot de passe" }).click();
    await page.getByRole("textbox", { name: "Mot de passe" }).fill(config.password);
    await page.getByRole("button", { name: "Soumettre" }).click();

    // login will trigger a popup, select the menu we need will close the pop up and renavigate automatically main page
    const page1Promise = page.waitForEvent("popup");
    await page.getByRole("link", { name: "Consulter", exact: true }).click();
    const page1 = await page1Promise;
    await page1.getByRole("link", { name: "Index des immeubles" }).click();

    await page.getByRole("textbox", { name: "Numéro de lot" }).click();
    await page.getByRole("textbox", { name: "Numéro de lot" }).fill(lotNumber);

    await page.getByRole("button", { name: "Soumettre" }).click();
    await page
      .locator('frame[name="page"]')
      .contentFrame()
      .locator('frame[name="frmNavgt"]')
      .contentFrame()
      .getByRole("link", { name: "Imprimer" })
      .click();

    // wait for the download to complete, the page download event is a bit unreliable.
    await page.waitForTimeout(10000);

    // Check if file was downloaded
    const files = await readdir(downloadPath);
    if (files.length === 0) {
      throw new Error("No document was downloaded");
    }

    // Rename the file
    await rename(`${downloadPath}/${files[0]}`, `${downloadPath}/${lotNumber}.pdf`);

  } finally {
    await browser.close();
  }
}

const main = async () => {
  const config = await loadConfig();
  const { lotNumber: configLotNumber } = config;
  lotNumber = configLotNumber;

  downloadPath = `./downloads/${lotNumber}`;
  await mkdir(downloadPath, { recursive: true });

  const args = argv.slice(2);
  const isHeaded = args.includes("--headed");

  // Load retry state
  let retryState = await loadRetryState(lotNumber);

  // Check if already marked as unavailable
  if (retryState.status === 'Document_introuvable') {
    console.log(`❌ Document ${lotNumber} is already marked as Document_introuvable`);
    return;
  }

  // Check if already completed
  if (retryState.status === 'termine') {
    console.log(`✅ Document ${lotNumber} is already completed`);
    return;
  }

  console.log(`🔄 Processing lot ${lotNumber} (attempt ${retryState.attempts + 1}/${MAX_RETRIES})`);
  
  try {
    retryState.status = 'en_cours';
    retryState.attempts += 1;
    await saveRetryState(lotNumber, retryState);

    await extractDocument(config, isHeaded);

    // Success
    retryState.status = 'termine';
    await saveRetryState(lotNumber, retryState);
    console.log(`✅ Successfully extracted document for lot ${lotNumber}`);

  } catch (error) {
    console.log(`📝 Attempt ${retryState.attempts}/${MAX_RETRIES} failed for lot ${lotNumber}: ${error.message}`);
    
    retryState.lastError = error.message;

    if (retryState.attempts >= MAX_RETRIES) {
      retryState.status = 'Document_introuvable';
      await saveRetryState(lotNumber, retryState);
      console.log(`❌ Lot ${lotNumber} marked as Document_introuvable after ${MAX_RETRIES} failed attempts`);
      // Exit gracefully with success code since we properly handled the unavailable document
      process.exit(0);
    } else {
      retryState.status = 'en_attente';
      await saveRetryState(lotNumber, retryState);
      console.log(`🔄 Lot ${lotNumber} will be retried (attempt ${retryState.attempts}/${MAX_RETRIES})`);
      // Exit gracefully to allow future retries
      process.exit(0);
    }
  }
};

main().catch((error) => {
  // Only log critical errors that weren't handled in the try-catch
  console.error("Critical extraction error:", error.message);
  process.exit(1);
});
