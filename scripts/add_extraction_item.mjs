// Script to add an item to the extraction queue
import { createClient } from '@supabase/supabase-js';

// These should be environment variables in production
const SUPABASE_URL = "https://sqzqvxqcybghcgrpubsy.supabase.co";
const SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InNxenF2eHFjeWJnaGNncnB1YnN5Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0MTA5NTUxOCwiZXhwIjoyMDU2NjcxNTE4fQ.YN8lGh9PurkS8dq7a7zzlrgoETGyWltcJrLnOBI7t6M";

// Initialize Supabase client
const supabase = createClient(SUPABASE_URL, SUPABASE_KEY);

/**
 * Add a new acte extraction item to the queue
 */
async function addActeExtractionItem(documentNumber, circonscriptionFonciere = null, acteType = 'Acte') {
  try {
    const { data, error } = await supabase
      .from('extraction_queue')
      .insert({
        document_source: 'acte',
        document_number: documentNumber,
        circonscription_fonciere: circonscriptionFonciere,
        acte_type: acteType,
        status: 'En_attente'
      })
      .select()
      .single();

    if (error) throw error;
    
    console.log(`Successfully added acte extraction item with ID: ${data.id}`);
    return data;
  } catch (error) {
    console.error("Error adding acte extraction item:", error);
    throw error;
  }
}

/**
 * Add a new index extraction item to the queue
 */
async function addIndexExtractionItem(documentNumber, circonscriptionFonciere = null, cadastre = null, designationSecondaire = null) {
  try {
    const { data, error } = await supabase
      .from('extraction_queue')
      .insert({
        document_source: 'index',
        document_number: documentNumber,
        circonscription_fonciere: circonscriptionFonciere,
        cadastre: cadastre,
        designation_secondaire: designationSecondaire,
        status: 'En_attente'
      })
      .select()
      .single();

    if (error) throw error;
    
    console.log(`Successfully added index extraction item with ID: ${data.id}`);
    return data;
  } catch (error) {
    console.error("Error adding index extraction item:", error);
    throw error;
  }
}

/**
 * Get queue status
 */
async function getQueueStatus() {
  try {
    const { data, error } = await supabase
      .from('extraction_queue')
      .select('document_source, status, count')
      .select('document_source, status')
      .order('created_at', { ascending: true });

    if (error) throw error;
    
    // Group by status
    const statusCount = {};
    for (const item of data) {
      const key = `${item.document_source}:${item.status}`;
      statusCount[key] = (statusCount[key] || 0) + 1;
    }
    
    console.log("Queue status:");
    for (const [key, count] of Object.entries(statusCount)) {
      const [source, status] = key.split(':');
      console.log(`${source} - ${status}: ${count}`);
    }
    
    return statusCount;
  } catch (error) {
    console.error("Error getting queue status:", error);
    throw error;
  }
}

// Usage example:
// Run with: node scripts/add_extraction_item.mjs
async function main() {
  const args = process.argv.slice(2);
  const command = args[0]?.toLowerCase();
  
  try {
    switch (command) {
      case 'acte':
        if (args.length < 2) {
          console.error("Usage: node add_extraction_item.mjs acte <document_number> [circonscription_fonciere] [acte_type]");
          process.exit(1);
        }
        await addActeExtractionItem(args[1], args[2], args[3]);
        break;
        
      case 'index':
        if (args.length < 2) {
          console.error("Usage: node add_extraction_item.mjs index <document_number> [circonscription_fonciere] [cadastre] [designation_secondaire]");
          process.exit(1);
        }
        await addIndexExtractionItem(args[1], args[2], args[3], args[4]);
        break;
        
      case 'status':
        await getQueueStatus();
        break;
        
      default:
        console.log("Available commands:");
        console.log("- acte <document_number> [circonscription_fonciere] [acte_type]");
        console.log("- index <document_number> [circonscription_fonciere] [cadastre] [designation_secondaire]");
        console.log("- status");
        break;
    }
  } catch (error) {
    console.error("Command failed:", error);
    process.exit(1);
  }
  
  process.exit(0);
}

main();
