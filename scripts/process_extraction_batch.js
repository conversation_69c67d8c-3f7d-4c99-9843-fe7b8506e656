import { chromium } from "playwright";
import { createClient } from '@supabase/supabase-js';
import { rename, readdir, mkdir, stat } from "fs/promises";
import * as path from 'path';
import { fileURLToPath } from 'url';

// Get __dirname equivalent for ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// These should be environment variables in production
const SUPABASE_URL = "https://sqzqvxqcybghcgrpubsy.supabase.co";
const SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InNxenF2eHFjeWJnaGNncnB1YnN5Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0MTA5NTUxOCwiZXhwIjoyMDU2NjcxNTE4fQ.YN8lGh9PurkS8dq7a7zzlrgoETGyWltcJrLnOBI7t6M"; // DO NOT COMMIT with real key
const USER_CODE = "30F3315"; // DO NOT COMMIT with real credentials
const PASSWORD = "Sainte-Clara1504!"; // DO NOT COMMIT with real credentials
// Constants
const BASE_URL = "https://www.registrefoncier.gouv.qc.ca/Sirf/";
const ACTE_SEARCH_URL = "https://www.registrefoncier.gouv.qc.ca/Sirf/Script/13_01_11/pf_13_01_11_08_reqst.asp";
const INDEX_SEARCH_URL = "https://www.registrefoncier.gouv.qc.ca/Sirf/Script/13_01_11/pf_13_01_11_02_indx_immbl.asp";
const BASE_DOWNLOAD_DIR = path.join(__dirname, "downloads");
// Timeouts
const NAVIGATION_TIMEOUT = 60000; // 60 seconds
const DOWNLOAD_TIMEOUT = 30000; // 30 seconds
const FRAME_WAIT_TIMEOUT = 10000; // 10 seconds
class ExtractionBatchProcessor {
    constructor(debugMode = false) {
        this.debugMode = false;
        // Initialize Supabase client
        this.supabase = createClient(SUPABASE_URL, SUPABASE_KEY);
        this.debugMode = debugMode;
    }
    /**
     * Logger with debug mode support
     */
    log(message, level = 'info') {
        const timestamp = new Date().toISOString();
        if (level === 'error') {
            console.error(`[${timestamp}] ERROR: ${message}`);
        }
        else if (level === 'debug' && this.debugMode) {
            console.log(`[${timestamp}] DEBUG: ${message}`);
        }
        else if (level === 'info') {
            console.log(`[${timestamp}] INFO: ${message}`);
        }
    }
    /**
     * Wait for a frame to be available with retry logic
     */
    async waitForFrame(parent, frameName, timeout = FRAME_WAIT_TIMEOUT) {
        const startTime = Date.now();
        while (Date.now() - startTime < timeout) {
            try {
                const frameElement = await parent.waitForSelector(`frame[name="${frameName}"]`, { timeout: 1000 });
                const frame = await frameElement.contentFrame();
                if (frame) {
                    this.log(`Frame '${frameName}' found and loaded`, 'debug');
                    return frame;
                }
            }
            catch (e) {
                // Continue retrying
            }
            await parent.waitForTimeout(500);
        }
        throw new Error(`Timeout waiting for frame '${frameName}'`);
    }
    /**
     * Main method to run the batch processor
     */
    async run() {
        this.log("Starting extraction batch processor...");
        // Launch the browser
        const browser = await chromium.launch({
            headless: !this.debugMode,
            downloadsPath: BASE_DOWNLOAD_DIR
        });
        try {
            const page = await browser.newPage();
            page.setDefaultTimeout(NAVIGATION_TIMEOUT);
            // Initialize directories
            await this.initDirectories();
            // Perform login (one time)
            this.log("Logging in to the website...");
            await this.loginToWebsite(page);
            // Process queue items
            let keepProcessing = true;
            let processedCount = 0;
            while (keepProcessing) {
                // Get next en_attente item
                const queueItem = await this.getNextEnAttenteItem();
                if (!queueItem) {
                    this.log("No more pending items to process.");
                    break;
                }
                this.log(`Processing item ${queueItem.id} (${queueItem.document_source}: ${queueItem.document_number})...`);
                // Update item to En_traitement
                await this.updateItemStatus(queueItem.id, {
                    status: 'En_traitement',
                    processing_started_at: new Date().toISOString()
                });
                try {
                    // Process this specific item
                    const itemDownloadDir = path.join(BASE_DOWNLOAD_DIR, queueItem.id);
                    await mkdir(itemDownloadDir, { recursive: true });
                    // Extract document based on its type
                    let filePath;
                    if (queueItem.document_source === 'acte') {
                        filePath = await this.extractActeDocument(page, queueItem.document_number, itemDownloadDir, queueItem.circonscription_fonciere, queueItem.acte_type);
                    }
                    else {
                        filePath = await this.extractIndexDocument(page, queueItem.document_number, itemDownloadDir, queueItem.circonscription_fonciere, queueItem.cadastre, queueItem.designation_secondaire);
                    }
                    // Verify the file was actually downloaded and has content
                    const fileStats = await stat(filePath);
                    if (fileStats.size === 0) {
                        throw new Error("Downloaded file is empty");
                    }
                    // Update item with success
                    await this.updateItemStatus(queueItem.id, {
                        status: 'Telecharge',
                        local_file_path: filePath,
                        error_message: undefined // Clear any previous errors
                    });
                    this.log(`Successfully downloaded document for item ${queueItem.id} (${fileStats.size} bytes)`);
                    // Increment counter
                    processedCount++;
                    // Optional: limit batch size
                    if (processedCount >= 10) {
                        this.log("Reached batch limit. Exiting processing loop.");
                        keepProcessing = false;
                    }
                    // Add a small delay to avoid overwhelming the site
                    await page.waitForTimeout(2000 + Math.random() * 3000);
                }
                catch (error) {
                    // Always continue processing, never stop the entire batch
                    await this.handleDocumentError(queueItem, error, page);
                    // Continue with next item regardless of this error
                }
            }
            // Log out if needed
            await this.logout(page);
        }
        catch (error) {
            this.log(`Critical error in batch processor: ${error}`, 'error');
            throw error;
        }
        finally {
            await browser.close();
            this.log("Browser closed. Extraction batch complete.");
        }
    }
    /**
     * Initialize download directories
     */
    async initDirectories() {
        await mkdir(BASE_DOWNLOAD_DIR, { recursive: true });
    }
    /**
     * Log in to the website
     */
    async loginToWebsite(page) {
        try {
            await page.goto(BASE_URL);
            await page.getByRole('link', { name: 'Entrée du site' }).nth(1).click();
            await page.getByRole('textbox', { name: 'Code d\'utilisateur' }).click();
            await page.getByRole('textbox', { name: 'Code d\'utilisateur' }).fill(USER_CODE);
            await page.getByRole('textbox', { name: 'Mot de passe' }).click();
            await page.getByRole('textbox', { name: 'Mot de passe' }).fill(PASSWORD);
            await page.getByRole('button', { name: 'Soumettre' }).click();
            // Wait for login to complete
            await page.waitForLoadState('networkidle');
            await page.waitForTimeout(2000);
            // Check if login was successful by looking for error messages
            const loginErrorVisible = await page.locator('text=/Code invalide|Mot de passe invalide/i').isVisible().catch(() => false);
            if (loginErrorVisible) {
                throw new Error("Login failed: Invalid credentials");
            }
            this.log("Login successful");
        }
        catch (error) {
            this.log(`Login error: ${error}`, 'error');
            throw new Error(`Failed to log in: ${error instanceof Error ? error.message : String(error)}`);
        }
    }
    /**
     * Log out from the website
     */
    async logout(page) {
        try {
            // Add logout steps if the website has a logout function
            // await page.getByRole('link', { name: 'Déconnexion' }).click();
            this.log("Logout complete (or not needed)");
        }
        catch (error) {
            this.log(`Logout error: ${error}`, 'error');
            // Don't throw, as this isn't critical
        }
    }
    /**
     * Get the next pending item from the queue
     */
    async getNextEnAttenteItem() {
        try {
            const { data, error } = await this.supabase
                .from('extraction_queue')
                .select('*')
                .eq('status', 'En_attente')
                .order('created_at', { ascending: true })
                .limit(1)
                .single();
            if (error)
                throw error;
            return data;
        }
        catch (error) {
            if (error.code === 'PGRST116') {
                // No rows returned
                return null;
            }
            this.log(`Error fetching next pending item: ${error}`, 'error');
            throw error;
        }
    }
    /**
     * Update an item's status in the queue
     */
    async updateItemStatus(itemId, updates) {
        try {
            const { error } = await this.supabase
                .from('extraction_queue')
                .update(updates)
                .eq('id', itemId);
            if (error)
                throw error;
        }
        catch (error) {
            this.log(`Error updating status for item ${itemId}: ${error}`, 'error');
            throw error;
        }
    }
    /**
     * Handle document extraction errors by logging and updating database
     * Always continues processing, never stops the batch
     */
    async handleDocumentError(queueItem, error, page) {
        const newRetryCount = (queueItem.retry_count || 0) + 1;
        const errorMessage = error instanceof Error ? error.message : String(error);
        this.log(`Attempt ${newRetryCount}/3 failed for item ${queueItem.id}: ${errorMessage}`, 'error');
        // Take screenshot on error if in debug mode
        if (this.debugMode && page) {
            try {
                const screenshotPath = path.join(BASE_DOWNLOAD_DIR, `error_${queueItem.id}_attempt${newRetryCount}.png`);
                await page.screenshot({ path: screenshotPath, fullPage: true });
                this.log(`Error screenshot saved to ${screenshotPath}`, 'debug');
            }
            catch (screenshotError) {
                this.log(`Failed to take error screenshot: ${screenshotError}`, 'error');
            }
        }
        try {
            if (newRetryCount >= 3) {
                this.log(`Item ${queueItem.id} marked as Document_introuvable after 3 failed attempts`);
                await this.updateItemStatus(queueItem.id, {
                    retry_count: newRetryCount,
                    error_message: errorMessage,
                    status: 'Document_introuvable'
                });
            }
            else {
                this.log(`Item ${queueItem.id} will be retried (attempt ${newRetryCount}/3)`);
                await this.updateItemStatus(queueItem.id, {
                    retry_count: newRetryCount,
                    error_message: errorMessage,
                    status: 'En_attente'
                });
            }
        }
        catch (dbError) {
            this.log(`Failed to update error status for item ${queueItem.id}: ${dbError}`, 'error');
            // Continue anyway - don't let database errors stop the batch
        }
    }
    /**
     * Extract an acte document
     */
    async extractActeDocument(page, inscriptionNumber, downloadDir, circonscription, acteType) {
        try {
            this.log(`Extracting acte document: ${inscriptionNumber}`, 'debug');
            const finalActeType = acteType || 'Acte';
            // Navigate to the acte search page
            const page1Promise = page.waitForEvent('popup');
            await page.getByRole('link', { name: 'Consulter', exact: true }).click();
            const page1 = await page1Promise;
            // Wait for the popup to fully load
            await page1.waitForLoadState('domcontentloaded');
            await page1.getByRole('link', { name: 'Index des immeubles' }).click();
            const page2Promise = page.waitForEvent('popup');
            await page.getByRole('link', { name: 'Consulter', exact: true }).click();
            const page2 = await page2Promise;
            await page2.waitForLoadState('domcontentloaded');
            await page2.getByRole('link', { name: 'Acte au long, radiation, avis' }).click();
            // Wait for the search form to be ready
            await page.waitForTimeout(2000);
            await page.waitForLoadState('networkidle');
            // Handle circonscription selection if provided
            if (circonscription) {
                try {
                    const selectElement = page.locator('#selCircnFoncr');
                    const optionExists = await selectElement.locator(`option:has-text("${circonscription}")`).count() > 0;
                    if (!optionExists) {
                        throw new Error(`Circonscription "${circonscription}" not found in dropdown`);
                    }
                    await selectElement.selectOption({ label: circonscription });
                } catch (error) {
                    throw new Error(`Could not select circonscription "${circonscription}": ${error.message}`);
                }
            }
            // Handle acte type selection
            switch (finalActeType) {
                case 'Radiation':
                    await page.locator('#radRdrtn').click();
                    break;
                case 'Avis d\'adresse':
                    await page.locator('#radAvis').click();
                    break;
                case 'Acte divers':
                    await page.locator('#radAct').click();
                    break;
                case 'Acte':
                default:
                    await page.locator('#radAct').click();
                    break;
            }
            // Enter the inscription number
            try {
                this.log(`Attempting to fill inscription number field with: ${inscriptionNumber}`, 'debug');
                
                // Try multiple selectors for the inscription number field
                let inscriptionField = null;
                const selectors = [
                    { type: 'id-txtNoReqst', selector: page.locator('#txtNoReqst') },
                    { type: 'name-txtNoReqst', selector: page.locator('input[name="txtNoReqst"]') },
                    { type: 'class-ChampStand', selector: page.locator('input.ChampStand[name="txtNoReqst"]') },
                    { type: 'role', selector: page.getByRole('textbox', { name: 'Numéro d\'inscription' }) },
                    { type: 'label', selector: page.getByLabel('Numéro d\'inscription') },
                    { type: 'placeholder', selector: page.getByPlaceholder('Numéro d\'inscription') },
                    { type: 'id-txtNumInscr', selector: page.locator('#txtNumInscr') },
                    { type: 'name-inscr', selector: page.locator('input[name*="inscr"]') },
                    { type: 'generic', selector: page.locator('input[type="text"]').first() }
                ];
                
                for (const selectorInfo of selectors) {
                    try {
                        await selectorInfo.selector.waitFor({ timeout: 2000 });
                        inscriptionField = selectorInfo.selector;
                        this.log(`Found inscription field using ${selectorInfo.type} selector`, 'debug');
                        break;
                    } catch (e) {
                        this.log(`Failed to find field with ${selectorInfo.type} selector`, 'debug');
                    }
                }
                
                if (!inscriptionField) {
                    throw new Error("Could not locate the 'Numéro d'inscription' field with any selector");
                }
                
                // Clear field first, then fill
                await inscriptionField.click();
                await inscriptionField.clear();
                await inscriptionField.fill(inscriptionNumber);
                
                // Verify the field was filled correctly
                const fieldValue = await inscriptionField.inputValue();
                if (fieldValue !== inscriptionNumber) {
                    throw new Error(`Field was not filled correctly. Expected: ${inscriptionNumber}, Got: ${fieldValue}`);
                }
                
                this.log(`Successfully filled inscription field with: ${fieldValue}`, 'debug');
                await page.waitForTimeout(1000);
                
            } catch (error) {
                throw new Error(`Failed to fill inscription number field: ${error.message}`);
            }
            this.log(`Searching for acte: ${inscriptionNumber}`, 'debug');
            // Search and wait for results
            await page.getByRole('button', { name: 'Rechercher' }).click();
            // Wait for the page to process the search
            await page.waitForLoadState('networkidle');
            await page.waitForTimeout(3000); // Additional wait for dynamic content
            // Check for error messages
            const errorTexts = [
                'Aucun document',
                'introuvable',
                'n\'existe pas',
                'Erreur',
                'Invalid'
            ];
            for (const errorText of errorTexts) {
                const hasError = await page.locator(`text=/${errorText}/i`).isVisible().catch(() => false);
                if (hasError) {
                    throw new Error(`Document not found: Page contains "${errorText}"`);
                }
            }
            // Wait for frames to be available
            this.log('Waiting for result frames...', 'debug');
            const pageFrame = await this.waitForFrame(page, 'page');
            if (!pageFrame)
                throw new Error('Page frame not found');
            const navFrame = await this.waitForFrame(pageFrame, 'frmNavgt');
            if (!navFrame)
                throw new Error('Navigation frame not found');
            // Wait for the print button to be available
            await navFrame.waitForSelector('a:has-text("Imprimer")', { state: 'visible', timeout: 10000 });
            // Click the print button
            await navFrame.getByRole('link', { name: 'Imprimer' }).click();
            // Wait for download to complete
            this.log('Waiting for download to complete...', 'debug');
            await page.waitForTimeout(DOWNLOAD_TIMEOUT);
            // Check if a file was downloaded
            const files = await readdir(downloadDir);
            if (files.length === 0) {
                throw new Error("No document was downloaded - the document may not exist or may require different search parameters");
            }
            // Find the PDF file (ignore hidden files and non-PDF files)
            const pdfFile = files.find(f => f.endsWith('.pdf') && !f.startsWith('.'));
            if (!pdfFile) {
                throw new Error("No PDF file was downloaded");
            }
            // Rename the file
            const sourcePath = path.join(downloadDir, pdfFile);
            const targetPath = path.join(downloadDir, `${inscriptionNumber}.pdf`);
            await rename(sourcePath, targetPath);
            this.log(`Acte document downloaded successfully: ${targetPath}`, 'debug');
            return targetPath;
        }
        catch (error) {
            this.log(`Error extracting acte document: ${error}`, 'error');
            throw error;
        }
    }
    /**
     * Extract an index document
     */
    async extractIndexDocument(page, lotNumber, downloadDir, circonscription, cadastre, designationSecondaire) {
        try {
            this.log(`Extracting index document: ${lotNumber}`, 'debug');
            // Navigate to the index search page
            const page1Promise = page.waitForEvent('popup');
            await page.getByRole('link', { name: 'Consulter', exact: true }).click();
            const page1 = await page1Promise;
            await page1.waitForLoadState('domcontentloaded');
            await page1.getByRole('link', { name: 'Index des immeubles' }).click();
            // Wait for the search form to be ready
            await page.waitForTimeout(2000);
            await page.waitForLoadState('networkidle');
            // Handle circonscription selection if provided
            if (circonscription) {
                try {
                    const selectElement = page.locator('#selCircnFoncr');
                    const optionExists = await selectElement.locator(`option:has-text("${circonscription}")`).count() > 0;
                    if (!optionExists) {
                        throw new Error(`Circonscription "${circonscription}" not found in dropdown`);
                    }
                    await selectElement.selectOption({ label: circonscription });
                } catch (error) {
                    throw new Error(`Could not select circonscription "${circonscription}": ${error.message}`);
                }
            }
            // Fill in cadastre if provided
            if (cadastre) {
                try {
                    const cadastreSelect = page.getByLabel('Cadastre');
                    const optionExists = await cadastreSelect.locator(`option:has-text("${cadastre}")`).count() > 0;
                    if (!optionExists) {
                        throw new Error(`Cadastre "${cadastre}" not found in dropdown`);
                    }
                    await cadastreSelect.selectOption({ label: cadastre });
                } catch (error) {
                    throw new Error(`Could not select cadastre "${cadastre}": ${error.message}`);
                }
            }
            // Fill in designation secondaire if provided
            if (designationSecondaire) {
                try {
                    await page.getByLabel('Désignation secondaire').fill(designationSecondaire);
                } catch (error) {
                    throw new Error(`Could not fill designation secondaire "${designationSecondaire}": ${error.message}`);
                }
            }
            // Enter the lot number
            await page.getByRole('textbox', { name: 'Numéro de lot' }).click();
            await page.getByRole('textbox', { name: 'Numéro de lot' }).fill(lotNumber);
            this.log(`Searching for index: ${lotNumber}`, 'debug');
            // Search and wait for results
            await page.getByRole('button', { name: 'Soumettre' }).click();
            // Wait for the page to process the search
            await page.waitForLoadState('networkidle');
            await page.waitForTimeout(3000); // Additional wait for dynamic content
            // Check for error messages
            const errorTexts = [
                'Aucun lot',
                'introuvable',
                'n\'existe pas',
                'Erreur',
                'Invalid'
            ];
            for (const errorText of errorTexts) {
                const hasError = await page.locator(`text=/${errorText}/i`).isVisible().catch(() => false);
                if (hasError) {
                    throw new Error(`Lot not found: Page contains "${errorText}"`);
                }
            }
            // Wait for frames to be available
            this.log('Waiting for result frames...', 'debug');
            const pageFrame = await this.waitForFrame(page, 'page');
            if (!pageFrame)
                throw new Error('Page frame not found');
            const navFrame = await this.waitForFrame(pageFrame, 'frmNavgt');
            if (!navFrame)
                throw new Error('Navigation frame not found');
            // Wait for the print button to be available
            await navFrame.waitForSelector('a:has-text("Imprimer")', { state: 'visible', timeout: 10000 });
            // Click the print button
            await navFrame.getByRole('link', { name: 'Imprimer' }).click();
            // Wait for download to complete
            this.log('Waiting for download to complete...', 'debug');
            await page.waitForTimeout(DOWNLOAD_TIMEOUT);
            // Check if a file was downloaded
            const files = await readdir(downloadDir);
            if (files.length === 0) {
                throw new Error("No document was downloaded - the lot may not exist or may require different search parameters");
            }
            // Find the PDF file (ignore hidden files and non-PDF files)
            const pdfFile = files.find(f => f.endsWith('.pdf') && !f.startsWith('.'));
            if (!pdfFile) {
                throw new Error("No PDF file was downloaded");
            }
            // Rename the file
            const sourcePath = path.join(downloadDir, pdfFile);
            const targetPath = path.join(downloadDir, `${lotNumber}.pdf`);
            await rename(sourcePath, targetPath);
            this.log(`Index document downloaded successfully: ${targetPath}`, 'debug');
            return targetPath;
        }
        catch (error) {
            this.log(`Error extracting index document: ${error}`, 'error');
            throw error;
        }
    }
}
// Run the processor
const debugMode = process.argv.includes('--debug') || process.argv.includes('--headed');
const processor = new ExtractionBatchProcessor(debugMode);
processor.run()
    .then(() => {
    console.log("Batch process completed successfully");
    process.exit(0);
})
    .catch((error) => {
    console.error("Batch process failed:", error);
    process.exit(1);
});
