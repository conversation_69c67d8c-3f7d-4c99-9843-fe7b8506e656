import { chromium, Page, Frame } from "playwright";
import { createClient, SupabaseClient } from '@supabase/supabase-js';
import { rename, readdir, mkdir, stat } from "fs/promises";
import * as path from 'path';

// These should be environment variables in production
const SUPABASE_URL = "https://sqzqvxqcybghcgrpubsy.supabase.co";
const SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InNxenF2eHFjeWJnaGNncnB1YnN5Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0MTA5NTUxOCwiZXhwIjoyMDU2NjcxNTE4fQ.YN8lGh9PurkS8dq7a7zzlrgoETGyWltcJrLnOBI7t6M"; // DO NOT COMMIT with real key
const USER_CODE = "30F3315"; // DO NOT COMMIT with real credentials
const PASSWORD = "Sainte-Clara1504!"; // DO NOT COMMIT with real credentials

// Constants
const BASE_URL = "https://www.registrefoncier.gouv.qc.ca/Sirf/";
const ACTE_SEARCH_URL = "https://www.registrefoncier.gouv.qc.ca/Sirf/Script/13_01_11/pf_13_01_11_08_reqst.asp";
const INDEX_SEARCH_URL = "https://www.registrefoncier.gouv.qc.ca/Sirf/Script/13_01_11/pf_13_01_11_02_indx_immbl.asp";
const BASE_DOWNLOAD_DIR = path.join(__dirname, "downloads");

// Timeouts
const NAVIGATION_TIMEOUT = 60000; // 60 seconds
const DOWNLOAD_TIMEOUT = 30000; // 30 seconds
const FRAME_WAIT_TIMEOUT = 10000; // 10 seconds

// Define the structure of an extraction queue item
interface ExtractionQueueItem {
  id: string;
  document_source: 'acte' | 'index';
  document_number: string;
  circonscription_fonciere?: string;
  acte_type?: 'Acte' | 'Avis d\'adresse' | 'Radiation' | 'Acte divers';
  cadastre?: string;
  designation_secondaire?: string;
  status: 'En_attente' | 'En_traitement' | 'Telecharge' | 'Disponible_sur_Drive' | 'Document_introuvable';
  retry_count: number;
  local_file_path?: string;
  error_message?: string;
  processing_started_at?: string;
}

class ExtractionBatchProcessor {
  private supabase: SupabaseClient;
  private debugMode: boolean = false;
  
  constructor(debugMode: boolean = false) {
    // Initialize Supabase client
    this.supabase = createClient(SUPABASE_URL, SUPABASE_KEY);
    this.debugMode = debugMode;
  }

  /**
   * Logger with debug mode support
   */
  private log(message: string, level: 'info' | 'error' | 'debug' = 'info'): void {
    const timestamp = new Date().toISOString();
    if (level === 'error') {
      console.error(`[${timestamp}] ERROR: ${message}`);
    } else if (level === 'debug' && this.debugMode) {
      console.log(`[${timestamp}] DEBUG: ${message}`);
    } else if (level === 'info') {
      console.log(`[${timestamp}] INFO: ${message}`);
    }
  }

  /**
   * Wait for a frame to be available with retry logic
   */
  private async waitForFrame(parent: Page | Frame, frameName: string, timeout: number = FRAME_WAIT_TIMEOUT): Promise<Frame | null> {
    const startTime = Date.now();
    
    while (Date.now() - startTime < timeout) {
      try {
        const frameElement = await parent.waitForSelector(`frame[name="${frameName}"]`, { timeout: 1000 });
        const frame = await frameElement.contentFrame();
        if (frame) {
          this.log(`Frame '${frameName}' found and loaded`, 'debug');
          return frame;
        }
      } catch (e) {
        // Continue retrying
      }
      await parent.waitForTimeout(500);
    }
    
    throw new Error(`Timeout waiting for frame '${frameName}'`);
  }

  /**
   * Main method to run the batch processor
   */
  public async run(): Promise<void> {
    this.log("Starting extraction batch processor...");
    
    // Launch the browser
    const browser = await chromium.launch({
      headless: !this.debugMode,
      downloadsPath: BASE_DOWNLOAD_DIR
    });
    
    try {
      const page = await browser.newPage();
      page.setDefaultTimeout(NAVIGATION_TIMEOUT);
      
      // Initialize directories
      await this.initDirectories();
      
      // Perform login (one time)
      this.log("Logging in to the website...");
      await this.loginToWebsite(page);
      
      // Process queue items
      let keepProcessing = true;
      let processedCount = 0;
      
      while (keepProcessing) {
        // Get next en_attente item
        const queueItem = await this.getNextEnAttenteItem();
        
        if (!queueItem) {
          this.log("No more pending items to process.");
          break;
        }
        
        this.log(`Processing item ${queueItem.id} (${queueItem.document_source}: ${queueItem.document_number})...`);
        
        // Update item to En_traitement
        await this.updateItemStatus(queueItem.id, {
          status: 'En_traitement',
          processing_started_at: new Date().toISOString()
        });
        
        try {
          // Process this specific item
          const itemDownloadDir = path.join(BASE_DOWNLOAD_DIR, queueItem.id);
          await mkdir(itemDownloadDir, { recursive: true });
          
          // Extract document based on its type
          let filePath;
          if (queueItem.document_source === 'acte') {
            filePath = await this.extractActeDocument(
              page, 
              queueItem.document_number,
              itemDownloadDir,
              queueItem.circonscription_fonciere,
              queueItem.acte_type
            );
          } else {
            filePath = await this.extractIndexDocument(
              page,
              queueItem.document_number,
              itemDownloadDir,
              queueItem.circonscription_fonciere,
              queueItem.cadastre,
              queueItem.designation_secondaire
            );
          }
          
          // Verify the file was actually downloaded and has content
          const fileStats = await stat(filePath);
          if (fileStats.size === 0) {
            throw new Error("Downloaded file is empty");
          }
          
          // Update item with success
          await this.updateItemStatus(queueItem.id, {
            status: 'Telecharge',
            local_file_path: filePath,
            error_message: undefined // Clear any previous errors
          });
          
          this.log(`Successfully downloaded document for item ${queueItem.id} (${fileStats.size} bytes)`);
          
          // Increment counter
          processedCount++;
          
          // Optional: limit batch size
          if (processedCount >= 10) {
            this.log("Reached batch limit. Exiting processing loop.");
            keepProcessing = false;
          }
          
          // Add a small delay to avoid overwhelming the site
          await page.waitForTimeout(2000 + Math.random() * 3000);
          
        } catch (error) {
          const newRetryCount = (queueItem.retry_count || 0) + 1;
          const errorMessage = error instanceof Error ? error.message : String(error);
          
          this.log(`Attempt ${newRetryCount}/3 failed for item ${queueItem.id}: ${errorMessage}`, 'error');
          
          // Take screenshot on error if in debug mode
          if (this.debugMode) {
            try {
              const screenshotPath = path.join(BASE_DOWNLOAD_DIR, `error_${queueItem.id}_attempt${newRetryCount}.png`);
              await page.screenshot({ path: screenshotPath, fullPage: true });
              this.log(`Error screenshot saved to ${screenshotPath}`, 'debug');
            } catch (screenshotError) {
              this.log(`Failed to take error screenshot: ${screenshotError}`, 'error');
            }
          }
          
          if (newRetryCount >= 3) {
            this.log(`Item ${queueItem.id} marked as Document_introuvable after 3 failed attempts`);
            await this.updateItemStatus(queueItem.id, {
              retry_count: newRetryCount,
              error_message: errorMessage,
              status: 'Document_introuvable'
            });
          } else {
            this.log(`Item ${queueItem.id} will be retried (attempt ${newRetryCount}/3)`);
            await this.updateItemStatus(queueItem.id, {
              retry_count: newRetryCount,
              error_message: errorMessage,
              status: 'En_attente'
            });
          }
        }
      }
      
      // Log out if needed
      await this.logout(page);
      
    } catch (error) {
      this.log(`Critical error in batch processor: ${error}`, 'error');
      throw error;
    } finally {
      await browser.close();
      this.log("Browser closed. Extraction batch complete.");
    }
  }
  
  /**
   * Initialize download directories
   */
  private async initDirectories(): Promise<void> {
    await mkdir(BASE_DOWNLOAD_DIR, { recursive: true });
  }
  
  /**
   * Log in to the website
   */
  private async loginToWebsite(page: Page): Promise<void> {
    try {
      await page.goto(BASE_URL);
      await page.getByRole('link', { name: 'Entrée du site' }).nth(1).click();
      await page.getByRole('textbox', { name: 'Code d\'utilisateur' }).click();
      await page.getByRole('textbox', { name: 'Code d\'utilisateur' }).fill(USER_CODE);
      await page.getByRole('textbox', { name: 'Mot de passe' }).click();
      await page.getByRole('textbox', { name: 'Mot de passe' }).fill(PASSWORD);
      await page.getByRole('button', { name: 'Soumettre' }).click();
      
      // Wait for login to complete
      await page.waitForLoadState('networkidle');
      await page.waitForTimeout(2000);
      
      // Check if login was successful by looking for error messages
      const loginErrorVisible = await page.locator('text=/Code invalide|Mot de passe invalide/i').isVisible().catch(() => false);
      if (loginErrorVisible) {
        throw new Error("Login failed: Invalid credentials");
      }
      
      this.log("Login successful");
    } catch (error) {
      this.log(`Login error: ${error}`, 'error');
      throw new Error(`Failed to log in: ${error instanceof Error ? error.message : String(error)}`);
    }
  }
  
  /**
   * Log out from the website
   */
  private async logout(page: Page): Promise<void> {
    try {
      // Add logout steps if the website has a logout function
      // await page.getByRole('link', { name: 'Déconnexion' }).click();
      this.log("Logout complete (or not needed)");
    } catch (error) {
      this.log(`Logout error: ${error}`, 'error');
      // Don't throw, as this isn't critical
    }
  }
  
  /**
   * Get the next pending item from the queue
   */
  private async getNextEnAttenteItem(): Promise<ExtractionQueueItem | null> {
    try {
      const { data, error } = await this.supabase
        .from('extraction_queue')
        .select('*')
        .eq('status', 'En_attente')
        .order('created_at', { ascending: true })
        .limit(1)
        .single();
      
      if (error) throw error;
      return data as ExtractionQueueItem;
    } catch (error: any) {
      if (error.code === 'PGRST116') {
        // No rows returned
        return null;
      }
      this.log(`Error fetching next pending item: ${error}`, 'error');
      throw error;
    }
  }
  
  /**
   * Update an item's status in the queue
   */
  private async updateItemStatus(itemId: string, updates: Partial<ExtractionQueueItem>): Promise<void> {
    try {
      const { error } = await this.supabase
        .from('extraction_queue')
        .update(updates)
        .eq('id', itemId);
      
      if (error) throw error;
    } catch (error) {
      this.log(`Error updating status for item ${itemId}: ${error}`, 'error');
      throw error;
    }
  }
  
  /**
   * Extract an acte document
   */
  private async extractActeDocument(
    page: Page,
    inscriptionNumber: string,
    downloadDir: string,
    circonscription?: string,
    acteType?: string
  ): Promise<string> {
    try {
      this.log(`Extracting acte document: ${inscriptionNumber}`, 'debug');
      const finalActeType = acteType || 'Acte';
      
      // Navigate to the acte search page
      const page1Promise = page.waitForEvent('popup');
      await page.getByRole('link', { name: 'Consulter', exact: true }).click();
      const page1 = await page1Promise;
      
      // Wait for the popup to fully load
      await page1.waitForLoadState('domcontentloaded');
      await page1.getByRole('link', { name: 'Index des immeubles' }).click();

      const page2Promise = page.waitForEvent('popup');
      await page.getByRole('link', { name: 'Consulter', exact: true }).click();
      const page2 = await page2Promise;
      
      await page2.waitForLoadState('domcontentloaded');
      await page2.getByRole('link', { name: 'Acte au long, radiation, avis' }).click();
      
      // Wait for the search form to be ready
      await page.waitForTimeout(2000);
      await page.waitForLoadState('networkidle');
      
      // Handle circonscription selection if provided
      if (circonscription) {
        const selectElement = page.locator('#selCircnFoncr');
        await selectElement.selectOption({ label: circonscription });
      }
      
      // Handle acte type selection
      switch (finalActeType) {
        case 'Radiation':
          await page.locator('#radRdrtn').click();
          break;
        case 'Avis d\'adresse':
          await page.locator('#radAvis').click();
          break;
        case 'Acte divers':
          await page.locator('#radAct').click();
          break;
        case 'Acte':
        default:
          await page.locator('#radAct').click();
          break;
      }
      
      // Enter the inscription number
      await page.getByRole('textbox', { name: 'Numéro d\'inscription' }).click();
      await page.getByRole('textbox', { name: 'Numéro d\'inscription' }).fill(inscriptionNumber);
      await page.waitForTimeout(1000);
      
      this.log(`Searching for acte: ${inscriptionNumber}`, 'debug');
      
      // Search and wait for results
      await page.getByRole('button', { name: 'Rechercher' }).click();
      
      // Wait for the page to process the search
      await page.waitForLoadState('networkidle');
      await page.waitForTimeout(3000); // Additional wait for dynamic content
      
      // Check for error messages
      const errorTexts = [
        'Aucun document',
        'introuvable',
        'n\'existe pas',
        'Erreur',
        'Invalid'
      ];
      
      for (const errorText of errorTexts) {
        const hasError = await page.locator(`text=/${errorText}/i`).isVisible().catch(() => false);
        if (hasError) {
          throw new Error(`Document not found: Page contains "${errorText}"`);
        }
      }
      
      // Wait for frames to be available
      this.log('Waiting for result frames...', 'debug');
      const pageFrame = await this.waitForFrame(page, 'page');
      if (!pageFrame) throw new Error('Page frame not found');
      
      const navFrame = await this.waitForFrame(pageFrame, 'frmNavgt');
      if (!navFrame) throw new Error('Navigation frame not found');
      
      // Wait for the print button to be available
      await navFrame.waitForSelector('a:has-text("Imprimer")', { state: 'visible', timeout: 10000 });
      
      // Click the print button
      await navFrame.getByRole('link', { name: 'Imprimer' }).click();
      
      // Wait for download to complete
      this.log('Waiting for download to complete...', 'debug');
      await page.waitForTimeout(DOWNLOAD_TIMEOUT);
      
      // Check if a file was downloaded
      const files = await readdir(downloadDir);
      
      if (files.length === 0) {
        throw new Error("No document was downloaded - the document may not exist or may require different search parameters");
      }
      
      // Find the PDF file (ignore hidden files and non-PDF files)
      const pdfFile = files.find(f => f.endsWith('.pdf') && !f.startsWith('.'));
      if (!pdfFile) {
        throw new Error("No PDF file was downloaded");
      }
      
      // Rename the file
      const sourcePath = path.join(downloadDir, pdfFile);
      const targetPath = path.join(downloadDir, `${inscriptionNumber}.pdf`);
      await rename(sourcePath, targetPath);
      
      this.log(`Acte document downloaded successfully: ${targetPath}`, 'debug');
      return targetPath;
    } catch (error) {
      this.log(`Error extracting acte document: ${error}`, 'error');
      throw error;
    }
  }
  
  /**
   * Extract an index document
   */
  private async extractIndexDocument(
    page: Page,
    lotNumber: string,
    downloadDir: string,
    circonscription?: string,
    cadastre?: string,
    designationSecondaire?: string
  ): Promise<string> {
    try {
      this.log(`Extracting index document: ${lotNumber}`, 'debug');
      
      // Navigate to the index search page
      const page1Promise = page.waitForEvent('popup');
      await page.getByRole('link', { name: 'Consulter', exact: true }).click();
      const page1 = await page1Promise;
      
      await page1.waitForLoadState('domcontentloaded');
      await page1.getByRole('link', { name: 'Index des immeubles' }).click();
      
      // Wait for the search form to be ready
      await page.waitForTimeout(2000);
      await page.waitForLoadState('networkidle');
      
      // Handle circonscription selection if provided
      if (circonscription) {
        const selectElement = page.locator('#selCircnFoncr');
        await selectElement.selectOption({ label: circonscription });
      }
      
      // Fill in cadastre if provided
      if (cadastre) {
        await page.getByLabel('Cadastre').selectOption({ label: cadastre });
      }
      
      // Fill in designation secondaire if provided
      if (designationSecondaire) {
        await page.getByLabel('Désignation secondaire').fill(designationSecondaire);
      }
      
      // Enter the lot number
      await page.getByRole('textbox', { name: 'Numéro de lot' }).click();
      await page.getByRole('textbox', { name: 'Numéro de lot' }).fill(lotNumber);
      
      this.log(`Searching for index: ${lotNumber}`, 'debug');
      
      // Search and wait for results
      await page.getByRole('button', { name: 'Soumettre' }).click();
      
      // Wait for the page to process the search
      await page.waitForLoadState('networkidle');
      await page.waitForTimeout(3000); // Additional wait for dynamic content
      
      // Check for error messages
      const errorTexts = [
        'Aucun lot',
        'introuvable',
        'n\'existe pas',
        'Erreur',
        'Invalid'
      ];
      
      for (const errorText of errorTexts) {
        const hasError = await page.locator(`text=/${errorText}/i`).isVisible().catch(() => false);
        if (hasError) {
          throw new Error(`Lot not found: Page contains "${errorText}"`);
        }
      }
      
      // Wait for frames to be available
      this.log('Waiting for result frames...', 'debug');
      const pageFrame = await this.waitForFrame(page, 'page');
      if (!pageFrame) throw new Error('Page frame not found');
      
      const navFrame = await this.waitForFrame(pageFrame, 'frmNavgt');
      if (!navFrame) throw new Error('Navigation frame not found');
      
      // Wait for the print button to be available
      await navFrame.waitForSelector('a:has-text("Imprimer")', { state: 'visible', timeout: 10000 });
      
      // Click the print button
      await navFrame.getByRole('link', { name: 'Imprimer' }).click();
      
      // Wait for download to complete
      this.log('Waiting for download to complete...', 'debug');
      await page.waitForTimeout(DOWNLOAD_TIMEOUT);
      
      // Check if a file was downloaded
      const files = await readdir(downloadDir);
      
      if (files.length === 0) {
        throw new Error("No document was downloaded - the lot may not exist or may require different search parameters");
      }
      
      // Find the PDF file (ignore hidden files and non-PDF files)
      const pdfFile = files.find(f => f.endsWith('.pdf') && !f.startsWith('.'));
      if (!pdfFile) {
        throw new Error("No PDF file was downloaded");
      }
      
      // Rename the file
      const sourcePath = path.join(downloadDir, pdfFile);
      const targetPath = path.join(downloadDir, `${lotNumber}.pdf`);
      await rename(sourcePath, targetPath);
      
      this.log(`Index document downloaded successfully: ${targetPath}`, 'debug');
      return targetPath;
    } catch (error) {
      this.log(`Error extracting index document: ${error}`, 'error');
      throw error;
    }
  }
}

// Run the processor
const debugMode = process.argv.includes('--debug') || process.argv.includes('--headed');
const processor = new ExtractionBatchProcessor(debugMode);

processor.run()
  .then(() => {
    console.log("Batch process completed successfully");
    process.exit(0);
  })
  .catch((error) => {
    console.error("Batch process failed:", error);
    process.exit(1);
  });
