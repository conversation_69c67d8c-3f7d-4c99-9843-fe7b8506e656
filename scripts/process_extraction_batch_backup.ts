import { chromium, Page } from "playwright";
import { createClient, SupabaseClient } from '@supabase/supabase-js';
import { rename, readdir, mkdir } from "fs/promises";
import path from 'path';
import fs from 'fs';

// These should be environment variables in production
const SUPABASE_URL = "https://sqzqvxqcybghcgrpubsy.supabase.co";
const SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InNxenF2eHFjeWJnaGNncnB1YnN5Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0MTA5NTUxOCwiZXhwIjoyMDU2NjcxNTE4fQ.YN8lGh9PurkS8dq7a7zzlrgoETGyWltcJrLnOBI7t6M"; // DO NOT COMMIT with real key
const USER_CODE = "30F3315"; // DO NOT COMMIT with real credentials
const PASSWORD = "Sainte-Clara1504!"; // DO NOT COMMIT with real credentials

// Constants
const BASE_URL = "https://www.registrefoncier.gouv.qc.ca/Sirf/";
const ACTE_SEARCH_URL = "https://www.registrefoncier.gouv.qc.ca/Sirf/Script/13_01_11/pf_13_01_11_08_reqst.asp";
const INDEX_SEARCH_URL = "https://www.registrefoncier.gouv.qc.ca/Sirf/Script/13_01_11/pf_13_01_11_02_indx_immbl.asp";
const BASE_DOWNLOAD_DIR = path.join(__dirname, "downloads");

// Define the structure of an extraction queue item
interface ExtractionQueueItem {
  id: string;
  document_source: 'acte' | 'index';
  document_number: string;
  circonscription_fonciere?: string;
  acte_type?: 'Acte' | 'Avis d\'adresse' | 'Radiation' | 'Acte divers';
  cadastre?: string;
  designation_secondaire?: string;
  status: 'En_attente' | 'En_traitement' | 'Telecharge' | 'Disponible_sur_drive' | 'Document_introuvable';
  retry_count: number;
  local_file_path?: string;
  error_message?: string;
  processing_started_at?: string;
}

class ExtractionBatchProcessor {
  private supabase: SupabaseClient;
  
  constructor() {
    // Initialize Supabase client
    this.supabase = createClient(SUPABASE_URL, SUPABASE_KEY);
  }

  /**
   * Main method to run the batch processor
   */
  public async run(): Promise<void> {
    console.log("Starting extraction batch processor...");
    
    // Launch the browser
    const browser = await chromium.launch({
      headless: true, // Set to false for debugging
      downloadsPath: BASE_DOWNLOAD_DIR // Set global downloads path
    });
    
    try {
      const page = await browser.newPage();
      
      // Initialize directories
      await this.initDirectories();
      
      // Perform login (one time)
      console.log("Logging in to the website...");
      await this.loginToWebsite(page);
      
      // Process queue items
      let keepProcessing = true;
      let processedCount = 0;
      
      while (keepProcessing) {
        // Get next en_attente item
        const queueItem = await this.getNextEnAttenteItem();
        
        if (!queueItem) {
          console.log("No more pending items to process.");
          break;
        }
        
        console.log(`Processing item ${queueItem.id} (${queueItem.document_source}: ${queueItem.document_number})...`);
        
        // Update item to En_traitement
        await this.updateItemStatus(queueItem.id, {
          status: 'En_traitement',
          processing_started_at: new Date().toISOString()
        });
        
        try {
          // Process this specific item
          const itemDownloadDir = path.join(BASE_DOWNLOAD_DIR, queueItem.id);
          await mkdir(itemDownloadDir, { recursive: true });
          
          // Extract document based on its type
          let filePath;
          if (queueItem.document_source === 'acte') {
            filePath = await this.extractActeDocument(
              page, 
              queueItem.document_number,
              itemDownloadDir,
              queueItem.circonscription_fonciere,
              queueItem.acte_type
            );
          } else {
            filePath = await this.extractIndexDocument(
              page,
              queueItem.document_number,
              itemDownloadDir,
              queueItem.circonscription_fonciere,
              queueItem.cadastre,
              queueItem.designation_secondaire
            );
          }
          
          // Update item with success
          await this.updateItemStatus(queueItem.id, {
            status: 'Telecharge',
            local_file_path: filePath
          });
          
          console.log(`Successfully downloaded document for item ${queueItem.id}`);
          
          // Increment counter
          processedCount++;
          
          // Optional: limit batch size
          if (processedCount >= 10) {
            console.log("Reached batch limit. Exiting processing loop.");
            keepProcessing = false;
          }
          
          // Add a small delay to avoid overwhelming the site
          await page.waitForTimeout(2000 + Math.random() * 3000);
          
        } catch (error) {
          const newRetryCount = (queueItem.retry_count || 0) + 1;
          
          console.log(`📝 Attempt ${newRetryCount}/3 failed for item ${queueItem.id}: ${error.message}`);
          
          if (newRetryCount >= 3) {
            console.log(`❌ Item ${queueItem.id} marked as Document_introuvable after 3 failed attempts`);
            await this.updateItemStatus(queueItem.id, {
              retry_count: newRetryCount,
              error_message: error.message,
              status: 'Document_introuvable'
            });
          } else {
            console.log(`🔄 Item ${queueItem.id} will be retried (attempt ${newRetryCount}/3)`);
            await this.updateItemStatus(queueItem.id, {
              retry_count: newRetryCount,
              error_message: error.message,
              status: 'En_attente'
            });
          }
        }
      }
      
      // Log out if needed
      await this.logout(page);
      
    } catch (error) {
      console.error("Critical error in batch processor:", error);
      throw error;
    } finally {
      await browser.close();
      console.log("Browser closed. Extraction batch complete.");
    }
  }
  
  /**
   * Initialize download directories
   */
  private async initDirectories(): Promise<void> {
    await mkdir(BASE_DOWNLOAD_DIR, { recursive: true });
  }
  
  /**
   * Log in to the website
   */
  private async loginToWebsite(page: Page): Promise<void> {
    try {
      await page.goto(BASE_URL);
      await page.getByRole('link', { name: 'Entrée du site' }).nth(1).click();
      await page.getByRole('textbox', { name: 'Code d\'utilisateur' }).click();
      await page.getByRole('textbox', { name: 'Code d\'utilisateur' }).fill(USER_CODE);
      await page.getByRole('textbox', { name: 'Mot de passe' }).click();
      await page.getByRole('textbox', { name: 'Mot de passe' }).fill(PASSWORD);
      await page.getByRole('button', { name: 'Soumettre' }).click();
      
      // Wait for login to complete
      await page.waitForTimeout(2000);
      
      // Check if login was successful
      const loginError = await page.getByText('Code invalide').isVisible();
      if (loginError) {
        throw new Error("Login failed: Invalid credentials");
      }
      
      console.log("Login successful");
    } catch (error) {
      console.error("Login error:", error);
      throw new Error(`Failed to log in: ${error.message}`);
    }
  }
  
  /**
   * Log out from the website
   */
  private async logout(page: Page): Promise<void> {
    try {
      // Add logout steps if the website has a logout function
      // await page.getByRole('link', { name: 'Déconnexion' }).click();
      console.log("Logout complete (or not needed)");
    } catch (error) {
      console.error("Logout error:", error);
      // Don't throw, as this isn't critical
    }
  }
  
  /**
   * Get the next pending item from the queue
   */
  private async getNextEnAttenteItem(): Promise<ExtractionQueueItem | null> {
    try {
      const { data, error } = await this.supabase
        .from('extraction_queue')
        .select('*')
        .eq('status', 'En_attente')
        .order('created_at', { ascending: true })
        .limit(1)
        .single();
      
      if (error) throw error;
      return data as ExtractionQueueItem;
    } catch (error) {
      if (error.code === 'PGRST116') {
        // No rows returned
        return null;
      }
      console.error("Error fetching next pending item:", error);
      throw error;
    }
  }
  
  /**
   * Update an item's status in the queue
   */
  private async updateItemStatus(itemId: string, updates: Partial<ExtractionQueueItem>): Promise<void> {
    try {
      const { error } = await this.supabase
        .from('extraction_queue')
        .update(updates)
        .eq('id', itemId);
      
      if (error) throw error;
    } catch (error) {
      console.error(`Error updating status for item ${itemId}:`, error);
      throw error;
    }
  }
  
  /**
   * Extract an acte document
   * @param page The Playwright page
   * @param inscriptionNumber The inscription number to search for
   * @param downloadDir The directory to save the downloaded file
   * @param circonscription Optional circonscription
   * @param acteType Optional acte type
   */
  private async extractActeDocument(
    page: Page,
    inscriptionNumber: string,
    downloadDir: string,
    circonscription?: string,
    acteType?: string
  ): Promise<string> {
    try {
      const finalActeType = acteType || 'Acte';
      
      // Navigate to the acte search page
      const page1Promise = page.waitForEvent('popup');
      await page.getByRole('link', { name: 'Consulter', exact: true }).click();
      const page1 = await page1Promise;
      await page1.getByRole('link', { name: 'Index des immeubles' }).click();

      const page2Promise = page.waitForEvent('popup');
      await page.getByRole('link', { name: 'Consulter', exact: true }).click();
      const page2 = await page2Promise;

      await page2.getByRole('link', { name: 'Acte au long, radiation, avis' }).click();
      
      await page.waitForTimeout(1000);
      
      // Handle circonscription selection if provided
      if (circonscription) {
        const selectElement = page.locator('#selCircnFoncr');
        await selectElement.selectOption({ label: circonscription });
      }
      
      // Handle acte type selection
      switch (finalActeType) {
        case 'Radiation':
          await page.locator('#radRdrtn').click();
          break;
        case 'Avis d\'adresse':
          await page.locator('#radAvis').click();
          break;
        case 'Acte divers':
          await page.locator('#radAct').click();
          break;
        case 'Acte':
        default:
          await page.locator('#radAct').click();
          break;
      }
      
      // Enter the inscription number
      await page.getByRole('textbox', { name: 'Numéro d\'inscription' }).click();
      await page.getByRole('textbox', { name: 'Numéro d\'inscription' }).fill(inscriptionNumber);
      await page.waitForTimeout(1000);
      
      // Note: We're already using the global download path set at browser launch time
      console.log(`Using download directory: ${downloadDir}`);
      
      // Search and download
      await page.getByRole('button', { name: 'Rechercher' }).click();
      
      // Wait for search results to load, then click the print button
      await page.locator('frame[name="page"]').contentFrame().locator('frame[name="frmNavgt"]').contentFrame().getByRole('link', { name: 'Imprimer' }).click();
      
      // Wait for download to complete
      await page.waitForTimeout(10000);
      
      // Check if a file was downloaded
      const files = await readdir(downloadDir);
      
      if (files.length === 0) {
        throw new Error("No document was downloaded");
      }
      
      // Rename the file
      const sourcePath = path.join(downloadDir, files[0]);
      const targetPath = path.join(downloadDir, `${inscriptionNumber}.pdf`);
      await rename(sourcePath, targetPath);
      
      return targetPath;
    } catch (error) {
      console.error("Error extracting acte document:", error);
      throw error;
    }
  }
  
  /**
   * Extract an index document
   * @param page The Playwright page
   * @param lotNumber The lot number to search for
   * @param downloadDir The directory to save the downloaded file
   * @param circonscription Optional circonscription
   * @param cadastre Optional cadastre
   * @param designationSecondaire Optional designation secondaire
   */
  private async extractIndexDocument(
    page: Page,
    lotNumber: string,
    downloadDir: string,
    circonscription?: string,
    cadastre?: string,
    designationSecondaire?: string
  ): Promise<string> {
    try {
      // Navigate to the index search page
      const page1Promise = page.waitForEvent('popup');
      await page.getByRole('link', { name: 'Consulter', exact: true }).click();
      const page1 = await page1Promise;
      await page1.getByRole('link', { name: 'Index des immeubles' }).click();
      
      // Handle circonscription selection if provided
      if (circonscription) {
        const selectElement = page.locator('#selCircnFoncr');
        await selectElement.selectOption({ label: circonscription });
      }
      
      // Fill in cadastre if provided
      if (cadastre) {
        await page.getByLabel('Cadastre').selectOption({ label: cadastre });
      }
      
      // Fill in designation secondaire if provided
      if (designationSecondaire) {
        await page.getByLabel('Désignation secondaire').fill(designationSecondaire);
      }
      
      // Enter the lot number
      await page.getByRole('textbox', { name: 'Numéro de lot' }).click();
      await page.getByRole('textbox', { name: 'Numéro de lot' }).fill(lotNumber);
      
      // Note: We're already using the global download path set at browser launch time
      console.log(`Using download directory: ${downloadDir}`);
      
      // Search and download
      await page.getByRole('button', { name: 'Soumettre' }).click();
      await page.locator('frame[name="page"]').contentFrame().locator('frame[name="frmNavgt"]').contentFrame().getByRole('link', { name: 'Imprimer' }).click();
      
      // Wait for download to complete
      await page.waitForTimeout(10000);
      
      // Check if a file was downloaded
      const files = await readdir(downloadDir);
      
      if (files.length === 0) {
        throw new Error("No document was downloaded");
      }
      
      // Rename the file
      const sourcePath = path.join(downloadDir, files[0]);
      const targetPath = path.join(downloadDir, `${lotNumber}.pdf`);
      await rename(sourcePath, targetPath);
      
      return targetPath;
    } catch (error) {
      console.error("Error extracting index document:", error);
      throw error;
    }
  }
}

// Run the processor
const processor = new ExtractionBatchProcessor();
processor.run()
  .then(() => {
    console.log("Batch process completed successfully");
    process.exit(0);
  })
  .catch((error) => {
    console.error("Batch process failed:", error);
    process.exit(1);
  });
