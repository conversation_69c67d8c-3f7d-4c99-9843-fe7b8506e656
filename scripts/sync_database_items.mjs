// Script to synchronize database items with the extraction queue
import { createClient } from '@supabase/supabase-js';

// These should be environment variables in production
const SUPABASE_URL = "https://sqzqvxqcybghcgrpubsy.supabase.co";
const SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InNxenF2eHFjeWJnaGNncnB1YnN5Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0MTA5NTUxOCwiZXhwIjoyMDU2NjcxNTE4fQ.YN8lGh9PurkS8dq7a7zzlrgoETGyWltcJrLnOBI7t6M";

// Initialize Supabase client
const supabase = createClient(SUPABASE_URL, SUPABASE_KEY);

/**
 * Sync actes from the database to the extraction queue
 */
async function syncActesForExtraction(limit = 10) {
  try {
    console.log("Syncing actes for extraction...");
    
    // Get actes that need to be downloaded but aren't in the queue yet
    // (no doc_url and not already in queue)
    const { data: actes, error } = await supabase
      .from('actes')
      .select('id, acte_publication_number, circonscription_fonciere')
      .is('doc_url', null)
      .order('created_at', { ascending: true })
      .limit(limit);
    
    if (error) throw error;
    
    if (!actes || actes.length === 0) {
      console.log("No actes found that require extraction");
      return [];
    }
    
    console.log(`Found ${actes.length} actes that require extraction`);
    
    // For each acte, check if it's already in the queue
    const addedItems = [];
    
    for (const acte of actes) {
      // Check if already in queue
      const { data: existing, error: checkError } = await supabase
        .from('extraction_queue')
        .select('id')
        .eq('document_source', 'acte')
        .eq('document_number', acte.acte_publication_number)
        .maybeSingle();
      
      if (checkError) throw checkError;
      
      if (existing) {
        console.log(`Acte ${acte.acte_publication_number} already in extraction queue`);
        continue;
      }
      
      // Add to queue
      const { data: queueItem, error: insertError } = await supabase
        .from('extraction_queue')
        .insert({
          document_source: 'acte',
          document_number: acte.acte_publication_number,
          circonscription_fonciere: acte.circonscription_fonciere,
          acte_type: 'Acte',
          status: 'En_attente',
          acte_id: acte.id
        })
        .select()
        .single();
      
      if (insertError) throw insertError;
      
      console.log(`Added acte ${acte.acte_publication_number} to extraction queue with ID: ${queueItem.id}`);
      addedItems.push(queueItem);
    }
    
    return addedItems;
  } catch (error) {
    console.error("Error syncing actes for extraction:", error);
    throw error;
  }
}

/**
 * Sync indices from the database to the extraction queue
 */
async function syncIndicesForExtraction(limit = 10) {
  try {
    console.log("Syncing indices for extraction...");
    
    // Get indices that need to be downloaded but aren't in the queue yet
    const { data: indices, error } = await supabase
      .from('index')
      .select('id, lot_number, circonscription, cadastre, designation_secondaire')
      .is('doc_url', null)
      .order('created_at', { ascending: true })
      .limit(limit);
    
    if (error) throw error;
    
    if (!indices || indices.length === 0) {
      console.log("No indices found that require extraction");
      return [];
    }
    
    console.log(`Found ${indices.length} indices that require extraction`);
    
    // For each index, check if it's already in the queue
    const addedItems = [];
    
    for (const index of indices) {
      // Check if already in queue
      const { data: existing, error: checkError } = await supabase
        .from('extraction_queue')
        .select('id')
        .eq('document_source', 'index')
        .eq('document_number', index.lot_number)
        .maybeSingle();
      
      if (checkError) throw checkError;
      
      if (existing) {
        console.log(`Index ${index.lot_number} already in extraction queue`);
        continue;
      }
      
      // Add to queue
      const { data: queueItem, error: insertError } = await supabase
        .from('extraction_queue')
        .insert({
          document_source: 'index',
          document_number: index.lot_number,
          circonscription_fonciere: index.circonscription,
          cadastre: index.cadastre,
          designation_secondaire: index.designation_secondaire,
          status: 'En_attente',
          index_id: index.id
        })
        .select()
        .single();
      
      if (insertError) throw insertError;
      
      console.log(`Added index ${index.lot_number} to extraction queue with ID: ${queueItem.id}`);
      addedItems.push(queueItem);
    }
    
    return addedItems;
  } catch (error) {
    console.error("Error syncing indices for extraction:", error);
    throw error;
  }
}

/**
 * Update main tables when documents are downloaded
 */
async function updateMainTables() {
  try {
    console.log("Updating main tables with downloaded documents...");
    
    // Get queue items that have been downloaded but not yet updated in main tables
    const { data: downloaded, error } = await supabase
      .from('extraction_queue')
      .select('*')
      .eq('status', 'Telecharge')
      .not('local_file_path', 'is', null);
    
    if (error) throw error;
    
    if (!downloaded || downloaded.length === 0) {
      console.log("No downloaded documents found that need updating");
      return [];
    }
    
    console.log(`Found ${downloaded.length} downloaded documents to update in main tables`);
    
    const updated = [];
    
    for (const item of downloaded) {
      // Update the corresponding table based on document_source
      if (item.document_source === 'acte' && item.acte_id) {
        const { data, error: updateError } = await supabase
          .from('actes')
          .update({
            doc_status: 'Telecharge',
            local_file_path: item.local_file_path,
            updated_at: new Date().toISOString()
          })
          .eq('id', item.acte_id)
          .select()
          .single();
        
        if (updateError) throw updateError;
        
        console.log(`Updated acte ${item.document_number} with downloaded document`);
        updated.push(data);
      } 
      else if (item.document_source === 'index' && item.index_id) {
        const { data, error: updateError } = await supabase
          .from('index')
          .update({
            doc_status: 'Telecharge',
            local_file_path: item.local_file_path,
            updated_at: new Date().toISOString()
          })
          .eq('id', item.index_id)
          .select()
          .single();
        
        if (updateError) throw updateError;
        
        console.log(`Updated index ${item.document_number} with downloaded document`);
        updated.push(data);
      }
      
      // Update the queue item to maintain the Downloaded status
      // This ensures consistency with the status set by process_extraction_batch.mjs
      await supabase
        .from('extraction_queue')
        .update({
          status: 'Telecharge', // Keep as Telecharge until actually uploaded to drive
          updated_at: new Date().toISOString()
        })
        .eq('id', item.id);
    }
    
    return updated;
  } catch (error) {
    console.error("Error updating main tables:", error);
    throw error;
  }
}

// Run sync based on command line arguments
async function main() {
  const args = process.argv.slice(2);
  const command = args[0]?.toLowerCase();
  const limit = parseInt(args[1], 10) || 10;
  
  try {
    switch (command) {
      case 'actes':
        await syncActesForExtraction(limit);
        break;
        
      case 'indices':
        await syncIndicesForExtraction(limit);
        break;
        
      case 'update':
        await updateMainTables();
        break;
        
      case 'all':
        await syncActesForExtraction(limit);
        await syncIndicesForExtraction(limit);
        await updateMainTables();
        break;
        
      default:
        console.log("Available commands:");
        console.log("- actes [limit]: Sync actes that need extraction");
        console.log("- indices [limit]: Sync indices that need extraction");
        console.log("- update: Update main tables with downloaded documents");
        console.log("- all [limit]: Run all sync operations");
        break;
    }
  } catch (error) {
    console.error("Command failed:", error);
    process.exit(1);
  }
  
  process.exit(0);
}

// Run the script
main();
