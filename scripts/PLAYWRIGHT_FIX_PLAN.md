# Playwright Script Issues - Complete Assessment & Fix Plan

## Current Issues Assessment

### 1. Navigation Failure After Search
**Problem**: The scripts fail with error messages like:
- "Search failed: URL did not change after clicking 'Rechercher'." (for actes)
- "Search failed: URL did not change after clicking 'Soumettre'." (for index)

**Root Cause**: The script doesn't properly wait for navigation after clicking search buttons. The page might:
- Load content dynamically without changing URL
- Display results in frames/iframes
- Show errors or validation messages
- Require additional time for server response

### 2. Database Status Update Issue
**Problem**: The script tries to write invalid status values to extraction_queue table.

**Valid Status Values** (from extraction_status_enum):
- `En_attente`
- `En_traitement` 
- `Telecharge`
- `Disponible_sur_Drive`
- `Document_introuvable`

**Issue**: The script likely tries to use 'error' or other invalid status values.

## Fix Implementation Plan

### Phase 1: Fix Navigation & Wait Logic

1. **Add Proper Wait Conditions**:
   - Wait for network idle after clicking search
   - Wait for specific DOM elements that indicate search results
   - Add error detection for invalid searches
   - Handle cases where no results are found

2. **Improve Frame Handling**:
   - Wait for frames to be available before accessing
   - Add retry logic for frame access
   - Handle frame navigation separately

3. **Add Search Result Validation**:
   - Check for error messages on the page
   - Validate that results are loaded
   - Handle "no results found" scenarios

### Phase 2: Fix Database Status Updates

1. **Correct Status Mapping**:
   - Use `Document_introuvable` instead of 'error'
   - Ensure all status updates use valid enum values
   - Add status validation before database updates

2. **Error Handling**:
   - Store detailed error messages in `error_message` field
   - Use proper retry count tracking
   - Update status to `En_attente` for retryable errors
   - Update status to `Document_introuvable` after max retries

### Phase 3: Add Robust Error Recovery

1. **Session Management**:
   - Check login status before each extraction
   - Re-login if session expired
   - Handle popup blockers and navigation issues

2. **Download Verification**:
   - Verify file actually downloaded
   - Check file size and validity
   - Handle partial downloads

3. **Logging & Monitoring**:
   - Add detailed logging for each step
   - Screenshot on errors for debugging
   - Track timing for performance analysis

## Specific Code Fixes Needed

### 1. In `extractActeDocument` method:
```typescript
// After clicking search button
await page.getByRole('button', { name: 'Rechercher' }).click();

// Add proper wait logic
await page.waitForLoadState('networkidle');
await page.waitForTimeout(2000); // Give time for dynamic content

// Check for errors first
const errorElement = await page.locator('.error-message').isVisible();
if (errorElement) {
  const errorText = await page.locator('.error-message').textContent();
  throw new Error(`Search error: ${errorText}`);
}

// Wait for frames to be available
const pageFrame = await page.waitForSelector('frame[name="page"]');
const contentFrame = await pageFrame.contentFrame();
const navFrame = await contentFrame.waitForSelector('frame[name="frmNavgt"]');
const navContentFrame = await navFrame.contentFrame();

// Wait for print button to be available
await navContentFrame.waitForSelector('a:has-text("Imprimer")', { state: 'visible' });
```

### 2. In `extractIndexDocument` method:
```typescript
// Similar pattern for index documents
await page.getByRole('button', { name: 'Soumettre' }).click();
await page.waitForLoadState('networkidle');
// ... similar error checking and frame waiting logic
```

### 3. In error handling:
```typescript
catch (error) {
  const newRetryCount = (queueItem.retry_count || 0) + 1;
  
  if (newRetryCount >= 3) {
    await this.updateItemStatus(queueItem.id, {
      retry_count: newRetryCount,
      error_message: error.message,
      status: 'Document_introuvable' // Use valid enum value
    });
  } else {
    await this.updateItemStatus(queueItem.id, {
      retry_count: newRetryCount,
      error_message: error.message,
      status: 'En_attente' // Keep valid status for retry
    });
  }
}
```

## Testing Strategy

1. **Unit Tests**:
   - Test status enum validation
   - Test error message handling
   - Test retry logic

2. **Integration Tests**:
   - Test full extraction flow
   - Test error scenarios
   - Test database updates

3. **Manual Testing**:
   - Run with headed browser to observe behavior
   - Test with known good and bad document numbers
   - Verify database state after runs

## Implementation Priority

1. **Immediate** (Fix breaking issues):
   - Fix status enum values in database updates
   - Add proper wait logic after search clicks

2. **High** (Improve reliability):
   - Add comprehensive error detection
   - Improve frame handling
   - Add download verification

3. **Medium** (Enhance maintainability):
   - Add logging and monitoring
   - Implement session management
   - Add configuration for timeouts

## Success Metrics

- No more "URL did not change" errors
- All database updates use valid status values
- Successful extraction rate > 95% for valid documents
- Clear error messages for invalid documents
- Proper retry handling with exponential backoff
